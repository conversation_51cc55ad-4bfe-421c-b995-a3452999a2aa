package usecases_test

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// Fake implementations are in test_helpers_test.go

func TestNewDesignPackageRetriever(t *testing.T) {
	t.Run("should create retriever with valid repository", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		retriever := usecases.NewDesignPackageRetriever(repo)
		assert.NotNil(t, retriever)
	})

	t.Run("should panic with nil repository", func(t *testing.T) {
		assert.Panics(t, func() {
			usecases.NewDesignPackageRetriever(nil)
		})
	})
}

func TestDesignPackageRetriever_RetrieveDesignPackage(t *testing.T) {
	ctx := context.Background()
	designPkgID := uuid.New()
	testDesignPkg := usecases.DesignPackage{
		ID:   designPkgID,
		Name: "Test Design Package",
		Tagged: usecases.Tagged{
			ColorScheme: usecases.Neutral,
			Style:       usecases.Modern,
		},
	}

	t.Run("should retrieve design package successfully", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		presenter := NewFakeDesignPackagesPresenter()
		retriever := usecases.NewDesignPackageRetriever(repo)

		// First, save the design package to the repository
		_, err := repo.InsertDesignPackage(ctx, testDesignPkg, "")
		require.NoError(t, err)

		retriever.RetrieveDesignPackage(ctx, presenter, designPkgID)

		// Verify the design package was presented
		AssertPresentDesignPackageCalled(t, presenter, testDesignPkg)
		AssertPresentErrorNotCalled(t, presenter)
	})

	t.Run("should present error when repository fails", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		presenter := NewFakeDesignPackagesPresenter()
		retriever := usecases.NewDesignPackageRetriever(repo)

		// Don't save any design package to the repository
		nonExistentId := uuid.New()
		retriever.RetrieveDesignPackage(ctx, presenter, nonExistentId)

		// Verify error was presented
		assert.NotEmpty(t, presenter.PresentErrorCalls, "Expected PresentError to be called")
		assert.Empty(t, presenter.PresentDesignPackageCalls, "Expected PresentDesignPackage not to be called")
	})
}

func TestDesignPackageRetriever_RetrieveAllDesignPackages(t *testing.T) {
	ctx := context.Background()
	testDesignPkgs := []usecases.DesignPackage{
		{
			ID:   uuid.New(),
			Name: "Design package 1",
			Tagged: usecases.Tagged{
				ColorScheme: usecases.Neutral,
				Style:       usecases.Modern,
			},
		},
		{
			ID:   uuid.New(),
			Name: "Design Package 2",
			Tagged: usecases.Tagged{
				ColorScheme: usecases.Bold,
				Style:       usecases.Traditional,
			},
		},
	}

	t.Run("should retrieve all design packages successfully", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		presenter := NewFakeDesignPackagesPresenter()
		retriever := usecases.NewDesignPackageRetriever(repo)

		// First, save the design packages to the repository
		for _, designPkg := range testDesignPkgs {
			_, err := repo.InsertDesignPackage(ctx, designPkg, "")
			require.NoError(t, err)
		}

		retriever.RetrieveAllDesignPackages(ctx, presenter)

		// Verify the design packages were presented
		assert.NotEmpty(t, presenter.PresentDesignPackagesCalls, "Expected PresentDesignPackages to be called")
		if len(presenter.PresentDesignPackagesCalls) > 0 {
			lastCall := presenter.PresentDesignPackagesCalls[len(presenter.PresentDesignPackagesCalls)-1]
			assert.Len(t, lastCall.DesignPkgs, len(testDesignPkgs),
				"Expected %d design packages to be presented", len(testDesignPkgs))
		}
		AssertPresentErrorNotCalled(t, presenter)
	})

	t.Run("should present error when repository fails", func(t *testing.T) {
		// For integration testing with FakeRelDb, we test normal operation
		// since FakeRelDb doesn't typically fail
		repo := gateways.NewFakeRelDb()
		presenter := NewFakeDesignPackagesPresenter()
		retriever := usecases.NewDesignPackageRetriever(repo)

		// Don't save any design packages to the repository
		retriever.RetrieveAllDesignPackages(ctx, presenter)

		// Verify empty list was presented (not an error)
		assert.NotEmpty(t, presenter.PresentDesignPackagesCalls, "Expected PresentDesignPackages to be called")
		if len(presenter.PresentDesignPackagesCalls) > 0 {
			lastCall := presenter.PresentDesignPackagesCalls[len(presenter.PresentDesignPackagesCalls)-1]
			assert.Empty(t, lastCall.DesignPkgs, "Expected empty design packages list")
		}
		AssertPresentErrorNotCalled(t, presenter)
	})
}
