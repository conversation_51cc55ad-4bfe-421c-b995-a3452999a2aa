package usecases

import (
	"context"
	"log/slog"

	"github.com/google/uuid"
)

// CartInclusionReplacer handles business logic for replacing cart inclusions
type CartInclusionReplacer struct {
	cartInclusionRepository cartInclusionRepository
	logger                  *slog.Logger
}

// NewCartInclusionReplacer creates a new CartInclusionReplacer
func NewCartInclusionReplacer(cartInclusionRepository cartInclusionRepository, logger *slog.Logger) *CartInclusionReplacer {
	if IsNil(cartInclusionRepository) {
		panic("cartInclusionRepository cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &CartInclusionReplacer{cartInclusionRepository: cartInclusionRepository, logger: logger}
}

// ReplaceCartInclusions performs a bulk replacement update of cart inclusions for a design.
// This replaces all existing cart inclusions with the provided ones.
func (s *CartInclusionReplacer) ReplaceCartInclusions(ctx context.Context, presenter OutcomePresenter, designId uuid.UUID, inclusions CartInclusions) {
	s.logger.DebugContext(ctx, "Starting bulk cart inclusions replacement update operation",
		slog.String("operation", "replace_cart_inclusions"),
		slog.String("designId", designId.String()),
		slog.Int("inclusionCount", len(inclusions)))

	// Validate all cart inclusions before making any changes
	if err := ValidateAndLogCartInclusions(ctx, s.logger, "replace_cart_inclusions", designId, inclusions); err != nil {
		presenter.PresentError(err)
		return
	}

	// Replace all cart inclusions (full replacement handled atomically at DB level)
	if err := s.cartInclusionRepository.ReplaceCartInclusionsForDesign(ctx, designId, inclusions); err != nil {
		s.logger.ErrorContext(ctx, "Failed to replace cart inclusions",
			slog.String("operation", "replace_cart_inclusions"),
			slog.String("designId", designId.String()),
			slog.String("error", err.Error()))
		presenter.PresentError(err)
		return
	}

	s.logger.DebugContext(ctx, "Successfully replaced cart inclusions",
		slog.String("operation", "replace_cart_inclusions"),
		slog.String("designId", designId.String()),
		slog.Int("finalInclusionCount", len(inclusions)),
		slog.String("status", "success"))
	presenter.ConveySuccess()
}
