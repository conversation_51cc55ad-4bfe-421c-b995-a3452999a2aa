package usecases

import (
	"context"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

type genAI interface {
	GenerateDesignTitleAndDescription(ctx context.Context, productDescriptions map[string]string) (string, string, error)
}

type designRepositoryReplica interface {
	ReadDesign(ctx context.Context, designId uuid.UUID) (Design, error)
	DesignsForProject(ctx context.Context, projectId entities.ProjectId) ([]Design, error)
	DesignsByProject(ctx context.Context, projectIDs []entities.ProjectId) (map[entities.ProjectId][]Design, []error, error)
	ProjectIdForDesign(ctx context.Context, designId uuid.UUID) (entities.ProjectId, error)
}

type designRepository interface {
	designRepositoryReplica
	UpsertDesign(ctx context.Context, design Design) (uuid.UUID, error)
	DeleteDesign(ctx context.Context, designId uuid.UUID) error
	DeleteDesignsForProjectExceptSpecified(ctx context.Context, projectId entities.ProjectId, designIds []uuid.UUID) error
	MarkRenditionsOutdatedForDesign(ctx context.Context, designId uuid.UUID) error
}

type presetRepositoryReplica interface {
	FindPresetByLegacyId(ctx context.Context, designPkgID string) (Preset, error)
}

type presetRepository interface {
	presetRepositoryReplica
	InsertPreset(ctx context.Context, preset Preset) error
}

type designPackageRepositoryReplica interface {
	ReadDesignPackage(ctx context.Context, designPkgID uuid.UUID) (DesignPackage, error)
	ReadDesignPackageByLegacyId(ctx context.Context, legacyId string) (DesignPackage, error)
	ReadAllDesignPackages(ctx context.Context) ([]DesignPackage, error)
	DesignPackagesById(ctx context.Context, designPkgIDs []uuid.UUID) ([]DesignPackage, error)
}

type designPackageRepository interface {
	designPackageRepositoryReplica
	InsertDesignPackage(ctx context.Context, designPkg DesignPackage, legacyId string) (uuid.UUID, error)
}

type renditionRepository interface {
	InsertRendition(ctx context.Context, designId uuid.UUID, rendition entities.Rendition) (uuid.UUID, error)
	Renditions(ctx context.Context, ids []uuid.UUID) ([]entities.Rendition, error)
	RenditionsForDesign(ctx context.Context, designId uuid.UUID) ([]entities.Rendition, error)
	UpdateRendition(ctx context.Context, rendition entities.RenditionDiff) error
	DeleteRendition(ctx context.Context, id uuid.UUID) error
	MarkAllCompletedRenditionsOutdatedForProject(ctx context.Context, projectId entities.ProjectId) error
}

type cartInclusionRepositoryReplica interface {
	CartInclusionsForDesign(ctx context.Context, designId uuid.UUID) (CartInclusions, error)
}

type cartInclusionRepository interface {
	cartInclusionRepositoryReplica
	UpsertCartInclusion(ctx context.Context, designId uuid.UUID, inclusion CartInclusion) error
	UpsertCartInclusionsForDesign(ctx context.Context, designId uuid.UUID, inclusions CartInclusions) error
	ReplaceCartInclusionsForDesign(ctx context.Context, designId uuid.UUID, inclusions CartInclusions) error
	DeleteCartInclusion(ctx context.Context, designId uuid.UUID, productId uuid.UUID, location Location) error
	DeleteCartInclusionsForDesign(ctx context.Context, designId uuid.UUID) error
}

type Presenter interface {
	PresentError(err error)
}

type DataPresenter interface {
	Presenter
	PresentData(ctx context.Context, data any)
}

type DesignPresenter interface {
	DataPresenter
	PresentDesign(ctx context.Context, design Design)
}
type DesignsPresenter interface {
	DesignPresenter
	PresentDesigns(ctx context.Context, designs []Design)
	PresentDesignsByProject(ctx context.Context, data map[entities.ProjectId][]Design, errors []error)
	SetLegacyIdMapping(legacyIdMapping map[uuid.UUID]string)
}

type EventPresenter interface {
	SendEvent(ctx context.Context, eventName string, data any) error
	Close(ctx context.Context)
}

type PresetPresenter interface {
	Presenter
	PresentPreset(ctx context.Context, preset Preset)
}

type RenditionsPresenter interface {
	Presenter
	PresentRenditions(ctx context.Context, renditions []entities.Rendition)
}

type CartInclusionsPresenter interface {
	Presenter
	PresentCartInclusions(ctx context.Context, inclusions CartInclusions)
}

type RenditionCreationOutcomePresenter interface {
	Presenter
	ConveySuccessWithResource(rendition entities.Rendition)
}
type DesignPackagesPresenter interface {
	Presenter
	PresentDesignPackage(ctx context.Context, designPkg DesignPackage)
	PresentDesignPackages(ctx context.Context, designPkgs []DesignPackage)
}

type OutcomePresenter interface {
	Presenter
	ConveySuccess()
}

type DesignMutationOutcomePresenter interface {
	OutcomePresenter
	ConveySuccessWithResource(design Design, status Status)
}

type PresetCreationOutcomePresenter interface {
	Presenter
	ConveySuccessWithNewResource(preset Preset)
}

type DesignPackageCreationOutcomePresenter interface {
	Presenter
	ConveySuccessWithNewResource(designPkg DesignPackage)
}

type ProjectIdPresenter interface {
	Presenter
	PresentProjectId(ctx context.Context, projectId entities.ProjectId)
}

type monolith interface {
	UpdateCurrentDesignIdForProject(ctx context.Context, projectId entities.ProjectId, designId uuid.UUID) error
	GetLayoutForProject(ctx context.Context, projectId entities.ProjectId) (entities.RoomLayout, error)
}

type catalog interface {
	ProductInfo(ctx context.Context, category string, productId uuid.UUID) (RenderableProduct, error)
	ProductDescriptionsForDesign(ctx context.Context, design Design) (map[string]string, error)
}

type productSearch interface {
	FindProducts(ctx context.Context, urlQueryParams string) ([]uuid.UUID, error)
	FindProductsViaAI(ctx context.Context, filters ProductSearchFilters) ([]uuid.UUID, error)
}

type rooms interface {
	DefaultRoomLayout() entities.RoomLayout
}
