package usecases_test

import (
	"context"
	"errors"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// Fake implementations are in test_helpers_test.go

// FailingDesignPackageRepository wraps FakeRelDb and can simulate failures for design package operations
type FailingDesignPackageRepository struct {
	*gateways.FakeRelDb
	ShouldFail bool
}

func (r *FailingDesignPackageRepository) InsertDesignPackage(ctx context.Context,
	designPkg usecases.DesignPackage, legacyId string) (uuid.UUID, error) {

	if r.ShouldFail {
		return uuid.UUID{}, errors.New("database constraint violation")
	}
	return r.FakeRelDb.InsertDesignPackage(ctx, designPkg, legacyId)
}

func (r *FailingDesignPackageRepository) DesignPackagesById(ctx context.Context, designPkgIDs []uuid.UUID) ([]usecases.DesignPackage, error) {
	if r.ShouldFail {
		return nil, errors.New("database read failure")
	}
	return r.FakeRelDb.DesignPackagesById(ctx, designPkgIDs)
}

func TestNewDesignPackageCreater(t *testing.T) {
	t.Run("should create creater with valid repository", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		creater := usecases.NewDesignPackageCreator(repo, nil)
		assert.NotNil(t, creater)
	})

	t.Run("should panic with nil repository", func(t *testing.T) {
		assert.Panics(t, func() {
			usecases.NewDesignPackageCreator(nil, nil)
		})
	})
}

func TestDesignPackageCreater_CreateDesignPackage(t *testing.T) {
	ctx := context.Background()

	testDesignPkg := usecases.DesignPackage{
		Tagged: usecases.Tagged{
			ColorScheme: usecases.Neutral,
			Style:       usecases.Modern,
		},
		Name:            "Test Design Package",
		Description:     "A test design package",
		Inspiration:     "Modern design",
		Atmosphere:      []string{"calm", "modern"},
		ColorPalette:    []string{"white", "gray"},
		MaterialPalette: []string{"marble", "wood"},
		FixedProductSelections: usecases.FixedProductSelections{
			FloorTile: &[]uuid.UUID{uuid.New()}[0],
			Lighting:  &[]uuid.UUID{uuid.New()}[0],
			Mirror:    &[]uuid.UUID{uuid.New()}[0],
			Paint:     &[]uuid.UUID{uuid.New()}[0],
			Shelving:  &[]uuid.UUID{uuid.New()}[0],
			Toilet:    &[]uuid.UUID{uuid.New()}[0],
		},
		ProductSelectionOptions: usecases.ProductSelectionOptions{
			AlcoveTub:          uuid.New(),
			FreestandingTub:    uuid.New(),
			ShowerGlassFixed:   uuid.New(),
			ShowerGlassSliding: uuid.New(),
			ShowerSystemCombo:  uuid.New(),
			ShowerSystemSolo:   uuid.New(),
			TubDoorFixed:       uuid.New(),
			TubDoorSliding:     uuid.New(),
		},
		WallTilePlacement:  usecases.HalfWall,
		WallpaperPlacement: usecases.VanityWall,
	}

	t.Run("should create design package successfully", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		presenter := &FakeDesignPackageCreationOutcomePresenter{}
		creater := usecases.NewDesignPackageCreator(repo, nil)

		creater.CreateDesignPackage(ctx, presenter, testDesignPkg, "00")

		// Verify design package was created successfully
		assert.Len(t, presenter.ConveySuccessWithNewResourceCalls, 1, "Should have called ConveySuccessWithNewResource once")
		assert.Empty(t, presenter.PresentErrorCalls, "Should not have presented any errors")

		createdDesignPkg := presenter.ConveySuccessWithNewResourceCalls[0]
		assert.Equal(t, testDesignPkg.Name, createdDesignPkg.Name)
		assert.Equal(t, testDesignPkg.Description, createdDesignPkg.Description)
		assert.NotEqual(t, uuid.UUID{}, createdDesignPkg.ID, "Created design package should have a valid ID")
	})

	t.Run("should present error when repository fails", func(t *testing.T) {
		// Create a failing repository
		repo := &FailingDesignPackageRepository{
			FakeRelDb:  gateways.NewFakeRelDb(),
			ShouldFail: true,
		}
		presenter := &FakeDesignPackageCreationOutcomePresenter{}
		creater := usecases.NewDesignPackageCreator(repo, nil)

		creater.CreateDesignPackage(ctx, presenter, testDesignPkg, "00")

		// Verify error was presented
		assert.Len(t, presenter.PresentErrorCalls, 1, "Should have presented an error")
		assert.Empty(t, presenter.ConveySuccessWithNewResourceCalls, "Should not have called ConveySuccessWithNewResource")
		assert.Contains(t, presenter.PresentErrorCalls[0].Error(),
			"database constraint violation", "Error should contain expected message")
	})

	t.Run("should present error when design package name is empty", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		presenter := &FakeDesignPackageCreationOutcomePresenter{}
		creater := usecases.NewDesignPackageCreator(repo, nil)

		emptyNameDesignPkg := testDesignPkg
		emptyNameDesignPkg.Name = ""

		creater.CreateDesignPackage(ctx, presenter, emptyNameDesignPkg, "00")

		// Verify error was presented and success was not called
		assert.Len(t, presenter.PresentErrorCalls, 1, "Should have presented an error")
		assert.Empty(t, presenter.ConveySuccessWithNewResourceCalls, "Should not have called ConveySuccessWithNewResource")
		assert.Equal(t, usecases.ErrInvalidPayload, presenter.PresentErrorCalls[0], "Should present invalid payload error")
	})

	t.Run("should handle design package with vanity scaling options", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		presenter := &FakeDesignPackageCreationOutcomePresenter{}
		creater := usecases.NewDesignPackageCreator(repo, nil)

		designPkgWithVanityOptions := testDesignPkg
		designPkgWithVanityOptions.VanityScalingOptions = map[int]usecases.VanityScalingOption{
			36: {
				VanityProductID: uuid.New(),
				FaucetProductID: uuid.New(),
			},
			48: {
				VanityProductID: uuid.New(),
				FaucetProductID: uuid.New(),
			},
		}

		creater.CreateDesignPackage(ctx, presenter, designPkgWithVanityOptions, "00")

		// Verify design package was created successfully
		assert.Len(t, presenter.ConveySuccessWithNewResourceCalls, 1, "Should have called ConveySuccessWithNewResource once")
		assert.Empty(t, presenter.PresentErrorCalls, "Should not have presented any errors")

		createdDesignPkg := presenter.ConveySuccessWithNewResourceCalls[0]
		assert.Equal(t, designPkgWithVanityOptions.Name, createdDesignPkg.Name)
		assert.Equal(t, designPkgWithVanityOptions.VanityScalingOptions, createdDesignPkg.VanityScalingOptions)
		assert.NotEqual(t, uuid.UUID{}, createdDesignPkg.ID, "Created design package should have a valid ID")
	})

	t.Run("should handle design package with provenance data", func(t *testing.T) {
		repo := gateways.NewFakeRelDb()
		presenter := &FakeDesignPackageCreationOutcomePresenter{}
		creater := usecases.NewDesignPackageCreator(repo, nil)

		designPkgWithProvenance := testDesignPkg
		designPkgWithProvenance.DesignPackageProvenance = usecases.DesignPackageProvenance{
			LightingBrand: &[]string{"Test Lighting Brand"}[0],
			PlumbingBrand: &[]string{"Test Plumbing Brand"}[0],
			ToiletBrand:   &[]string{"Test Toilet Brand"}[0],
			VanityBrand:   &[]string{"Test Vanity Brand"}[0],
			VanityStorage: &[]string{"Test Storage"}[0],
		}

		creater.CreateDesignPackage(ctx, presenter, designPkgWithProvenance, "00")

		// Verify design package was created successfully
		assert.Len(t, presenter.ConveySuccessWithNewResourceCalls, 1, "Should have called ConveySuccessWithNewResource once")
		assert.Empty(t, presenter.PresentErrorCalls, "Should not have presented any errors")

		createdDesignPkg := presenter.ConveySuccessWithNewResourceCalls[0]
		assert.Equal(t, designPkgWithProvenance.Name, createdDesignPkg.Name)
		assert.Equal(t, designPkgWithProvenance.DesignPackageProvenance, createdDesignPkg.DesignPackageProvenance)
		assert.NotEqual(t, uuid.UUID{}, createdDesignPkg.ID, "Created design package should have a valid ID")
	})
}
