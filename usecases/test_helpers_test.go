package usecases_test

import (
	"context"
	"errors"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// Fake presenter implementations for integration testing

// FakeEventPresenter is a fake implementation of usecases.EventPresenter
type FakeEventPresenter struct {
	ErrorMessagesReceived []string
	DesignsReceived       []usecases.Design
}

func NewFakeEventPresenter() *FakeEventPresenter {
	return &FakeEventPresenter{
		ErrorMessagesReceived: make([]string, 0),
		DesignsReceived:       make([]usecases.Design, 0),
	}
}

func (f *FakeEventPresenter) SendEvent(ctx context.Context, eventName string, data any) error {
	switch eventName {
	case "design":
		d, ok := data.(usecases.Design)
		if !ok {
			return errors.New("unexpected data type for design event")
		}
		f.DesignsReceived = append(f.DesignsReceived, d)
	case "error":
		s, ok := data.(string)
		if !ok {
			return errors.New("unexpected data type for error event")
		}
		f.ErrorMessagesReceived = append(f.ErrorMessagesReceived, s)
	case "update message":
	default:
		return errors.New("unrecognized event name")
	}
	return nil
}

func (f *FakeEventPresenter) Close(_ context.Context) {}

// FakeDesignsPresenter is a fake implementation of usecases.DesignsPresenter
type FakeDesignsPresenter struct {
	PresentDataCalls             []PresentDataCall
	PresentDesignCalls           []PresentDesignCall
	PresentDesignsCalls          []PresentDesignsCall
	PresentDesignsByProjectCalls []PresentDesignsByProjectCall
	PresentErrorCalls            []error
	LegacyIdMapping              map[uuid.UUID]string
}

type PresentDataCall struct {
	Ctx  context.Context
	Data any
}

type PresentDesignCall struct {
	Ctx    context.Context
	Design usecases.Design
}

type PresentDesignsCall struct {
	Ctx     context.Context
	Designs []usecases.Design
}

type PresentDesignsByProjectCall struct {
	Ctx    context.Context
	Data   map[entities.ProjectId][]usecases.Design
	Errors []error
}

func NewFakeDesignsPresenter() *FakeDesignsPresenter {
	return &FakeDesignsPresenter{
		PresentDataCalls:             make([]PresentDataCall, 0),
		PresentDesignCalls:           make([]PresentDesignCall, 0),
		PresentDesignsCalls:          make([]PresentDesignsCall, 0),
		PresentDesignsByProjectCalls: make([]PresentDesignsByProjectCall, 0),
		PresentErrorCalls:            make([]error, 0),
		LegacyIdMapping:              make(map[uuid.UUID]string),
	}
}

func (f *FakeDesignsPresenter) PresentData(ctx context.Context, data any) {
	f.PresentDataCalls = append(f.PresentDataCalls, PresentDataCall{Ctx: ctx, Data: data})
}

func (f *FakeDesignsPresenter) PresentDesign(ctx context.Context, design usecases.Design) {
	f.PresentDesignCalls = append(f.PresentDesignCalls, PresentDesignCall{Ctx: ctx, Design: design})
}

func (f *FakeDesignsPresenter) PresentDesigns(ctx context.Context, designs []usecases.Design) {
	f.PresentDesignsCalls = append(f.PresentDesignsCalls, PresentDesignsCall{Ctx: ctx, Designs: designs})
}

func (f *FakeDesignsPresenter) PresentDesignsByProject(ctx context.Context, data map[entities.ProjectId][]usecases.Design, errors []error) {
	f.PresentDesignsByProjectCalls = append(f.PresentDesignsByProjectCalls, PresentDesignsByProjectCall{
		Ctx: ctx, Data: data, Errors: errors,
	})
}

func (f *FakeDesignsPresenter) PresentError(err error) {
	f.PresentErrorCalls = append(f.PresentErrorCalls, err)
}

func (f *FakeDesignsPresenter) SetLegacyIdMapping(legacyIdMapping map[uuid.UUID]string) {
	f.LegacyIdMapping = legacyIdMapping
}

// FakeProjectIdPresenter is a fake implementation of usecases.ProjectIdPresenter
type FakeProjectIdPresenter struct {
	PresentProjectIdCalls []PresentProjectIdCall
	PresentErrorCalls     []error
}

type PresentProjectIdCall struct {
	Ctx       context.Context
	ProjectId entities.ProjectId
}

func NewFakeProjectIdPresenter() *FakeProjectIdPresenter {
	return &FakeProjectIdPresenter{
		PresentProjectIdCalls: make([]PresentProjectIdCall, 0),
		PresentErrorCalls:     make([]error, 0),
	}
}

func (f *FakeProjectIdPresenter) PresentProjectId(ctx context.Context, projectId entities.ProjectId) {
	f.PresentProjectIdCalls = append(f.PresentProjectIdCalls, PresentProjectIdCall{Ctx: ctx, ProjectId: projectId})
}

func (f *FakeProjectIdPresenter) PresentError(err error) {
	f.PresentErrorCalls = append(f.PresentErrorCalls, err)
}

// FakeDesignPackagesPresenter is a fake implementation of usecases.DesignPackagesPresenter
type FakeDesignPackagesPresenter struct {
	PresentDesignPackageCalls  []PresentDesignPackageCall
	PresentDesignPackagesCalls []PresentDesignPackagesCall
	PresentErrorCalls          []error
}

type PresentDesignPackageCall struct {
	Ctx       context.Context
	DesignPkg usecases.DesignPackage
}

type PresentDesignPackagesCall struct {
	Ctx        context.Context
	DesignPkgs []usecases.DesignPackage
}

func NewFakeDesignPackagesPresenter() *FakeDesignPackagesPresenter {
	return &FakeDesignPackagesPresenter{
		PresentDesignPackageCalls:  make([]PresentDesignPackageCall, 0),
		PresentDesignPackagesCalls: make([]PresentDesignPackagesCall, 0),
		PresentErrorCalls:          make([]error, 0),
	}
}

func (f *FakeDesignPackagesPresenter) PresentDesignPackage(ctx context.Context, designPkg usecases.DesignPackage) {
	f.PresentDesignPackageCalls = append(f.PresentDesignPackageCalls, PresentDesignPackageCall{Ctx: ctx, DesignPkg: designPkg})
}

func (f *FakeDesignPackagesPresenter) PresentDesignPackages(ctx context.Context, designPkgs []usecases.DesignPackage) {
	f.PresentDesignPackagesCalls = append(f.PresentDesignPackagesCalls, PresentDesignPackagesCall{Ctx: ctx, DesignPkgs: designPkgs})
}

func (f *FakeDesignPackagesPresenter) PresentError(err error) {
	f.PresentErrorCalls = append(f.PresentErrorCalls, err)
}

// FakeDesignMutationOutcomePresenter is a fake implementation of usecases.DesignMutationOutcomePresenter
type FakeDesignMutationOutcomePresenter struct {
	ConveySuccessCalls             []bool
	ConveySuccessWithResourceCalls []ConveySuccessWithResourceCall
	PresentErrorCalls              []error
}

type ConveySuccessWithResourceCall struct {
	Design usecases.Design
	Status usecases.Status
}

func NewFakeDesignMutationOutcomePresenter() *FakeDesignMutationOutcomePresenter {
	return &FakeDesignMutationOutcomePresenter{
		ConveySuccessCalls:             make([]bool, 0),
		ConveySuccessWithResourceCalls: make([]ConveySuccessWithResourceCall, 0),
		PresentErrorCalls:              make([]error, 0),
	}
}

func (f *FakeDesignMutationOutcomePresenter) ConveySuccess() {
	f.ConveySuccessCalls = append(f.ConveySuccessCalls, true)
}

func (f *FakeDesignMutationOutcomePresenter) ConveySuccessWithResource(design usecases.Design, status usecases.Status) {
	f.ConveySuccessWithResourceCalls = append(f.ConveySuccessWithResourceCalls, ConveySuccessWithResourceCall{
		Design: design, Status: status,
	})
}

func (f *FakeDesignMutationOutcomePresenter) PresentError(err error) {
	f.PresentErrorCalls = append(f.PresentErrorCalls, err)
}

// FakeDesignPackageCreationOutcomePresenter is a fake implementation of usecases.DesignPackageCreationOutcomePresenter
type FakeDesignPackageCreationOutcomePresenter struct {
	ConveySuccessWithNewResourceCalls []usecases.DesignPackage
	PresentErrorCalls                 []error
}

func NewFakeDesignPackageCreationOutcomePresenter() *FakeDesignPackageCreationOutcomePresenter {
	return &FakeDesignPackageCreationOutcomePresenter{
		ConveySuccessWithNewResourceCalls: make([]usecases.DesignPackage, 0),
		PresentErrorCalls:                 make([]error, 0),
	}
}

func (f *FakeDesignPackageCreationOutcomePresenter) ConveySuccessWithNewResource(designPkg usecases.DesignPackage) {
	f.ConveySuccessWithNewResourceCalls = append(f.ConveySuccessWithNewResourceCalls, designPkg)
}

func (f *FakeDesignPackageCreationOutcomePresenter) PresentError(err error) {
	f.PresentErrorCalls = append(f.PresentErrorCalls, err)
}

// FakeOutcomePresenter is a fake implementation of usecases.OutcomePresenter
type FakeOutcomePresenter struct {
	PresentErrorCalls  []error
	ConveySuccessCalls int
}

func NewFakeOutcomePresenter() *FakeOutcomePresenter {
	return &FakeOutcomePresenter{
		PresentErrorCalls:  make([]error, 0),
		ConveySuccessCalls: 0,
	}
}

func (f *FakeOutcomePresenter) PresentError(err error) {
	f.PresentErrorCalls = append(f.PresentErrorCalls, err)
}

func (f *FakeOutcomePresenter) ConveySuccess() {
	f.ConveySuccessCalls++
}

// FakeGenAI is a fake implementation of the AI interface for testing
type FakeGenAI struct {
	GenerateCalls []GenerateCall
	ShouldFail    bool
	FailError     error
}

type GenerateCall struct {
	Ctx                 context.Context
	ProductDescriptions map[string]string
	Title               string
	Desc                string
}

func NewFakeGenAI() *FakeGenAI {
	return &FakeGenAI{
		GenerateCalls: make([]GenerateCall, 0),
		ShouldFail:    false,
		FailError:     errors.New("fake AI generation failed"),
	}
}

func (f *FakeGenAI) GenerateDesignTitleAndDescription(ctx context.Context, productDescriptions map[string]string) (string, string, error) {
	title := "Fake Generated Title"
	description := "Fake Generated Description"

	f.GenerateCalls = append(f.GenerateCalls, GenerateCall{
		Ctx:                 ctx,
		ProductDescriptions: productDescriptions,
		Title:               title,
		Desc:                description,
	})

	if f.ShouldFail {
		return "", "", f.FailError
	}

	return title, description, nil
}

// Helper functions for fake verification

// AssertPresentErrorCalled checks if PresentError was called with the expected error
func AssertPresentErrorCalled(t assert.TestingT, fake any, expectedError error) {
	switch f := fake.(type) {
	case *FakeDesignsPresenter:
		assert.Contains(t, f.PresentErrorCalls, expectedError, "Expected PresentError to be called with error: %v", expectedError)
	case *FakeProjectIdPresenter:
		assert.Contains(t, f.PresentErrorCalls, expectedError, "Expected PresentError to be called with error: %v", expectedError)
	case *FakeDesignPackagesPresenter:
		assert.Contains(t, f.PresentErrorCalls, expectedError, "Expected PresentError to be called with error: %v", expectedError)
	case *FakeDesignMutationOutcomePresenter:
		assert.Contains(t, f.PresentErrorCalls, expectedError, "Expected PresentError to be called with error: %v", expectedError)
	case *FakeDesignPackageCreationOutcomePresenter:
		assert.Contains(t, f.PresentErrorCalls, expectedError, "Expected PresentError to be called with error: %v", expectedError)
	default:
		assert.Fail(t, "Unsupported fake type", "Type: %T", fake)
	}
}

// AssertPresentErrorNotCalled checks if PresentError was not called
func AssertPresentErrorNotCalled(t assert.TestingT, fake any) {
	switch f := fake.(type) {
	case *FakeDesignsPresenter:
		assert.Empty(t, f.PresentErrorCalls, "Expected PresentError not to be called")
	case *FakeProjectIdPresenter:
		assert.Empty(t, f.PresentErrorCalls, "Expected PresentError not to be called")
	case *FakeDesignPackagesPresenter:
		assert.Empty(t, f.PresentErrorCalls, "Expected PresentError not to be called")
	case *FakeDesignMutationOutcomePresenter:
		assert.Empty(t, f.PresentErrorCalls, "Expected PresentError not to be called")
	case *FakeDesignPackageCreationOutcomePresenter:
		assert.Empty(t, f.PresentErrorCalls, "Expected PresentError not to be called")
	default:
		assert.Fail(t, "Unsupported fake type", "Type: %T", fake)
	}
}

// AssertConveySuccessCalled checks if ConveySuccess was called
func AssertConveySuccessCalled(t assert.TestingT, fake any) {
	switch f := fake.(type) {
	case *FakeDesignMutationOutcomePresenter:
		assert.NotEmpty(t, f.ConveySuccessCalls, "Expected ConveySuccess to be called")
	default:
		assert.Fail(t, "Unsupported fake type for ConveySuccess", "Type: %T", fake)
	}
}

// AssertConveySuccessWithResourceCalled checks if ConveySuccessWithResource was called
func AssertConveySuccessWithResourceCalled(t assert.TestingT,
	fake any, expectedDesign usecases.Design, expectedStatus usecases.Status) {

	switch f := fake.(type) {
	case *FakeDesignMutationOutcomePresenter:
		found := false
		for _, call := range f.ConveySuccessWithResourceCalls {
			if call.Design.ID == expectedDesign.ID && call.Status == expectedStatus {
				found = true
				break
			}
		}
		assert.True(t, found, "Expected ConveySuccessWithResource to be called with design ID %v and status %v", expectedDesign.ID, expectedStatus)
	default:
		assert.Fail(t, "Unsupported fake type for ConveySuccessWithResource", "Type: %T", fake)
	}
}

// AssertPresentDesignCalled checks if PresentDesign was called with the expected design
func AssertPresentDesignCalled(t assert.TestingT, fake *FakeDesignsPresenter, expectedDesign usecases.Design) {
	found := false
	for _, call := range fake.PresentDesignCalls {
		if call.Design.ID == expectedDesign.ID {
			found = true
			break
		}
	}
	assert.True(t, found, "Expected PresentDesign to be called with design ID %v", expectedDesign.ID)
}

// AssertPresentDesignsCalled checks if PresentDesigns was called with the expected designs
func AssertPresentDesignsCalled(t assert.TestingT, fake *FakeDesignsPresenter, expectedDesigns []usecases.Design) {
	assert.NotEmpty(t, fake.PresentDesignsCalls, "Expected PresentDesigns to be called")
	if len(fake.PresentDesignsCalls) > 0 {
		lastCall := fake.PresentDesignsCalls[len(fake.PresentDesignsCalls)-1]
		assert.Len(t, lastCall.Designs, len(expectedDesigns), "Expected PresentDesigns to be called with %d designs", len(expectedDesigns))
	}
}

// AssertPresentProjectIdCalled checks if PresentProjectId was called with the expected project ID
func AssertPresentProjectIdCalled(t assert.TestingT, fake *FakeProjectIdPresenter, expectedProjectId entities.ProjectId) {
	found := false
	for _, call := range fake.PresentProjectIdCalls {
		if call.ProjectId == expectedProjectId {
			found = true
			break
		}
	}
	assert.True(t, found, "Expected PresentProjectId to be called with project ID %v", expectedProjectId)
}

// AssertPresentDesignPackageCalled checks if PresentDesignPackage was called with the expected design package
func AssertPresentDesignPackageCalled(t assert.TestingT, fake *FakeDesignPackagesPresenter, expectedDesignPkg usecases.DesignPackage) {
	found := false
	for _, call := range fake.PresentDesignPackageCalls {
		if call.DesignPkg.ID == expectedDesignPkg.ID {
			found = true
			break
		}
	}
	assert.True(t, found, "Expected PresentDesignPackage to be called with design package ID %v", expectedDesignPkg.ID)
}

// AssertGenerateDesignTitleAndDescriptionCalled checks if the AI generation was called
func AssertGenerateDesignTitleAndDescriptionCalled(t assert.TestingT, fake *FakeGenAI) {
	assert.NotEmpty(t, fake.GenerateCalls, "Expected GenerateDesignTitleAndDescription to be called")
}

// FailingDesignRepository wraps FakeRelDb and can simulate failures for testing
type FailingDesignRepository struct {
	*gateways.FakeRelDb
	ShouldFail bool
	FailError  error
}

func NewFailingDesignRepository() *FailingDesignRepository {
	return &FailingDesignRepository{
		FakeRelDb:  gateways.NewFakeRelDb(),
		ShouldFail: false,
		FailError:  errors.New("simulated repository failure"),
	}
}

func (r *FailingDesignRepository) UpsertDesign(ctx context.Context, design usecases.Design) (uuid.UUID, error) {
	if r.ShouldFail {
		return uuid.UUID{}, r.FailError
	}
	return r.FakeRelDb.UpsertDesign(ctx, design)
}

func (r *FailingDesignRepository) DesignsForProject(ctx context.Context, projectId entities.ProjectId) ([]usecases.Design, error) {
	if r.ShouldFail {
		return nil, r.FailError
	}
	return r.FakeRelDb.DesignsForProject(ctx, projectId)
}

// FailingRenditionRepository wraps FakeRelDb and can simulate failures for rendition operations
type FailingRenditionRepository struct {
	*gateways.FakeRelDb
	ShouldFail bool
	FailError  error
}

func NewFailingRenditionRepository() *FailingRenditionRepository {
	return &FailingRenditionRepository{
		FakeRelDb:  gateways.NewFakeRelDb(),
		ShouldFail: false,
		FailError:  errors.New("simulated rendition repository failure"),
	}
}

func (r *FailingRenditionRepository) InsertRendition(ctx context.Context, designId uuid.UUID, rendition entities.Rendition) (uuid.UUID, error) {
	if r.ShouldFail {
		return uuid.UUID{}, r.FailError
	}
	return r.FakeRelDb.InsertRendition(ctx, designId, rendition)
}
