package usecases

import (
	"context"
	"errors"
	"fmt"
	"log"
	"log/slog"
	"net/url"
	"strings"
	"sync"
	"time"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

type DesignEvolver struct {
	catalog       catalog
	productSearch productSearch
	designRepo    designRepository
	logger        *slog.Logger
}

func NewDesignEvolver(catalog catalog, productSearch productSearch, designRepo designRepository, logger *slog.Logger) *DesignEvolver {
	if IsNil(catalog) {
		panic("catalog cannot be nil")
	}
	if IsNil(productSearch) {
		panic("productSearch cannot be nil")
	}
	if IsNil(designRepo) {
		panic("designRepo cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &DesignEvolver{productSearch: productSearch, catalog: catalog, designRepo: designRepo, logger: logger}
}

// EvolveProjectDesignsForLayout evolves multiple designs concurrently for a given layout.
// It presents the successfully evolved designs via the presenter's PresentDesigns method,
// or calls PresentError if any failures occur during the evolution process.
func (de *DesignEvolver) EvolveProjectDesignsForLayout(ctx context.Context,
	projectId entities.ProjectId, layout entities.RoomLayout, presenter DesignsPresenter) {

	if IsNil(presenter) {
		panic("presenter cannot be nil")
	}

	designs, err := de.designRepo.DesignsForProject(ctx, projectId)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	if len(designs) == 0 {
		de.logger.DebugContext(ctx, "No designs found for project", slog.String("projectId", string(projectId)))
		presenter.PresentDesigns(ctx, []Design{})
		return
	}

	// Structure to hold results with their original index
	type result struct {
		design Design
		err    error
		index  int
	}

	results := make([]result, len(designs))
	var wg sync.WaitGroup
	start := time.Now()

	// Process each design concurrently
	for i, design := range designs {
		wg.Add(1)
		go func(index int, d Design) {
			defer wg.Done()

			evolvedDesign, err := de.EvolveDesignForLayout(ctx, d, layout)
			results[index] = result{
				design: evolvedDesign,
				err:    err,
				index:  index,
			}
		}(i, design)
	}
	wg.Wait()

	// Collect successful results and errors
	var successfulDesigns []Design
	var errors []error
	for _, res := range results {
		if res.err != nil {
			errors = append(errors, res.err)
			de.logger.ErrorContext(ctx, "Failed to evolve design for layout", slog.String("error", res.err.Error()),
				slog.String("designId", res.design.ID.String()),
				slog.String("projectId", string(projectId)),
				slog.String("layoutId", layout.Id.String()),
			)
		} else {
			successfulDesigns = append(successfulDesigns, res.design)
		}
	}
	duration := time.Since(start)
	de.logger.InfoContext(ctx, "Evolved designs for layout",
		slog.String("projectId", string(projectId)),
		slog.Int("successful", len(successfulDesigns)),
		slog.Int("failed", len(errors)),
		slog.String("latency", duration.String()),
	)

	if len(errors) > 0 {
		// Merge all errors into a single error message
		var errorMessages []string
		for _, err := range errors {
			errorMessages = append(errorMessages, err.Error())
		}
		mergedError := fmt.Errorf("failed to evolve %d design(s): %s",
			len(errors), strings.Join(errorMessages, "; "))
		presenter.PresentError(mergedError)
		return
	}

	// TODO: actually save the new designs to the repo for the project.
	// We can leave the UUIDs as-is & update them in-place for now.
	// Once layout versioning is a thing then we can create new designs with new UUIDs.
	// When we do that, we may need to update the active design for the project.
	presenter.PresentDesigns(ctx, successfulDesigns)
}

func (de *DesignEvolver) EvolveDesignForLayout(ctx context.Context,
	design Design, layout entities.RoomLayout) (Design, error) {
	var err error
	if len(layout.ToiletIds) == 0 {
		design.Toilet = nil
		design.Shelving = nil
	} else {
		if design.Toilet == nil {
			toiletUUIDs, err := de.productSearch.FindProducts(ctx, "category=Toilet")
			if err != nil {
				return design, err
			}
			if len(toiletUUIDs) == 0 {
				return design, errors.New("no toilets found via product search")
			}
			design.Toilet = &toiletUUIDs[0]
		}

		if design.Shelving == nil {
			shelvingUUIDs, err := de.productSearch.FindProducts(ctx, "category=Shelves")
			if err != nil {
				return design, err
			}
			if len(shelvingUUIDs) == 0 {
				return design, errors.New("no shelving found via product search")
			}
			design.Shelving = &shelvingUUIDs[0]
		}
	}

	if design, err = de.evolveVanityProducts(ctx, design, layout); err != nil {
		return design, err
	}

	// Assume only one wet area for now. TODO: Handle multiple wet areas.
	hasWalkinShower := len(layout.WetAreas) > 0 && len(layout.WetAreas[0].ShowerIds) > 0
	if !hasWalkinShower {
		design.ShowerFloorTile = nil
		design.ShowerGlass = nil
	}
	hasAlcoveTub := len(layout.WetAreas) > 0 && len(layout.WetAreas[0].AlcoveTubs) > 0
	maxShowerGlassLength := 0.0
	if len(layout.WetAreas) > 0 && layout.WetAreas[0].MaxShowerGlassLength != nil {
		maxShowerGlassLength = *layout.WetAreas[0].MaxShowerGlassLength
	}
	if design, err = de.evolveShowerProducts(ctx,
		design, hasAlcoveTub, hasWalkinShower, maxShowerGlassLength); err != nil {
		return design, err
	}

	hasFreestandingTub := len(layout.WetAreas) > 0 && len(layout.WetAreas[0].FreestandingTubIds) > 0
	maxTubDoorLength := 0.0
	if len(layout.WetAreas) > 0 && layout.WetAreas[0].MaxTubLength != nil {
		maxTubDoorLength = *layout.WetAreas[0].MaxTubLength
	}
	if design, err = de.evolveTubProducts(ctx,
		design, hasAlcoveTub, hasFreestandingTub, maxTubDoorLength); err != nil {
		return design, err
	}

	design.LastUpdated = time.Now()
	return design, nil
}

func (de *DesignEvolver) findProduct(ctx context.Context, target, baseQueryParams string,
	productFamilyName *string, color *ColorGroup) (*uuid.UUID, error) {

	urlQueryParams := baseQueryParams
	if productFamilyName != nil {
		fam := url.QueryEscape(*productFamilyName)
		urlQueryParams += fmt.Sprintf("&collection=%s", fam)
	}
	if color != nil {
		urlQueryParams += fmt.Sprintf("&colors=%s", *color)
	}
	vanityUUIDs, err := de.productSearch.FindProducts(ctx, urlQueryParams)
	if err != nil {
		return nil, err
	}
	if len(vanityUUIDs) == 0 {
		var c ColorGroup
		if color != nil {
			c = *color
		}
		errMsg := fmt.Sprintf("no %s %s found", c, target)
		if productFamilyName == nil {
			return nil, errors.New(errMsg)
		}
		errMsg += fmt.Sprintf(" in the %s collection so trying again without the collection filter", *productFamilyName)
		log.Println(errMsg)
		return de.findProduct(ctx, target, baseQueryParams, nil, color)
	}
	return &vanityUUIDs[0], nil
}
