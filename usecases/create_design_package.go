package usecases

import (
	"context"
	"log/slog"
)

type DesignPackageCreator struct {
	designPackageRepo designPackageRepository
	logger            *slog.Logger
}

func NewDesignPackageCreator(designPackageRepo designPackageRepository, logger *slog.Logger) *DesignPackageCreator {
	if IsNil(designPackageRepo) {
		panic("designPackageRepo cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &DesignPackageCreator{designPackageRepo: designPackageRepo, logger: logger}
}

func (tc *DesignPackageCreator) CreateDesignPackage(ctx context.Context, presenter DesignPackageCreationOutcomePresenter, designPkg DesignPackage, legacyId string) {
	if designPkg.Name == "" {
		tc.logger.ErrorContext(ctx, "Attempt to create design package with no name")
		presenter.PresentError(ErrInvalidPayload)
		return
	}

	var err error
	if designPkg.ID, err = tc.designPackageRepo.InsertDesignPackage(ctx, designPkg, legacyId); err != nil {
		presenter.PresentError(err)
		return
	}

	presenter.ConveySuccessWithNewResource(designPkg)
}
