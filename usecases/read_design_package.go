package usecases

import (
	"context"

	"github.com/google/uuid"
)

type DesignPackageRetriever struct {
	designPackageRepo designPackageRepositoryReplica
}

func NewDesignPackageRetriever(designPackageRepo designPackageRepositoryReplica) *DesignPackageRetriever {
	if IsNil(designPackageRepo) {
		panic("designPackageRepo cannot be nil")
	}
	return &DesignPackageRetriever{designPackageRepo: designPackageRepo}
}

func (tr *DesignPackageRetriever) RetrieveDesignPackage(ctx context.Context, presenter DesignPackagesPresenter, designPkgID uuid.UUID) {
	designPkg, err := tr.designPackageRepo.ReadDesignPackage(ctx, designPkgID)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	presenter.PresentDesignPackage(ctx, designPkg)
}

func (tr *DesignPackageRetriever) RetrieveDesignPackageByLegacyId(ctx context.Context, presenter DesignPackagesPresenter, legacyId string) {
	designPkg, err := tr.designPackageRepo.ReadDesignPackageByLegacyId(ctx, legacyId)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	presenter.PresentDesignPackage(ctx, designPkg)
}

func (tr *DesignPackageRetriever) RetrieveAllDesignPackages(ctx context.Context, presenter DesignPackagesPresenter) {
	designPkgs, err := tr.designPackageRepo.ReadAllDesignPackages(ctx)
	if err != nil {
		presenter.PresentError(err)
		return
	}
	presenter.PresentDesignPackages(ctx, designPkgs)
}
