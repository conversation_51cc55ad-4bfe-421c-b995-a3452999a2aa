package usecases_test

import (
	"context"
	"net/url"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

func createTestDesignGenerator() *usecases.DesignGenerator {
	// For testing the existing GenerateDesignsFromDesignPackages method, we don't need real implementations
	// since it doesn't use the designPackageRepo, monolith, or rooms dependencies.
	return usecases.NewDesignGenerator(
		&FakeDesignPackageRepo{}, NewFakeMonolith(), gateways.NewRooms([]byte(`{}`)), gateways.NewFakeProductSearch(), gateways.NewFakeCatalog(), NewFakeGenAI(),
		nil)
}

type FakeDesignPackageRepo struct {
	designPkgsToReturn []usecases.DesignPackage
	shouldFail         bool
	errorToReturn      error
}

func (f *FakeDesignPackageRepo) ReadDesignPackage(ctx context.Context, designPkgID uuid.UUID) (usecases.DesignPackage, error) {
	if f.shouldFail {
		return usecases.DesignPackage{}, f.errorToReturn
	}
	return usecases.DesignPackage{}, nil
}

func (f *FakeDesignPackageRepo) ReadDesignPackageByLegacyId(ctx context.Context, legacyId string) (usecases.DesignPackage, error) {
	if f.shouldFail {
		return usecases.DesignPackage{}, f.errorToReturn
	}
	return usecases.DesignPackage{}, nil
}

func (f *FakeDesignPackageRepo) ReadAllDesignPackages(ctx context.Context) ([]usecases.DesignPackage, error) {
	if f.shouldFail {
		return nil, f.errorToReturn
	}
	return f.designPkgsToReturn, nil
}

func (f *FakeDesignPackageRepo) DesignPackagesById(ctx context.Context, designPkgIDs []uuid.UUID) ([]usecases.DesignPackage, error) {
	if f.shouldFail {
		return nil, f.errorToReturn
	}
	return f.designPkgsToReturn, nil
}

func (r *FakeDesignPackageRepo) PanoramicImagesForDesignPackages(ctx context.Context, designPkgIDs []uuid.UUID) (map[uuid.UUID]url.URL, error) {
	return map[uuid.UUID]url.URL{}, nil
}

func TestDesignGenerator_GenerateDesignsFromDesignPackages(t *testing.T) {
	ctx := context.Background()
	projId := entities.ProjectId("TEST-PROJECT")

	t.Run("should generate designs from design packages successfully", func(t *testing.T) {
		generator := createTestDesignGenerator()
		presenter := NewFakeDesignsPresenter()

		roomLayout := createTestRoomLayout()
		designPkgs := []usecases.DesignPackage{
			createTestDesignPackage("Modern Design Package", usecases.Modern, usecases.Bold),
			createTestDesignPackage("Traditional Design Package", usecases.Traditional, usecases.Neutral),
		}
		panoramicImages := map[uuid.UUID]url.URL{
			designPkgs[0].ID: {Scheme: "https", Host: "example.com", Path: "/modern.jpg"},
			designPkgs[1].ID: {Scheme: "https", Host: "example.com", Path: "/traditional.jpg"},
		}

		generator.GenerateDesignsFromDesignPackages(ctx,
			roomLayout, designPkgs, panoramicImages, projId, presenter)

		require.Len(t, presenter.PresentDesignsCalls, 1)
		designs := presenter.PresentDesignsCalls[0].Designs
		require.Len(t, designs, 2)

		// Verify first design
		design1 := designs[0]
		assert.Equal(t, "Modern Design Package", design1.Title.String)
		assert.Equal(t, usecases.Modern, *design1.Style)
		assert.Equal(t, usecases.Bold, *design1.ColorScheme)
		assert.Equal(t, "/modern.jpg", design1.Renditions[0].URL.Path)

		// Verify second design
		design2 := designs[1]
		assert.Equal(t, "Traditional Design Package", design2.Title.String)
		assert.Equal(t, usecases.Traditional, *design2.Style)
		assert.Equal(t, usecases.Neutral, *design2.ColorScheme)
		assert.Equal(t, "/traditional.jpg", design2.Renditions[0].URL.Path)
	})

	t.Run("should handle empty design packages list", func(t *testing.T) {
		generator := createTestDesignGenerator()
		presenter := NewFakeDesignsPresenter()

		roomLayout := createTestRoomLayout()
		designPkgs := []usecases.DesignPackage{}
		panoramicImages := map[uuid.UUID]url.URL{}

		generator.GenerateDesignsFromDesignPackages(ctx, roomLayout, designPkgs, panoramicImages, projId, presenter)

		require.Len(t, presenter.PresentDesignsCalls, 1)
		designs := presenter.PresentDesignsCalls[0].Designs
		assert.Empty(t, designs)
	})

	t.Run("should panic with nil presenter", func(t *testing.T) {
		generator := createTestDesignGenerator()
		roomLayout := createTestRoomLayout()
		designPkgs := []usecases.DesignPackage{createTestDesignPackage("Test", usecases.Modern, usecases.Bold)}
		panoramicImages := map[uuid.UUID]url.URL{}

		assert.Panics(t, func() {
			generator.GenerateDesignsFromDesignPackages(ctx, roomLayout, designPkgs, panoramicImages, projId, nil)
		})
	})

	t.Run("should apply shower glass based on wet area glass type", func(t *testing.T) {
		generator := createTestDesignGenerator()
		presenter := NewFakeDesignsPresenter()

		// Create room layout with sliding shower glass
		roomLayout := createTestRoomLayoutWithShowerGlass(entities.SlidingShowerEnclosure)
		designPkg := createTestDesignPackageWithProductOptions()
		designPkgs := []usecases.DesignPackage{designPkg}

		panoramicImages := map[uuid.UUID]url.URL{}
		generator.GenerateDesignsFromDesignPackages(ctx, roomLayout, designPkgs, panoramicImages, projId, presenter)

		require.Len(t, presenter.PresentDesignsCalls, 1)
		designs := presenter.PresentDesignsCalls[0].Designs
		require.Len(t, designs, 1)

		design := designs[0]
		assert.Equal(t, &designPkg.ShowerGlassSliding, design.ShowerGlass)
	})

	t.Run("should apply tub door based on alcove tub door type", func(t *testing.T) {
		generator := createTestDesignGenerator()
		presenter := NewFakeDesignsPresenter()

		// Create room layout with fixed tub door
		roomLayout := createTestRoomLayoutWithAlcoveTub(entities.FixedShowerEnclosure)
		designPkg := createTestDesignPackageWithProductOptions()
		designPkgs := []usecases.DesignPackage{designPkg}

		panoramicImages := map[uuid.UUID]url.URL{}
		generator.GenerateDesignsFromDesignPackages(ctx, roomLayout, designPkgs, panoramicImages, projId, presenter)

		require.Len(t, presenter.PresentDesignsCalls, 1)
		designs := presenter.PresentDesignsCalls[0].Designs
		require.Len(t, designs, 1)

		design := designs[0]
		assert.Equal(t, &designPkg.AlcoveTub, design.Tub)
		assert.Equal(t, &designPkg.TubDoorFixed, design.TubDoor)
	})

	t.Run("should apply correct products for alcove tub layout", func(t *testing.T) {
		generator := createTestDesignGenerator()
		presenter := NewFakeDesignsPresenter()

		// Create room layout with an alcove tub
		roomLayout := createTestRoomLayoutWithAlcoveTub(entities.NoShowerEnclosure)

		// Create a designPkg that has products for freestanding tubs, to ensure they are cleared
		designPkg := createTestDesignPackageWithProductOptions()
		tubFillerId := uuid.New()
		showerFloorTileId := uuid.New()
		designPkg.TubFiller = &tubFillerId
		designPkg.ShowerFloorTile = &showerFloorTileId

		designPkgs := []usecases.DesignPackage{designPkg}

		panoramicImages := map[uuid.UUID]url.URL{}
		generator.GenerateDesignsFromDesignPackages(ctx, roomLayout, designPkgs, panoramicImages, projId, presenter)

		require.Len(t, presenter.PresentDesignsCalls, 1)
		designs := presenter.PresentDesignsCalls[0].Designs
		require.Len(t, designs, 1)

		design := designs[0]
		assert.Equal(t, &designPkg.AlcoveTub, design.Tub, "Should use the alcove tub from design package")
		assert.Equal(t, &designPkg.ShowerSystemCombo, design.ShowerSystem, "Should use the combo shower system for alcove tub")
		assert.Nil(t, design.TubFiller, "Tub filler should be nil for an alcove tub layout")
		assert.Nil(t, design.ShowerFloorTile, "Shower floor tile should be nil for an alcove tub layout")
	})

	t.Run("should apply vanity scaling based on max length", func(t *testing.T) {
		generator := createTestDesignGenerator()
		presenter := NewFakeDesignsPresenter()

		// Create room layout with vanity max length
		maxLength := 48.0
		roomLayout := createTestRoomLayoutWithVanity(&maxLength)
		designPkg := createTestDesignPackageWithVanityScaling()
		designPkgs := []usecases.DesignPackage{designPkg}

		panoramicImages := map[uuid.UUID]url.URL{}
		generator.GenerateDesignsFromDesignPackages(ctx, roomLayout, designPkgs, panoramicImages, projId, presenter)

		require.Len(t, presenter.PresentDesignsCalls, 1)
		designs := presenter.PresentDesignsCalls[0].Designs
		require.Len(t, designs, 1)

		design := designs[0]
		scalingOption := designPkg.VanityScalingOptions[48]
		assert.Equal(t, &scalingOption.VanityProductID, design.Vanity)
		assert.Equal(t, &scalingOption.FaucetProductID, design.Faucet)
	})

	t.Run("should select largest vanity that fits within max length", func(t *testing.T) {
		generator := createTestDesignGenerator()
		presenter := NewFakeDesignsPresenter()

		// Create room layout with vanity max length of 50 inches
		maxLength := 50.0
		roomLayout := createTestRoomLayoutWithVanity(&maxLength)

		// Create designPkg with multiple vanity options: 36", 48", 60"
		designPkg := createTestDesignPackageWithMultipleVanityScaling()
		designPkgs := []usecases.DesignPackage{designPkg}

		panoramicImages := map[uuid.UUID]url.URL{}
		generator.GenerateDesignsFromDesignPackages(ctx, roomLayout, designPkgs, panoramicImages, projId, presenter)

		require.Len(t, presenter.PresentDesignsCalls, 1)
		designs := presenter.PresentDesignsCalls[0].Designs
		require.Len(t, designs, 1)

		design := designs[0]
		// Should select the 48" option (largest that fits in 50")
		scalingOption := designPkg.VanityScalingOptions[48]
		assert.Equal(t, &scalingOption.VanityProductID, design.Vanity)
		assert.Equal(t, &scalingOption.FaucetProductID, design.Faucet)
	})

	t.Run("should not select vanity when none fit within max length", func(t *testing.T) {
		generator := createTestDesignGenerator()
		presenter := NewFakeDesignsPresenter()

		// Create room layout with very small vanity max length
		maxLength := 30.0
		roomLayout := createTestRoomLayoutWithVanity(&maxLength)

		// Create designPkg with vanity options that are all too large: 36", 48", 60"
		designPkg := createTestDesignPackageWithMultipleVanityScaling()
		designPkgs := []usecases.DesignPackage{designPkg}

		panoramicImages := map[uuid.UUID]url.URL{}
		generator.GenerateDesignsFromDesignPackages(ctx, roomLayout, designPkgs, panoramicImages, projId, presenter)

		require.Len(t, presenter.PresentDesignsCalls, 1)
		designs := presenter.PresentDesignsCalls[0].Designs
		require.Len(t, designs, 1)

		design := designs[0]
		// Should not select any vanity or faucet
		assert.Nil(t, design.Vanity)
		assert.Nil(t, design.Faucet)
	})

	t.Run("should set niches visible when walls have niches", func(t *testing.T) {
		generator := createTestDesignGenerator()
		presenter := NewFakeDesignsPresenter()

		roomLayout := createTestRoomLayoutWithNiches()
		designPkg := createTestDesignPackageWithProductOptions()
		designPkgs := []usecases.DesignPackage{designPkg}

		panoramicImages := map[uuid.UUID]url.URL{}
		generator.GenerateDesignsFromDesignPackages(ctx, roomLayout, designPkgs, panoramicImages, projId, presenter)

		require.Len(t, presenter.PresentDesignsCalls, 1)
		designs := presenter.PresentDesignsCalls[0].Designs
		require.Len(t, designs, 1)

		design := designs[0]
		assert.Equal(t, designPkg.ShowerWallTile, design.NicheTile)
	})
}

func TestDesignGenerator_GenerateDesignViaAI(t *testing.T) {
	ctx := context.Background()

	t.Run("should generate design with products from all categories", func(t *testing.T) {
		// Setup
		projId := entities.ProjectId("TEST-PROJECT")
		designPackageRepo := &FakeDesignPackageRepo{}
		monolith := NewFakeMonolith()
		rooms := gateways.NewRooms([]byte(`{}`))
		productSearch := gateways.NewFakeProductSearch()
		genAI := NewFakeGenAI()
		presenter := NewFakeEventPresenter()
		catalog := gateways.NewFakeCatalog()

		generator := usecases.NewDesignGenerator(designPackageRepo, monolith, rooms, productSearch, catalog, genAI, nil)

		// Setup product search results for each category
		toiletId := uuid.New()
		productSearch.AddResults(usecases.CategoryToilet.AsPathSegment(), []uuid.UUID{toiletId})

		mirrorId := uuid.New()
		productSearch.AddResults(usecases.CategoryMirror.AsPathSegment(), []uuid.UUID{mirrorId})

		lightingId := uuid.New()
		productSearch.AddResults(usecases.CategoryLighting.AsPathSegment(), []uuid.UUID{lightingId})

		paintId := uuid.New()
		productSearch.AddResults(usecases.CategoryPaint.AsPathSegment(), []uuid.UUID{paintId})

		shelvingId := uuid.New()
		productSearch.AddResults(usecases.CategoryShelving.AsPathSegment(), []uuid.UUID{shelvingId})

		floorTileId := uuid.New()
		productSearch.AddResults(usecases.CategoryTile.AsPathSegment(), []uuid.UUID{floorTileId})

		faucetId := uuid.New()
		productSearch.AddResults(usecases.CategoryFaucet.AsPathSegment(), []uuid.UUID{faucetId})

		vanityId := uuid.New()
		productSearch.AddResults(usecases.CategoryVanity.AsPathSegment(), []uuid.UUID{vanityId})

		wallpaperId := uuid.New()
		productSearch.AddResults(string(usecases.CategoryWallpaper.AsPathSegment()), []uuid.UUID{wallpaperId})

		// Create a test room layout
		roomLayout := createTestRoomLayout()

		// Execute
		generator.GenerateDesignViaAI(ctx, projId, roomLayout, "Something fancy", presenter)

		// Verify
		require.Len(t, presenter.DesignsReceived, 1)
		design := presenter.DesignsReceived[0]

		// Verify all products are populated
		assert.Equal(t, &mirrorId, design.Mirror, "Should have mirror")
		assert.Equal(t, &lightingId, design.Lighting, "Should have lighting")
		assert.Equal(t, &paintId, design.Paint, "Should have paint")
		assert.Equal(t, &floorTileId, design.FloorTile, "Should have floor tile")
		assert.Equal(t, &faucetId, design.Faucet, "Should have faucet")
		assert.Equal(t, &vanityId, design.Vanity, "Should have vanity")
		assert.Equal(t, &wallpaperId, design.Wallpaper, "Should have wallpaper")
	})

	t.Run("should handle product search errors gracefully", func(t *testing.T) {
		// Setup
		projId := entities.ProjectId("TEST-PROJECT")
		designPackageRepo := &FakeDesignPackageRepo{}
		monolith := NewFakeMonolith()
		rooms := gateways.NewRooms([]byte(`{}`))
		productSearch := gateways.NewFakeProductSearch()
		genAI := NewFakeGenAI()
		presenter := NewFakeEventPresenter()
		catalog := gateways.NewFakeCatalog()

		generator := usecases.NewDesignGenerator(designPackageRepo, monolith, rooms, productSearch, catalog, genAI, nil)

		// Setup product search results for each category except one (vanities)
		productSearch.AddResults(usecases.CategoryToilet.AsPathSegment(), []uuid.UUID{uuid.New()})
		productSearch.AddResults(usecases.CategoryMirror.AsPathSegment(), []uuid.UUID{uuid.New()})
		productSearch.AddResults(usecases.CategoryLighting.AsPathSegment(), []uuid.UUID{uuid.New()})
		productSearch.AddResults(usecases.CategoryPaint.AsPathSegment(), []uuid.UUID{uuid.New()})
		productSearch.AddResults(usecases.CategoryShelving.AsPathSegment(), []uuid.UUID{uuid.New()})
		productSearch.AddResults(usecases.CategoryTile.AsPathSegment(), []uuid.UUID{uuid.New()})
		productSearch.AddResults(usecases.CategoryFaucet.AsPathSegment(), []uuid.UUID{uuid.New()})
		productSearch.AddResults(usecases.CategoryWallpaper.AsPathSegment(), []uuid.UUID{uuid.New()})

		// Create a test room layout
		roomLayout := createTestRoomLayout()

		// Execute
		generator.GenerateDesignViaAI(ctx, projId, roomLayout, "boring", presenter)

		// Verify
		require.Len(t, presenter.ErrorMessagesReceived, 1)
	})

	t.Run("should panic with nil presenter", func(t *testing.T) {
		generator := createTestDesignGenerator()
		projId := entities.ProjectId("TEST-PROJECT")
		roomLayout := createTestRoomLayout()

		assert.Panics(t, func() {
			generator.GenerateDesignViaAI(ctx, projId, roomLayout, "", nil)
		})
	})
}

func TestDesignGenerator_GenerateDesignsForProjectFromDesignPackages(t *testing.T) {
	ctx := context.Background()

	t.Run("should generate designs for project from design package IDs successfully", func(t *testing.T) {
		fakeRepo := &FakeDesignPackageRepo{}
		fakeMonolith := NewFakeMonolith()
		rooms := gateways.NewRooms([]byte(`{}`))
		ai := gateways.NewFakeLLM()
		productSearch := gateways.NewFakeProductSearch()
		catalog := gateways.NewFakeCatalog()
		generator := usecases.NewDesignGenerator(fakeRepo, fakeMonolith, rooms, productSearch, catalog, ai, nil)
		presenter := NewFakeDesignsPresenter()

		projectId := entities.ProjectId("TEST-PROJECT")
		designPkgIDs := []uuid.UUID{uuid.New(), uuid.New()}

		// Set up the fake repo to return design packages
		fakeRepo.designPkgsToReturn = []usecases.DesignPackage{
			createTestDesignPackage("Design Package 1", usecases.Modern, usecases.Bold),
			createTestDesignPackage("Design Package 2", usecases.Traditional, usecases.Neutral),
		}

		// Set up the fake monolith to return a layout
		testLayout := createTestRoomLayout()
		fakeMonolith.SetLayoutToReturn(testLayout)

		generator.GenerateDesignsForProjectFromDesignPackages(ctx, projectId, designPkgIDs, presenter)

		// Verify the monolith was called to get the layout
		layoutCalls := fakeMonolith.GetLayoutCalls()
		require.Len(t, layoutCalls, 1)
		assert.Equal(t, projectId, layoutCalls[0].ProjectId)

		// Verify designs were generated and presented
		require.Len(t, presenter.PresentDesignsCalls, 1)
		designs := presenter.PresentDesignsCalls[0].Designs
		assert.Len(t, designs, 2)
	})

	t.Run("should handle empty design package IDs list", func(t *testing.T) {
		generator := createTestDesignGenerator()
		presenter := NewFakeDesignsPresenter()

		projectId := entities.ProjectId("TEST-PROJECT")
		designPkgIDs := []uuid.UUID{}

		generator.GenerateDesignsForProjectFromDesignPackages(ctx, projectId, designPkgIDs, presenter)

		require.Len(t, presenter.PresentDesignsCalls, 1)
		designs := presenter.PresentDesignsCalls[0].Designs
		assert.Empty(t, designs)
	})

	t.Run("should present error when layout fetch fails", func(t *testing.T) {
		fakeRepo := &FakeDesignPackageRepo{}
		fakeMonolith := NewFakeMonolith()
		rooms := gateways.NewRooms([]byte(`{}`))
		ai := gateways.NewFakeLLM()
		productSearch := gateways.NewFakeProductSearch()
		catalog := gateways.NewFakeCatalog()
		generator := usecases.NewDesignGenerator(fakeRepo, fakeMonolith, rooms, productSearch, catalog, ai, nil)
		presenter := NewFakeDesignsPresenter()

		projectId := entities.ProjectId("TEST-PROJECT")
		designPkgIDs := []uuid.UUID{uuid.New()}

		// Set up the monolith to fail
		fakeMonolith.SetShouldFailGetLayout(true, assert.AnError)

		generator.GenerateDesignsForProjectFromDesignPackages(ctx, projectId, designPkgIDs, presenter)

		// Verify error was presented
		require.Len(t, presenter.PresentErrorCalls, 1)
		assert.Equal(t, assert.AnError, presenter.PresentErrorCalls[0])
	})

	t.Run("should present error when design package fetch fails", func(t *testing.T) {
		fakeRepo := &FakeDesignPackageRepo{}
		fakeMonolith := NewFakeMonolith()
		rooms := gateways.NewRooms([]byte(`{}`))
		ai := gateways.NewFakeLLM()
		productSearch := gateways.NewFakeProductSearch()
		catalog := gateways.NewFakeCatalog()
		generator := usecases.NewDesignGenerator(fakeRepo, fakeMonolith, rooms, productSearch, catalog, ai, nil)
		presenter := NewFakeDesignsPresenter()

		projectId := entities.ProjectId("TEST-PROJECT")
		designPkgIDs := []uuid.UUID{uuid.New()}

		// Set up the repo to fail
		fakeRepo.shouldFail = true
		fakeRepo.errorToReturn = assert.AnError

		generator.GenerateDesignsForProjectFromDesignPackages(ctx, projectId, designPkgIDs, presenter)

		// Verify error was presented
		require.Len(t, presenter.PresentErrorCalls, 1)
		assert.Equal(t, assert.AnError, presenter.PresentErrorCalls[0])
	})

	t.Run("should panic with nil presenter", func(t *testing.T) {
		generator := createTestDesignGenerator()
		projectId := entities.ProjectId("TEST-PROJECT")
		designPkgIDs := []uuid.UUID{uuid.New()}

		assert.Panics(t, func() {
			generator.GenerateDesignsForProjectFromDesignPackages(ctx, projectId, designPkgIDs, nil)
		})
	})
}

// Helper functions for creating test data

func createTestRoomLayout() entities.RoomLayout {
	return entities.RoomLayout{
		Id:        uuid.New(),
		FloorIds:  []uuid.UUID{uuid.New()},
		Walls:     []entities.Wall{{LayoutId: uuid.New(), NicheIds: []uuid.UUID{}}},
		WetAreas:  []entities.WetArea{},
		ToiletIds: []uuid.UUID{uuid.New()},
		Vanities:  []entities.Vanity{{LayoutId: uuid.New()}},
	}
}

func createTestRoomLayoutWithShowerGlass(glassType entities.ShowerEnclosureType) entities.RoomLayout {
	layout := createTestRoomLayout()
	layout.WetAreas = []entities.WetArea{
		{
			LayoutId:  uuid.New(),
			GlassType: glassType,
			ShowerIds: []uuid.UUID{uuid.New()},
		},
	}
	return layout
}

func createTestRoomLayoutWithAlcoveTub(doorType entities.ShowerEnclosureType) entities.RoomLayout {
	layout := createTestRoomLayout()
	layout.WetAreas = []entities.WetArea{
		{
			LayoutId:  uuid.New(),
			GlassType: entities.NoShowerEnclosure,
			AlcoveTubs: []entities.AlcoveTub{
				{
					LayoutId: uuid.New(),
					DoorType: doorType,
				},
			},
		},
	}
	return layout
}

func createTestRoomLayoutWithVanity(maxLength *float64) entities.RoomLayout {
	layout := createTestRoomLayout()
	layout.Vanities = []entities.Vanity{
		{
			LayoutId:  uuid.New(),
			MaxLength: maxLength,
		},
	}
	return layout
}

func createTestRoomLayoutWithNiches() entities.RoomLayout {
	layout := createTestRoomLayout()
	layout.Walls = []entities.Wall{
		{
			LayoutId: uuid.New(),
			NicheIds: []uuid.UUID{uuid.New()},
		},
	}
	return layout
}

func createTestDesignPackage(name string, style usecases.Style, colorScheme usecases.ColorScheme) usecases.DesignPackage {
	floorTileId := uuid.New()
	lightingId := uuid.New()
	mirrorId := uuid.New()
	paintId := uuid.New()
	shelvingId := uuid.New()
	toiletId := uuid.New()

	return usecases.DesignPackage{
		ID: uuid.New(),
		Tagged: usecases.Tagged{
			ColorScheme: colorScheme,
			Style:       style,
		},
		Name:        name,
		Description: "Test design package description",
		FixedProductSelections: usecases.FixedProductSelections{
			FloorTile: &floorTileId,
			Lighting:  &lightingId,
			Mirror:    &mirrorId,
			Paint:     &paintId,
			Shelving:  &shelvingId,
			Toilet:    &toiletId,
		},
		WallpaperPlacement: usecases.NoWallpaper,
		WallTilePlacement:  usecases.NoWallTile,
	}
}

func createTestDesignPackageWithProductOptions() usecases.DesignPackage {
	designPkg := createTestDesignPackage("Test Design Package", usecases.Modern, usecases.Bold)
	showerWallTileId := uuid.New()
	designPkg.ShowerWallTile = &showerWallTileId
	designPkg.ProductSelectionOptions = usecases.ProductSelectionOptions{
		AlcoveTub:          uuid.New(),
		FreestandingTub:    uuid.New(),
		ShowerGlassFixed:   uuid.New(),
		ShowerGlassSliding: uuid.New(),
		ShowerSystemCombo:  uuid.New(),
		ShowerSystemSolo:   uuid.New(),
		TubDoorFixed:       uuid.New(),
		TubDoorSliding:     uuid.New(),
	}
	return designPkg
}

func createTestDesignPackageWithVanityScaling() usecases.DesignPackage {
	designPkg := createTestDesignPackageWithProductOptions()
	designPkg.VanityScalingOptions = map[int]usecases.VanityScalingOption{
		48: {
			VanityProductID: uuid.New(),
			FaucetProductID: uuid.New(),
		},
	}
	return designPkg
}

func createTestDesignPackageWithMultipleVanityScaling() usecases.DesignPackage {
	designPkg := createTestDesignPackageWithProductOptions()
	designPkg.VanityScalingOptions = map[int]usecases.VanityScalingOption{
		36: {
			VanityProductID: uuid.New(),
			FaucetProductID: uuid.New(),
		},
		48: {
			VanityProductID: uuid.New(),
			FaucetProductID: uuid.New(),
		},
		60: {
			VanityProductID: uuid.New(),
			FaucetProductID: uuid.New(),
		},
	}
	return designPkg
}
