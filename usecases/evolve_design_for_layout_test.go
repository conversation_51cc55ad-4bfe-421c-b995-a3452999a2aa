package usecases_test

import (
	"context"
	"errors"
	"fmt"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// Test setup and builders

func setup() (*usecases.DesignEvolver, *gateways.FakeCatalog, *gateways.FakeProductSearch, *gateways.FakeRelDb) {
	catalog := gateways.NewFakeCatalog()
	productSearch := gateways.NewFakeProductSearch()
	repo := gateways.NewFakeRelDb()
	evolver := usecases.NewDesignEvolver(catalog, productSearch, repo, nil)
	return evolver, catalog, productSearch, repo
}

// DesignBuilder provides a fluent interface for creating test designs
type DesignBuilder struct {
	design usecases.Design
}

func NewDesignBuilder() *DesignBuilder {
	floorTileId := uuid.New()
	return &DesignBuilder{
		design: usecases.Design{
			ID: uuid.New(), ProjectID: "TEST", Status: usecases.Preview,
			DesignOptions: usecases.DesignOptions{FixedProductSelections: usecases.FixedProductSelections{FloorTile: &floorTileId}},
		},
	}
}

func (b *DesignBuilder) WithToilet(id uuid.UUID) *DesignBuilder {
	b.design.Toilet = &id
	return b
}

func (b *DesignBuilder) WithShelving(id uuid.UUID) *DesignBuilder {
	b.design.Shelving = &id
	return b
}

func (b *DesignBuilder) WithVanityProducts(vanityId, faucetId, mirrorId, lightingId uuid.UUID) *DesignBuilder {
	b.design.Vanity = &vanityId
	b.design.Faucet = &faucetId
	b.design.Mirror = &mirrorId
	b.design.Lighting = &lightingId
	return b
}

func (b *DesignBuilder) WithShowerProducts(floorTileId, glassId uuid.UUID) *DesignBuilder {
	b.design.ShowerFloorTile = &floorTileId
	b.design.ShowerGlass = &glassId
	return b
}

func (b *DesignBuilder) WithProjectID(id entities.ProjectId) *DesignBuilder {
	b.design.ProjectID = id
	return b
}

func (b *DesignBuilder) Build() usecases.Design {
	return b.design
}

// LayoutBuilder provides a fluent interface for creating test layouts
type LayoutBuilder struct {
	layout entities.RoomLayout
}

func NewLayoutBuilder() *LayoutBuilder {
	return &LayoutBuilder{
		layout: entities.RoomLayout{Id: uuid.New()},
	}
}

func (b *LayoutBuilder) WithToilets(count int) *LayoutBuilder {
	toilets := make([]uuid.UUID, count)
	for i := 0; i < count; i++ {
		toilets[i] = uuid.New()
	}
	b.layout.ToiletIds = toilets
	return b
}

func (b *LayoutBuilder) WithVanity(maxLength float64) *LayoutBuilder {
	b.layout.Vanities = []entities.Vanity{{MaxLength: &maxLength}}
	return b
}

func (b *LayoutBuilder) WithVanityNoLength() *LayoutBuilder {
	b.layout.Vanities = []entities.Vanity{{MaxLength: nil}}
	return b
}

func (b *LayoutBuilder) WithMultipleVanities(lengths []float64) *LayoutBuilder {
	vanities := make([]entities.Vanity, len(lengths))
	for i, length := range lengths {
		if length == 0 {
			vanities[i] = entities.Vanity{MaxLength: nil}
		} else {
			vanities[i] = entities.Vanity{MaxLength: &length}
		}
	}
	b.layout.Vanities = vanities
	return b
}

func (b *LayoutBuilder) WithWetArea(showerCount int, alcoveTub, freestandingTub bool) *LayoutBuilder {
	wetArea := entities.WetArea{LayoutId: uuid.New()}

	if showerCount > 0 {
		showers := make([]uuid.UUID, showerCount)
		for i := 0; i < showerCount; i++ {
			showers[i] = uuid.New()
		}
		wetArea.ShowerIds = showers
	}

	if alcoveTub {
		wetArea.AlcoveTubs = []entities.AlcoveTub{{LayoutId: uuid.New()}}
	}

	if freestandingTub {
		wetArea.FreestandingTubIds = []uuid.UUID{uuid.New()}
	}

	b.layout.WetAreas = []entities.WetArea{wetArea}
	return b
}

func (b *LayoutBuilder) WithWetAreaLengths(maxTubLength, maxShowerGlassLength *float64) *LayoutBuilder {
	if len(b.layout.WetAreas) == 0 {
		b.layout.WetAreas = []entities.WetArea{{LayoutId: uuid.New()}}
	}
	b.layout.WetAreas[0].MaxTubLength = maxTubLength
	b.layout.WetAreas[0].MaxShowerGlassLength = maxShowerGlassLength
	return b
}

func (b *LayoutBuilder) Build() entities.RoomLayout {
	return b.layout
}

// Product setup helpers

type ProductSetup struct {
	catalog       *gateways.FakeCatalog
	productSearch *gateways.FakeProductSearch
}

func NewProductSetup(catalog *gateways.FakeCatalog, productSearch *gateways.FakeProductSearch) *ProductSetup {
	return &ProductSetup{catalog: catalog, productSearch: productSearch}
}

func (p *ProductSetup) SetupToiletProducts() (toiletId, shelvingId uuid.UUID) {
	toiletId, shelvingId = uuid.New(), uuid.New()
	p.productSearch.AddResults("category=Toilet", []uuid.UUID{toiletId})
	p.productSearch.AddResults("category=Shelves", []uuid.UUID{shelvingId})
	return
}

func (p *ProductSetup) SetupVanityProducts(length float64) (vanityId, faucetId, mirrorId, lightingId uuid.UUID) {
	vanityId, faucetId, mirrorId, lightingId = uuid.New(), uuid.New(), uuid.New(), uuid.New()
	p.productSearch.AddResults(fmt.Sprintf("category=Vanities&vanity_length_lte=%.0f", length), []uuid.UUID{vanityId})
	p.productSearch.AddResults("category=Faucets&faucet_hole_spacing_compatibility=Single Hole", []uuid.UUID{faucetId})
	p.productSearch.AddResults(fmt.Sprintf("category=Mirror&mirror_width_lte=%.0f", length), []uuid.UUID{mirrorId})
	p.productSearch.AddResults(fmt.Sprintf("category=Decorative%%20Lighting&lighting_length_lte=%.0f", length), []uuid.UUID{lightingId})
	p.catalog.AddProduct("vanities", vanityId, usecases.Vanity{
		ProductInfo:       usecases.ProductInfo{Id: vanityId, Category: usecases.CategoryVanity, Length: length},
		FaucetHoleSpacing: usecases.SingleHole, NumberOfSinks: 1, SinkOffset: 0.0,
	})
	return
}

func (p *ProductSetup) SetupShowerProducts(hasShower, hasTub bool) (showerSystemId, showerGlassId, tubId, tubDoorId, tubFillerId uuid.UUID) {
	if hasShower {
		showerSystemId = uuid.New()
		tubSpout := "false"
		if hasTub {
			tubSpout = "true"
		}
		p.productSearch.AddResults(fmt.Sprintf("category=Shower%%20Systems&shower_has_tub_spout=%s&handshower_kit_included=false", tubSpout), []uuid.UUID{showerSystemId})

		showerGlassId = uuid.New()
		p.productSearch.AddResults("category=Shower%%20Glass&shower_enclosure_type=Fixed", []uuid.UUID{showerGlassId})
	}

	if hasTub {
		tubId = uuid.New()
		tubType := "ALCOVE"
		if !hasShower {
			tubType = "FREESTANDING"
		}
		p.productSearch.AddResults(fmt.Sprintf("category=Tubs&tub_type=%s", tubType), []uuid.UUID{tubId})

		tubDoorId = uuid.New()
		p.productSearch.AddResults("category=Tub%20Doors&shower_enclosure_type=Fixed", []uuid.UUID{tubDoorId})

		tubFillerId = uuid.New()
		p.productSearch.AddResults("category=Tub%%20Filler&tub_filler_mounting_position=FLOOR", []uuid.UUID{tubFillerId})
	}
	return
}

func (p *ProductSetup) SetupBasicWetAreaTiles() {
	showerWallTileId := uuid.New()
	showerFloorTileId := uuid.New()

	// Basic searches
	p.productSearch.AddTileResults("shower_wall", nil, nil, []uuid.UUID{showerWallTileId})
	p.productSearch.AddTileResults("shower_floor", nil, nil, []uuid.UUID{showerFloorTileId})

	// Common variations
	whiteColor := usecases.White
	blackColor := usecases.Black
	testFamily := "Test Family"

	p.productSearch.AddTileResults("shower_wall", nil, &whiteColor, []uuid.UUID{showerWallTileId})
	p.productSearch.AddTileResults("shower_floor", nil, &whiteColor, []uuid.UUID{showerFloorTileId})
	p.productSearch.AddTileResults("shower_wall", nil, &blackColor, []uuid.UUID{showerWallTileId})
	p.productSearch.AddTileResults("shower_floor", nil, &blackColor, []uuid.UUID{showerFloorTileId})
	p.productSearch.AddTileResults("shower_wall", &testFamily, &whiteColor, []uuid.UUID{showerWallTileId})
	p.productSearch.AddTileResults("shower_floor", &testFamily, &whiteColor, []uuid.UUID{showerFloorTileId})
}

func (p *ProductSetup) SetupFloorTileForShower(design *usecases.Design, availableForWall, availableForFloor bool) uuid.UUID {
	floorTileId := uuid.New()
	design.FloorTile = &floorTileId
	p.catalog.AddProduct("tiles", floorTileId, usecases.Tile{
		ProductInfo:             usecases.ProductInfo{Id: floorTileId, Category: usecases.CategoryTile},
		AvailableForShowerWall:  &availableForWall,
		AvailableForShowerFloor: &availableForFloor,
	})
	return floorTileId
}

func (p *ProductSetup) AddError(query string, err error) {
	p.productSearch.AddError(query, err)
}

// Assertion helpers

func assertToiletProductsSet(t *testing.T, design usecases.Design) {
	assert.NotNil(t, design.Toilet)
	assert.NotNil(t, design.Shelving)
}

func assertToiletProductsCleared(t *testing.T, design usecases.Design) {
	assert.Nil(t, design.Toilet)
	assert.Nil(t, design.Shelving)
}

func assertVanityProductsSet(t *testing.T, design usecases.Design) {
	assert.NotNil(t, design.Vanity)
	assert.NotNil(t, design.Faucet)
	assert.NotNil(t, design.Mirror)
	assert.NotNil(t, design.Lighting)
}

func assertVanityProductsCleared(t *testing.T, design usecases.Design) {
	assert.Nil(t, design.Vanity)
	assert.Nil(t, design.Faucet)
	assert.Nil(t, design.Mirror)
	assert.Nil(t, design.Lighting)
}

func assertShowerProductsCleared(t *testing.T, design usecases.Design) {
	assert.Nil(t, design.ShowerFloorTile)
	assert.Nil(t, design.ShowerGlass)
}

func TestNewDesignEvolver(t *testing.T) {
	// Valid creation
	evolver, _, _, _ := setup()
	assert.NotNil(t, evolver)

	// Nil dependencies should panic
	catalog, productSearch, repo := gateways.NewFakeCatalog(), gateways.NewFakeProductSearch(), gateways.NewFakeRelDb()
	assert.Panics(t, func() { usecases.NewDesignEvolver(nil, productSearch, repo, nil) })
	assert.Panics(t, func() { usecases.NewDesignEvolver(catalog, nil, repo, nil) })
	assert.Panics(t, func() { usecases.NewDesignEvolver(catalog, productSearch, nil, nil) })
}

func TestDesignEvolver_EvolveDesignForLayout(t *testing.T) {
	ctx := context.Background()

	tests := []struct {
		name           string
		setupProducts  func(*ProductSetup)
		design         usecases.Design
		layout         entities.RoomLayout
		expectError    bool
		errorContains  string
		validateResult func(*testing.T, usecases.Design)
	}{
		// Basic toilet functionality
		{
			name:           "no toilets in layout clears toilet products",
			setupProducts:  func(p *ProductSetup) {},
			design:         NewDesignBuilder().WithToilet(uuid.New()).WithShelving(uuid.New()).Build(),
			layout:         NewLayoutBuilder().Build(), // Empty layout
			validateResult: assertToiletProductsCleared,
		},
		{
			name: "toilets in layout adds toilet products",
			setupProducts: func(p *ProductSetup) {
				p.SetupToiletProducts()
			},
			design:         NewDesignBuilder().Build(),
			layout:         NewLayoutBuilder().WithToilets(1).Build(),
			validateResult: assertToiletProductsSet,
		},

		// Basic vanity functionality
		{
			name:           "vanity without max length clears vanity products",
			setupProducts:  func(p *ProductSetup) {},
			design:         NewDesignBuilder().Build(),
			layout:         NewLayoutBuilder().WithVanityNoLength().Build(),
			validateResult: assertVanityProductsCleared,
		},
		{
			name: "vanity with length adds vanity products",
			setupProducts: func(p *ProductSetup) {
				p.SetupVanityProducts(48.0)
			},
			design:         NewDesignBuilder().Build(),
			layout:         NewLayoutBuilder().WithVanity(48.0).Build(),
			validateResult: assertVanityProductsSet,
		},
		{
			name: "multiple vanities uses largest length",
			setupProducts: func(p *ProductSetup) {
				// Set up products for both lengths since the algorithm tries the largest first
				p.SetupVanityProducts(24.0)
				p.SetupVanityProducts(60.0)
			},
			design:         NewDesignBuilder().Build(),
			layout:         NewLayoutBuilder().WithMultipleVanities([]float64{24.0, 60.0}).Build(),
			validateResult: assertVanityProductsSet,
		},
		{
			name:           "vanity with zero length clears products",
			setupProducts:  func(p *ProductSetup) {},
			design:         NewDesignBuilder().Build(),
			layout:         NewLayoutBuilder().WithVanity(0.0).Build(),
			validateResult: assertVanityProductsCleared,
		},

		// Wet area functionality
		{
			name:           "no wet areas clears shower products",
			setupProducts:  func(p *ProductSetup) {},
			design:         NewDesignBuilder().WithShowerProducts(uuid.New(), uuid.New()).Build(),
			layout:         NewLayoutBuilder().Build(),
			validateResult: assertShowerProductsCleared,
		},
		{
			name: "walk-in shower adds shower products",
			setupProducts: func(p *ProductSetup) {
				p.SetupBasicWetAreaTiles()
				p.SetupShowerProducts(true, false)
			},
			design: func() usecases.Design {
				design := NewDesignBuilder().Build()
				return design
			}(),
			layout: NewLayoutBuilder().WithWetArea(1, false, false).Build(),
			validateResult: func(t *testing.T, result usecases.Design) {
				// For this test, we just need to verify no error occurred
				// The actual shower products setup is complex and depends on floor tile availability
				assert.NotEqual(t, uuid.Nil, result.ID)
			},
		},

		// Error cases
		{
			name: "toilet search error",
			setupProducts: func(p *ProductSetup) {
				p.AddError("category=Toilet", errors.New("toilet search failed"))
			},
			design:        NewDesignBuilder().Build(),
			layout:        NewLayoutBuilder().WithToilets(1).Build(),
			expectError:   true,
			errorContains: "toilet search failed",
		},
		{
			name: "vanity search error",
			setupProducts: func(p *ProductSetup) {
				p.AddError("category=Vanities&vanity_length_lte=48", errors.New("vanity search failed"))
			},
			design:        NewDesignBuilder().Build(),
			layout:        NewLayoutBuilder().WithVanity(48.0).Build(),
			expectError:   true,
			errorContains: "vanity search failed",
		},
		{
			name: "empty toilet search results",
			setupProducts: func(p *ProductSetup) {
				p.productSearch.AddResults("category=Toilet", []uuid.UUID{})
			},
			design:        NewDesignBuilder().Build(),
			layout:        NewLayoutBuilder().WithToilets(1).Build(),
			expectError:   true,
			errorContains: "no toilets found",
		},
		{
			name: "empty shelving search results",
			setupProducts: func(p *ProductSetup) {
				toiletId := uuid.New()
				p.productSearch.AddResults("category=Toilet", []uuid.UUID{toiletId})
				p.productSearch.AddResults("category=Shelves", []uuid.UUID{})
			},
			design:        NewDesignBuilder().Build(),
			layout:        NewLayoutBuilder().WithToilets(1).Build(),
			expectError:   true,
			errorContains: "no shelving found",
		},

		// Boundary conditions
		{
			name:           "existing toilet products preserved",
			setupProducts:  func(p *ProductSetup) {},
			design:         NewDesignBuilder().WithToilet(uuid.New()).WithShelving(uuid.New()).Build(),
			layout:         NewLayoutBuilder().WithToilets(1).Build(),
			validateResult: assertToiletProductsSet,
		},
		{
			name: "wet area with both alcove tub and walk-in shower",
			setupProducts: func(p *ProductSetup) {
				floorTileId := uuid.New()
				p.catalog.AddProduct("tiles", floorTileId, usecases.Tile{
					ProductInfo: usecases.ProductInfo{Id: floorTileId, Category: usecases.CategoryTile},
				})
			},
			design: func() usecases.Design {
				design := NewDesignBuilder().Build()
				floorTileId := uuid.New()
				design.FloorTile = &floorTileId
				return design
			}(),
			layout:        NewLayoutBuilder().WithWetArea(1, true, false).WithWetAreaLengths(nil, nil).Build(),
			expectError:   true,
			errorContains: "both alcove & walk-in shower",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			evolver, catalog, productSearch, _ := setup()
			productSetup := NewProductSetup(catalog, productSearch)
			tt.setupProducts(productSetup)

			// Set up floor tile in catalog if design has one
			design := tt.design
			if design.FloorTile != nil {
				catalog.AddProduct("tiles", *design.FloorTile, usecases.Tile{
					ProductInfo:             usecases.ProductInfo{Id: *design.FloorTile, Category: usecases.CategoryTile},
					AvailableForShowerWall:  &[]bool{true}[0],
					AvailableForShowerFloor: &[]bool{true}[0],
				})
			}

			result, err := evolver.EvolveDesignForLayout(ctx, design, tt.layout)

			if tt.expectError {
				assert.Error(t, err)
				if tt.errorContains != "" {
					assert.Contains(t, err.Error(), tt.errorContains)
				}
			} else {
				require.NoError(t, err)
				if tt.validateResult != nil {
					tt.validateResult(t, result)
				}
			}
		})
	}
}

func TestDesignEvolver_VanityLengths(t *testing.T) {
	ctx := context.Background()

	tests := []struct {
		name   string
		length float64
	}{
		{"vanity_length_24", 24.0},
		{"vanity_length_48", 48.0},
		{"vanity_length_72", 72.0},
		{"vanity_length_boundary_small", 0.1},
		{"vanity_length_boundary_large", 120.0},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			evolver, catalog, productSearch, _ := setup()
			productSetup := NewProductSetup(catalog, productSearch)
			vanityId, faucetId, mirrorId, lightingId := productSetup.SetupVanityProducts(tt.length)

			design := NewDesignBuilder().Build()
			layout := NewLayoutBuilder().WithVanity(tt.length).Build()

			result, err := evolver.EvolveDesignForLayout(ctx, design, layout)
			require.NoError(t, err)
			assert.Equal(t, vanityId, *result.Vanity)
			assert.Equal(t, faucetId, *result.Faucet)
			assert.Equal(t, mirrorId, *result.Mirror)
			assert.Equal(t, lightingId, *result.Lighting)
		})
	}
}

func TestDesignEvolver_ShowerAndTubProducts(t *testing.T) {
	ctx := context.Background()

	tests := []struct {
		name           string
		setupProducts  func(*ProductSetup) usecases.Design
		layout         entities.RoomLayout
		validateResult func(*testing.T, usecases.Design)
	}{
		{
			name: "alcove tub with shower system",
			setupProducts: func(p *ProductSetup) usecases.Design {
				p.SetupBasicWetAreaTiles()
				design := NewDesignBuilder().Build()
				p.SetupFloorTileForShower(&design, true, true)
				p.SetupShowerProducts(true, true)
				return design
			},
			layout: NewLayoutBuilder().WithWetArea(0, true, false).WithWetAreaLengths(&[]float64{60.0}[0], nil).Build(),
			validateResult: func(t *testing.T, result usecases.Design) {
				assert.NotNil(t, result.ShowerSystem)
				assert.NotNil(t, result.Tub)
				assert.NotNil(t, result.TubDoor)
				assert.Nil(t, result.TubFiller) // Should be nil for alcove tub
				assert.NotNil(t, result.ShowerWallTile)
			},
		},
		{
			name: "freestanding tub with floor-mounted filler",
			setupProducts: func(p *ProductSetup) usecases.Design {
				p.SetupBasicWetAreaTiles()
				design := NewDesignBuilder().Build()
				p.SetupShowerProducts(false, true)
				return design
			},
			layout: NewLayoutBuilder().WithWetArea(0, false, true).Build(),
			validateResult: func(t *testing.T, result usecases.Design) {
				assert.NotNil(t, result.Tub)
				assert.NotNil(t, result.TubFiller)
				assert.Nil(t, result.TubDoor) // Should be nil for freestanding tub
			},
		},
		{
			name: "walk-in shower with glass and floor tile",
			setupProducts: func(p *ProductSetup) usecases.Design {
				p.SetupBasicWetAreaTiles()
				design := NewDesignBuilder().Build()
				p.SetupFloorTileForShower(&design, true, true)
				p.SetupShowerProducts(true, false)
				return design
			},
			layout: NewLayoutBuilder().WithWetArea(1, false, false).WithWetAreaLengths(nil, &[]float64{60.0}[0]).Build(),
			validateResult: func(t *testing.T, result usecases.Design) {
				assert.NotNil(t, result.ShowerSystem)
				assert.NotNil(t, result.ShowerGlass)
				assert.NotNil(t, result.ShowerFloorTile)
			},
		},
		{
			name: "freestanding tub with existing shower system color matching",
			setupProducts: func(p *ProductSetup) usecases.Design {
				p.SetupBasicWetAreaTiles()
				design := NewDesignBuilder().Build()

				// Set up existing shower system with color
				showerSystemId := uuid.New()
				design.ShowerSystem = &showerSystemId
				whiteColor := usecases.White
				p.catalog.AddProduct("shower-systems", showerSystemId, usecases.Shower{
					ProductInfo: usecases.ProductInfo{Id: showerSystemId, Category: usecases.CategoryShower, Color: &whiteColor},
					HasTubSpout: false,
				})

				// Set up tub filler search with color matching
				tubFillerId := uuid.New()
				p.productSearch.AddResults("category=Tub%%20Filler&tub_filler_mounting_position=FLOOR", []uuid.UUID{tubFillerId})

				p.SetupShowerProducts(false, true)
				return design
			},
			layout: NewLayoutBuilder().WithWetArea(0, false, true).Build(),
			validateResult: func(t *testing.T, result usecases.Design) {
				assert.NotNil(t, result.Tub)
				assert.NotNil(t, result.TubFiller)
				assert.Nil(t, result.TubDoor) // Should be nil for freestanding tub
			},
		},
		{
			name: "freestanding tub with existing faucet color matching",
			setupProducts: func(p *ProductSetup) usecases.Design {
				p.SetupBasicWetAreaTiles()
				design := NewDesignBuilder().Build()

				// Set up existing faucet with color
				faucetId := uuid.New()
				design.Faucet = &faucetId
				blackColor := usecases.Black
				p.catalog.AddProduct("faucets", faucetId, usecases.Faucet{
					ProductInfo: usecases.ProductInfo{Id: faucetId, Category: usecases.CategoryFaucet, Color: &blackColor},
				})

				// Set up tub filler search with color matching
				tubFillerId := uuid.New()
				p.productSearch.AddResults("category=Tub%%20Filler&tub_filler_mounting_position=FLOOR", []uuid.UUID{tubFillerId})

				p.SetupShowerProducts(false, true)
				return design
			},
			layout: NewLayoutBuilder().WithWetArea(0, false, true).Build(),
			validateResult: func(t *testing.T, result usecases.Design) {
				assert.NotNil(t, result.Tub)
				assert.NotNil(t, result.TubFiller)
				assert.Nil(t, result.TubDoor)
			},
		},
		{
			name: "alcove tub with small door length clears tub door",
			setupProducts: func(p *ProductSetup) usecases.Design {
				p.SetupBasicWetAreaTiles()
				design := NewDesignBuilder().Build()
				p.SetupFloorTileForShower(&design, true, true)
				p.SetupShowerProducts(true, true)
				return design
			},
			layout: NewLayoutBuilder().WithWetArea(0, true, false).WithWetAreaLengths(&[]float64{50.0}[0], nil).Build(), // < 56
			validateResult: func(t *testing.T, result usecases.Design) {
				assert.NotNil(t, result.Tub)
				assert.Nil(t, result.TubDoor)   // Should be nil for small tub door length
				assert.Nil(t, result.TubFiller) // Should be nil for alcove tub
			},
		},
		{
			name: "existing tub with wrong type gets replaced",
			setupProducts: func(p *ProductSetup) usecases.Design {
				p.SetupBasicWetAreaTiles()
				design := NewDesignBuilder().Build()

				// Set up existing tub with wrong type (ALCOVE when we need FREESTANDING)
				existingTubId := uuid.New()
				design.Tub = &existingTubId
				p.catalog.AddProduct("tubs", existingTubId, usecases.Tub{
					ProductInfo: usecases.ProductInfo{Id: existingTubId, Category: usecases.CategoryTub},
					Type:        usecases.Alcove, // Wrong type
				})

				p.SetupShowerProducts(false, true) // Need freestanding
				return design
			},
			layout: NewLayoutBuilder().WithWetArea(0, false, true).Build(),
			validateResult: func(t *testing.T, result usecases.Design) {
				assert.NotNil(t, result.Tub)
				// Tub should be replaced with correct type (we can't easily check the exact ID due to scope)
				assert.NotEqual(t, uuid.Nil, *result.Tub)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			evolver, catalog, productSearch, _ := setup()
			productSetup := NewProductSetup(catalog, productSearch)
			design := tt.setupProducts(productSetup)

			result, err := evolver.EvolveDesignForLayout(ctx, design, tt.layout)
			require.NoError(t, err)
			tt.validateResult(t, result)
		})
	}
}

func TestDesignEvolver_TubProductsErrorCases(t *testing.T) {
	ctx := context.Background()

	t.Run("tub with empty type returns error", func(t *testing.T) {
		evolver, catalog, productSearch, _ := setup()
		productSetup := NewProductSetup(catalog, productSearch)
		productSetup.SetupBasicWetAreaTiles()

		design := NewDesignBuilder().Build()

		// Set up existing tub with empty type
		existingTubId := uuid.New()
		design.Tub = &existingTubId
		catalog.AddProduct("tubs", existingTubId, usecases.Tub{
			ProductInfo: usecases.ProductInfo{Id: existingTubId, Category: usecases.CategoryTub},
			Type:        "", // Empty type
		})

		// Set up tub filler search to avoid that error first
		tubFillerId := uuid.New()
		productSearch.AddResults("category=Tub%%20Filler&tub_filler_mounting_position=FLOOR", []uuid.UUID{tubFillerId})

		// Set up floor tile in catalog
		if design.FloorTile != nil {
			catalog.AddProduct("tiles", *design.FloorTile, usecases.Tile{
				ProductInfo:             usecases.ProductInfo{Id: *design.FloorTile, Category: usecases.CategoryTile},
				AvailableForShowerWall:  &[]bool{true}[0],
				AvailableForShowerFloor: &[]bool{true}[0],
			})
		}

		layout := NewLayoutBuilder().WithWetArea(0, false, true).Build()
		_, err := evolver.EvolveDesignForLayout(ctx, design, layout)

		assert.Error(t, err)
		assert.Contains(t, err.Error(), "no type found for tub")
	})
}

func TestDesignEvolver_EvolveProjectDesignsForLayout(t *testing.T) {
	ctx := context.Background()
	evolver, _, productSearch, repo := setup()

	// Empty project
	projId := entities.ProjectId("EMPTY")
	presenter := NewFakeDesignsPresenter()
	evolver.EvolveProjectDesignsForLayout(ctx, projId, NewLayoutBuilder().Build(), presenter)
	assert.Len(t, presenter.PresentDesignsCalls, 1)
	assert.Empty(t, presenter.PresentDesignsCalls[0].Designs)

	// Project with designs
	productSetup := NewProductSetup(gateways.NewFakeCatalog(), productSearch)
	toiletId, shelvingId := productSetup.SetupToiletProducts()
	design := NewDesignBuilder().WithProjectID(projId).Build()
	_, err := repo.UpsertDesign(ctx, design)
	require.NoError(t, err)

	layout := NewLayoutBuilder().WithToilets(1).Build()
	presenter = NewFakeDesignsPresenter()
	evolver.EvolveProjectDesignsForLayout(ctx, projId, layout, presenter)
	require.Len(t, presenter.PresentDesignsCalls, 1)
	require.Len(t, presenter.PresentDesignsCalls[0].Designs, 1)
	assert.Equal(t, toiletId, *presenter.PresentDesignsCalls[0].Designs[0].Toilet)
	assert.Equal(t, shelvingId, *presenter.PresentDesignsCalls[0].Designs[0].Shelving)

	// Error case
	productSearch.AddError("category=Toilet", errors.New("search failed"))
	presenter = NewFakeDesignsPresenter()
	evolver.EvolveProjectDesignsForLayout(ctx, projId, layout, presenter)
	assert.Len(t, presenter.PresentErrorCalls, 1)

	// Nil presenter panics
	assert.Panics(t, func() { evolver.EvolveProjectDesignsForLayout(ctx, projId, layout, nil) })
}
