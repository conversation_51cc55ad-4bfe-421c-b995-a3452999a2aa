package usecases

import (
	"context"
	"log/slog"

	"github.com/google/uuid"
)

// CartInclusionMerger handles merging new cart inclusions with existing ones (partial updates).
type CartInclusionMerger struct {
	cartInclusionRepository cartInclusionRepository
	logger                  *slog.Logger
}

// NewCartInclusionMerger creates a new CartInclusionMerger.
func NewCartInclusionMerger(cartInclusionRepository cartInclusionRepository, logger *slog.Logger) *CartInclusionMerger {
	if cartInclusionRepository == nil {
		panic("cartInclusionRepository cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}

	return &CartInclusionMerger{
		cartInclusionRepository: cartInclusionRepository,
		logger:                  logger,
	}
}

// MergeCartInclusions merges new cart inclusions with existing ones for a design.
// This performs a partial update - only the provided inclusions are modified,
// existing inclusions not in the input are preserved.
// The merge logic is handled efficiently at the DB level.
func (m *CartInclusionMerger) MergeCartInclusions(ctx context.Context, presenter OutcomePresenter, designId uuid.UUID, newInclusions CartInclusions) {
	m.logger.DebugContext(ctx, "Starting cart inclusions merge",
		slog.String("operation", "merge_cart_inclusions"),
		slog.String("designId", designId.String()),
		slog.Int("newInclusionCount", len(newInclusions)))

	// Validate all cart inclusions before making any changes
	if err := ValidateAndLogCartInclusions(ctx, m.logger, "merge_cart_inclusions", designId, newInclusions); err != nil {
		presenter.PresentError(err)
		return
	}

	// Upsert the new inclusions - this merges with existing ones at the DB level
	err := m.cartInclusionRepository.UpsertCartInclusionsForDesign(ctx, designId, newInclusions)
	if err != nil {
		m.logger.ErrorContext(ctx, "Failed to save merged cart inclusions",
			slog.String("operation", "merge_cart_inclusions"),
			slog.String("designId", designId.String()),
			slog.String("error", err.Error()))
		presenter.PresentError(err)
		return
	}

	m.logger.DebugContext(ctx, "Successfully merged cart inclusions",
		slog.String("operation", "merge_cart_inclusions"),
		slog.String("designId", designId.String()),
		slog.Int("mergedInclusionCount", len(newInclusions)),
		slog.String("status", "success"))

	presenter.ConveySuccess()
}
