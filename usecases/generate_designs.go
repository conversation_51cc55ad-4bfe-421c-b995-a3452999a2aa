package usecases

import (
	"context"
	"database/sql"
	"fmt"
	"log/slog"
	"net/url"
	"sync"
	"time"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

type designPackageRepo interface {
	designPackageRepositoryReplica
	PanoramicImagesForDesignPackages(ctx context.Context, designPkgIDs []uuid.UUID) (map[uuid.UUID]url.URL, error)
}

// DesignGenerator creates designs for specified room layouts
type DesignGenerator struct {
	logger            *slog.Logger
	designPackageRepo designPackageRepo
	monolith          monolith
	rooms             rooms
	productSearch     productSearch
	catalog           catalog
	genAI             genAI
}

func NewDesignGenerator(designPackageRepo designPackageRepo, monolith monolith, rooms rooms, productSearch productSearch, catalog catalog, genAI genAI,
	logger *slog.Logger) *DesignGenerator {

	if IsNil(designPackageRepo) {
		panic("designPackageRepo cannot be nil")
	}
	if IsNil(monolith) {
		panic("monolith cannot be nil")
	}
	if IsNil(rooms) {
		panic("rooms cannot be nil")
	}
	if IsNil(productSearch) {
		panic("productSearch cannot be nil")
	}
	if IsNil(genAI) {
		panic("genAI cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &DesignGenerator{logger: logger, designPackageRepo: designPackageRepo, monolith: monolith, rooms: rooms, productSearch: productSearch, genAI: genAI}
}

func (dg *DesignGenerator) GenerateDesignForProjectViaAI(ctx context.Context,
	projectId entities.ProjectId, userInput string, presenter EventPresenter) {

	if IsNil(presenter) {
		panic("presenter cannot be nil")
	}
	dg.logger.InfoContext(ctx, "Generating design for project via AI",
		slog.String("projectId", projectId.String()))

	roomLayout, err := dg.monolith.GetLayoutForProject(ctx, projectId)
	if err != nil {
		dg.logger.ErrorContext(ctx, "Failed to fetch layout for project",
			slog.String("projectId", projectId.String()),
			slog.String("error", err.Error()))
		if err := presenter.SendEvent(ctx, "error", err.Error()); err != nil {
			dg.logger.ErrorContext(ctx, "Failed to send error event",
				slog.String("error", err.Error()))
		}
		presenter.Close(ctx)
		return
	}
	dg.GenerateDesignViaAI(ctx, projectId, roomLayout, userInput, presenter)
}

type designElement struct {
	Category Category
	Location TileLocation
}

// productSearchResult holds the result of a product search for a specific category
type productSearchResult struct {
	filters       ProductSearchFilters
	productUUIDs  []uuid.UUID
	err           error
	topChoiceInfo RenderableProduct
}

// GenerateDesignViaAI creates a design via AI, sending incremental updates via the provided EventPresenter
func (dg *DesignGenerator) GenerateDesignViaAI(ctx context.Context,
	projectId entities.ProjectId, roomLayout entities.RoomLayout, userInput string, presenter EventPresenter) {

	start := time.Now()
	nilUUID := uuid.Nil
	design := NewDesign(projectId, &nilUUID)

	// Define design elements for which to search.
	floor := TileLocationFloor
	wall := TileLocationWall
	showerFloor := TileLocationShowerFloor
	showerWall := TileLocationShowerWall
	catMap := map[designElement]*productSearchResult{
		{CategoryTile, TileLocationFloor}: {
			filters: ProductSearchFilters{
				Category:     CategoryTile,
				TileLocation: &floor,
			},
		},
		{CategoryTile, TileLocationWall}: {
			filters: ProductSearchFilters{
				Category:     CategoryTile,
				TileLocation: &wall,
			},
		},
		{CategoryTile, TileLocationShowerFloor}: {
			filters: ProductSearchFilters{
				Category:     CategoryTile,
				TileLocation: &showerFloor,
			},
		},
		{CategoryTile, TileLocationShowerWall}: {
			filters: ProductSearchFilters{
				Category:     CategoryTile,
				TileLocation: &showerWall,
			},
		},
		{CategoryPaint, TileLocationUnspecified}: {
			filters: ProductSearchFilters{
				Category: CategoryPaint,
			},
		},
		{CategoryToilet, TileLocationUnspecified}: {
			filters: ProductSearchFilters{
				Category: CategoryToilet,
			},
		},
		{CategoryVanity, TileLocationUnspecified}: {
			filters: ProductSearchFilters{
				Category: CategoryVanity,
			},
		},
		{CategoryFaucet, TileLocationUnspecified}: {
			filters: ProductSearchFilters{
				Category: CategoryFaucet,
			},
		},
		{CategoryMirror, TileLocationUnspecified}: {
			filters: ProductSearchFilters{
				Category: CategoryMirror,
			},
		},
		{CategoryLighting, TileLocationUnspecified}: {
			filters: ProductSearchFilters{
				Category: CategoryLighting,
			},
		},
		{CategoryShelving, TileLocationUnspecified}: {
			filters: ProductSearchFilters{
				Category: CategoryShelving,
			},
		},
		{CategoryWallpaper, TileLocationUnspecified}: {
			filters: ProductSearchFilters{
				Category: CategoryWallpaper,
			},
		},
	}

	// Create a channel to collect results
	resultsChan := make(chan *productSearchResult, len(catMap))
	var wg sync.WaitGroup
	// Launch goroutines for each category
	dg.logger.InfoContext(ctx, "Searching for products for AI design generation...",
		slog.String("projectId", design.ProjectID.String()))
	for _, result := range catMap {
		result.filters.Query = &userInput
		wg.Add(1)
		go dg.searchProductsForCategory(ctx, result.filters, resultsChan, &wg)
	}
	// Wait for all goroutines to complete and close the channel
	go func() {
		wg.Wait()
		close(resultsChan)
	}()
	if err := presenter.SendEvent(ctx, "update message", "Searching for products..."); err != nil {
		dg.logger.ErrorContext(ctx, "Failed to send update about searching for products",
			slog.String("error", err.Error()))
	}

	// Collect results
	var searchErrors []string
	for result := range resultsChan {
		if result.err != nil {
			dg.logger.ErrorContext(ctx, "Failed to search products for category",
				slog.String("category", string(result.filters.Category)),
				slog.String("error", result.err.Error()))
			if err := presenter.SendEvent(ctx, "error", result.err.Error()); err != nil {
				dg.logger.ErrorContext(ctx, "Failed to send error event",
					slog.String("error", err.Error()))
			}
			searchErrors = append(searchErrors, result.err.Error())
		}
		location := TileLocationUnspecified
		if result.filters.TileLocation != nil {
			location = *result.filters.TileLocation
		}
		catMap[designElement{result.filters.Category, location}] = result
		if len(result.productUUIDs) == 0 {
			err := presenter.SendEvent(ctx, "error",
				fmt.Sprintf("No %s %s found.", location, result.filters.Category))
			if err != nil {
				dg.logger.ErrorContext(ctx, "Failed to send error event",
					slog.String("error", err.Error()))
			}
			continue
		}
		productInfo, err := dg.catalog.ProductInfo(ctx,
			result.filters.Category.AsPathSegment(), result.productUUIDs[0])
		if err != nil {
			dg.logger.ErrorContext(ctx, "Failed to get product info from Catalog",
				slog.String("category", string(result.filters.Category)),
				slog.String("productId", result.productUUIDs[0].String()),
				slog.String("error", err.Error()))
			continue
		}
		result.topChoiceInfo = productInfo
		eventName := "product info"
		if location != TileLocationUnspecified {
			eventName = fmt.Sprintf("%s %s", location, eventName)
		}
		if err := presenter.SendEvent(ctx, eventName, productInfo); err != nil {
			dg.logger.ErrorContext(ctx, "Failed to send product search result event",
				slog.String("error", err.Error()))
		}
	}

	// If there were search errors, present the error and return
	if len(searchErrors) > 0 {
		dg.logger.ErrorContext(ctx, "Product Search failed for some categories",
			slog.Int("errorCount", len(searchErrors)))
	}

	dg.populateDesignWithProducts(&design, catMap)
	dg.logger.InfoContext(ctx, "Generated design via AI",
		slog.String("projectId", design.ProjectID.String()),
		slog.String("designId", design.ID.String()),
		slog.String("latency", time.Since(start).String()))
	if err := presenter.SendEvent(ctx, "design", design); err != nil {
		dg.logger.ErrorContext(ctx, "Failed to send design event",
			slog.String("error", err.Error()))
	}
	presenter.Close(ctx)
}

// searchProductsForCategory searches for products in a specific category and sends the result to the channel
func (dg *DesignGenerator) searchProductsForCategory(ctx context.Context, filters ProductSearchFilters,
	resultsChan chan<- *productSearchResult, wg *sync.WaitGroup) {

	defer wg.Done()
	dg.logger.DebugContext(ctx, "Searching for products..",
		slog.String("category", string(filters.Category)))
	productUUIDs, err := dg.productSearch.FindProductsViaAI(ctx, filters)

	if err != nil {
		dg.logger.ErrorContext(ctx, "Failed to search products for category",
			slog.String("category", string(filters.Category)),
			slog.String("error", err.Error()))
	} else if len(productUUIDs) == 0 {
		dg.logger.WarnContext(ctx, "No products found 😟",
			slog.String("category", string(filters.Category)))
	} else {
		dg.logger.InfoContext(ctx, "Successfully found products",
			slog.String("category", string(filters.Category)),
			slog.Int("productCount", len(productUUIDs)))
	}

	// Send result to channel
	resultsChan <- &productSearchResult{
		filters:      filters,
		productUUIDs: productUUIDs,
		err:          err,
	}
}

// populateDesignWithProducts populates the design with products from search results
func (dg *DesignGenerator) populateDesignWithProducts(design *Design, results map[designElement]*productSearchResult) {
	// Helper function to get first product UUID from results, if available
	getFirstProduct := func(category Category) *uuid.UUID {
		result, exists := results[designElement{category, TileLocationUnspecified}]
		if exists && len(result.productUUIDs) > 0 {
			return &result.productUUIDs[0]
		}
		return nil
	}
	design.Toilet = getFirstProduct(CategoryToilet)
	design.Mirror = getFirstProduct(CategoryMirror)
	design.Lighting = getFirstProduct(CategoryLighting)
	design.Paint = getFirstProduct(CategoryPaint)
	design.Shelving = getFirstProduct(CategoryShelving)
	design.Wallpaper = getFirstProduct(CategoryWallpaper)
	design.Faucet = getFirstProduct(CategoryFaucet)
	design.Vanity = getFirstProduct(CategoryVanity)

	getFirstTile := func(category Category, location TileLocation) *uuid.UUID {
		result, exists := results[designElement{category, location}]
		if exists && len(result.productUUIDs) > 0 {
			return &result.productUUIDs[0]
		}
		return nil
	}
	design.FloorTile = getFirstTile(CategoryTile, TileLocationFloor)
	design.WallTile = getFirstTile(CategoryTile, TileLocationWall)
	design.ShowerWallTile = getFirstTile(CategoryTile, TileLocationShowerWall)
	design.ShowerFloorTile = getFirstTile(CategoryTile, TileLocationShowerFloor)
}

// GenerateDesignsFromDesignPackages creates a slice of designs from design packages and room layout,
// then presents them using the provided DesignsPresenter
func (dg *DesignGenerator) GenerateDesignsFromDesignPackages(ctx context.Context,
	roomLayout entities.RoomLayout, designPkgs []DesignPackage, panoramicImagesForDesignPkgs map[uuid.UUID]url.URL,
	projectId entities.ProjectId, presenter DesignsPresenter) {

	if IsNil(presenter) {
		panic("presenter cannot be nil")
	}
	if len(designPkgs) == 0 {
		dg.logger.DebugContext(ctx, "No design packages provided for design generation")
		presenter.PresentDesigns(ctx, []Design{})
		return
	}
	dg.logger.DebugContext(ctx, "Generating designs from design packages",
		slog.Int("designPkgCount", len(designPkgs)),
		slog.String("roomLayoutId", roomLayout.Id.String()),
	)

	designs := make([]Design, 0, len(designPkgs))
	for _, designPkg := range designPkgs {
		design, err := dg.generateDesignFromDesignPackage(ctx, roomLayout, designPkg)
		if err != nil {
			dg.logger.ErrorContext(ctx, "Failed to generate design from design package",
				slog.String("designPkgId", designPkg.ID.String()),
				slog.String("error", err.Error()))
			presenter.PresentError(err)
			return
		}
		design.ProjectID = projectId
		if panoramicImagesForDesignPkgs == nil {
			designs = append(designs, design)
			continue
		}

		panoURL, ok := panoramicImagesForDesignPkgs[designPkg.ID]
		if ok {
			design.Renditions = []entities.Rendition{
				{URL: &panoURL, Status: entities.RenditionCompleted, CreatedAt: time.Now(), UpdatedAt: time.Now()},
			}
		} else {
			dg.logger.ErrorContext(ctx, "Failed to find panoramic image for design package",
				slog.String("designPkgId", designPkg.ID.String()))
		}
		designs = append(designs, design)
	}

	dg.logger.InfoContext(ctx, "Successfully generated designs from design packages",
		slog.Int("designCount", len(designs)))
	presenter.PresentDesigns(ctx, designs)
}

// GenerateDesignsForProjectFromDesignPackages fetches the design packages and project layout by IDs,
// then generates designs from those design packages using GenerateDesignsFromDesignPackages.
func (dg *DesignGenerator) GenerateDesignsForProjectFromDesignPackages(ctx context.Context,
	projectId entities.ProjectId, designPkgIDs []uuid.UUID, presenter DesignsPresenter) {

	if IsNil(presenter) {
		panic("presenter cannot be nil")
	}
	if len(designPkgIDs) == 0 {
		dg.logger.DebugContext(ctx, "No design package IDs provided for design generation")
		presenter.PresentDesigns(ctx, []Design{})
		return
	}
	dg.logger.InfoContext(ctx, "Generating designs for project from design packages",
		slog.String("projectId", projectId.String()),
		slog.Int("designPkgCount", len(designPkgIDs)))

	roomLayout, err := dg.monolith.GetLayoutForProject(ctx, projectId)
	if err != nil {
		dg.logger.ErrorContext(ctx, "Failed to fetch layout for project",
			slog.String("projectId", projectId.String()),
			slog.String("error", err.Error()))
		presenter.PresentError(err)
		return
	}
	designPkgs, err := dg.designPackageRepo.DesignPackagesById(ctx, designPkgIDs)
	if err != nil {
		dg.logger.ErrorContext(ctx, "Failed to fetch design packages",
			slog.String("error", err.Error()))
		presenter.PresentError(err)
		return
	}

	var panos map[uuid.UUID]url.URL
	defaultRoomLayout := dg.rooms.DefaultRoomLayout()
	if defaultRoomLayout.Hash != roomLayout.Hash {
		dg.logger.WarnContext(ctx, "Room layout is different from the default; skipping instant panos.")
		dg.GenerateDesignsFromDesignPackages(ctx, roomLayout, designPkgs, panos, projectId, presenter)
		return
	}

	panos, err = dg.designPackageRepo.PanoramicImagesForDesignPackages(ctx, designPkgIDs)
	if err != nil {
		dg.logger.ErrorContext(ctx, "Failed to fetch panoramic images for design packages",
			slog.String("error", err.Error()))
	}
	dg.GenerateDesignsFromDesignPackages(ctx, roomLayout, designPkgs, panos, projectId, presenter)
}

// generateDesignFromDesignPackage converts a single design package into a design based on the room layout
func (dg *DesignGenerator) generateDesignFromDesignPackage(ctx context.Context,
	roomLayout entities.RoomLayout, designPkg DesignPackage) (Design, error) {

	now := time.Now()
	design := NewEmptyDesign()
	design.Created = now
	design.LastUpdated = now
	design.WallpaperPlacement = designPkg.WallpaperPlacement
	design.WallTilePlacement = designPkg.WallTilePlacement
	design.DesignOptions = DesignOptions{
		ColorScheme:            &designPkg.ColorScheme,
		Style:                  &designPkg.Style,
		Title:                  sql.NullString{String: designPkg.Name, Valid: true},
		Description:            sql.NullString{String: designPkg.Description, Valid: true},
		FixedProductSelections: designPkg.FixedProductSelections,
	}

	dg.applyLayoutSpecificProducts(ctx, &design, roomLayout, designPkg)
	return design, nil
}

// applyLayoutSpecificProducts selects appropriate products based on room layout features
func (dg *DesignGenerator) applyLayoutSpecificProducts(ctx context.Context,
	design *Design, roomLayout entities.RoomLayout, designPkg DesignPackage) {

	if len(roomLayout.WetAreas) > 0 {
		design.ShowerWallTile = designPkg.ShowerWallTile
		wetArea := roomLayout.WetAreas[0]
		if len(wetArea.ShowerIds) > 0 {
			design.ShowerSystem = &designPkg.ShowerSystemSolo
		}

		switch wetArea.GlassType {
		case entities.SlidingShowerEnclosure:
			design.ShowerGlass = &designPkg.ShowerGlassSliding
		case entities.FixedShowerEnclosure:
			design.ShowerGlass = &designPkg.ShowerGlassFixed
		}

		if len(wetArea.AlcoveTubs) > 0 {
			dg.logger.DebugContext(ctx, "Alcove tub found in layout", slog.String("designId", design.ID.String()))
			fmt.Println("Alcove tub found in layout")
			design.Tub = &designPkg.AlcoveTub
			design.ShowerSystem = &designPkg.ShowerSystemCombo
			design.TubFiller = nil
			design.ShowerFloorTile = nil
			alcoveTub := wetArea.AlcoveTubs[0]
			switch alcoveTub.DoorType {
			case entities.SlidingShowerEnclosure:
				design.TubDoor = &designPkg.TubDoorSliding
			case entities.FixedShowerEnclosure:
				design.TubDoor = &designPkg.TubDoorFixed
			}
		} else if len(wetArea.FreestandingTubIds) > 0 {
			fmt.Println("Freestanding tub found in layout")
			design.Tub = &designPkg.FreestandingTub
			design.TubFiller = designPkg.TubFiller
			design.ShowerFloorTile = designPkg.ShowerFloorTile
		}
	}

	if len(roomLayout.Vanities) > 0 {
		vanity := roomLayout.Vanities[0]
		if vanity.MaxLength != nil {
			maxLengthInches := int(*vanity.MaxLength)
			// Find the largest vanity that fits within the max length
			var bestLength int
			var bestOption VanityScalingOption
			var found bool

			for length, scalingOption := range designPkg.VanityScalingOptions {
				if length <= maxLengthInches && length > bestLength {
					bestLength = length
					bestOption = scalingOption
					found = true
				}
			}

			if found {
				design.Vanity = &bestOption.VanityProductID
				design.Faucet = &bestOption.FaucetProductID
			}
		}
	}

	if dg.hasNiches(roomLayout) && designPkg.ShowerWallTile != nil {
		design.NicheTile = designPkg.ShowerWallTile
	}
}

func (dg *DesignGenerator) hasNiches(roomLayout entities.RoomLayout) bool {
	for _, wall := range roomLayout.Walls {
		if len(wall.NicheIds) > 0 {
			return true
		}
	}
	return false
}
