package usecases

import (
	"context"
	"fmt"
	"log"
	"math"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

func (de *DesignEvolver) evolveVanityProducts(ctx context.Context,
	design Design, layout entities.RoomLayout) (Design, error) {
	var err error

	if len(layout.Vanities) == 0 || layout.Vanities[0].MaxLength == nil || *layout.Vanities[0].MaxLength == 0 {
		design.Vanity = nil
		design.Faucet = nil
		design.Mirror = nil
		design.Lighting = nil
		return design, nil
	}

	maxVanityLengthInches := int(*layout.Vanities[0].MaxLength)
	if design.Vanity == nil {
		if design.Vanity, err = de.findVanity(ctx, maxVanityLengthInches, nil, nil); err != nil {
			return design, err
		}
	}
	vanity, err := de.catalog.ProductInfo(ctx, "vanities", *design.Vanity)
	if err != nil {
		return design, err
	}
	if vanity.category() != CategoryVanity {
		return design, fmt.Errorf("expected vanity type for %s but got %s", design.Vanity.String(), vanity.category())
	}
	currentVanity := vanity.(Vanity)
	if currentVanity.Length == 0 {
		return design, fmt.Errorf("no length found for vanity %s", design.Vanity.String())
	}
	if currentVanity.Length > float64(maxVanityLengthInches) {
		// TODO: try to preserve the number of sinks.
		fam := currentVanity.ProductFamilyName
		if design.Vanity, err = de.findVanity(ctx, maxVanityLengthInches,
			&fam, currentVanity.Color); err != nil {
			return design, err
		}
		vanity, err = de.catalog.ProductInfo(ctx, "vanities", *design.Vanity)
		if err != nil {
			return design, err
		}
		currentVanity = vanity.(Vanity)
	}

	if currentVanity.FaucetHoleSpacing == "" {
		return design, fmt.Errorf("no faucet hole spacing found for vanity %s", design.Vanity.String())
	}
	if design.Faucet == nil {
		holeSpacing := currentVanity.FaucetHoleSpacing
		faucetUUIDs, err := de.productSearch.FindProducts(ctx,
			fmt.Sprintf("category=Faucets&faucet_hole_spacing_compatibility=%s", holeSpacing))
		if err != nil {
			return design, err
		}
		if len(faucetUUIDs) == 0 {
			return design, fmt.Errorf("no faucets found with %s hole spacing", holeSpacing)
		}
		design.Faucet = &faucetUUIDs[0]
	}

	if currentVanity.NumberOfSinks == 0 {
		return design, fmt.Errorf("number of sinks not provided for vanity %s", design.Vanity.String())
	}
	if currentVanity.NumberOfSinks > 1 && currentVanity.SinkOffset < 0.001 {
		log.Printf("unrealistic sink offset found for vanity %s", design.Vanity.String())
		return design, fmt.Errorf("unrealistic sink offset found for vanity %s", design.Vanity.String())
	}
	maxRelativeProductLength := calculateMaxRelativeProductLength(
		currentVanity.NumberOfSinks, currentVanity.SinkOffset, currentVanity.Length)

	if design.Mirror == nil {
		if design.Mirror, err = de.findMirror(ctx, int(maxRelativeProductLength), nil, nil); err != nil {
			return design, err
		}
	} else {
		mirror, err := de.catalog.ProductInfo(ctx, "mirrors", *design.Mirror)
		if err != nil {
			return design, err
		}
		currentMirror := mirror.(Mirror)
		if currentMirror.Length > maxRelativeProductLength {
			fam := currentMirror.ProductFamilyName
			if design.Mirror, err = de.findMirror(ctx, int(maxRelativeProductLength),
				&fam, currentMirror.Color); err != nil {
				return design, err
			}
		}
	}
	if design.Lighting == nil {
		if design.Lighting, err = de.findLighting(ctx, int(maxRelativeProductLength), nil, nil); err != nil {
			return design, err
		}
	} else {
		lighting, err := de.catalog.ProductInfo(ctx, "lightings", *design.Lighting)
		if err != nil {
			return design, err
		}
		currentLighting := lighting.(Lighting)
		if currentLighting.Length > maxRelativeProductLength {
			fam := currentLighting.ProductFamilyName
			if design.Lighting, err = de.findLighting(ctx, int(maxRelativeProductLength),
				&fam, currentLighting.Color); err != nil {
				return design, err
			}
		}
	}

	return design, nil
}

func (de *DesignEvolver) findVanity(ctx context.Context,
	maxVanityLengthInches int, productFamilyName *string, color *ColorGroup) (*uuid.UUID, error) {

	urlQueryParams := fmt.Sprintf("category=Vanities&vanity_length_lte=%d", maxVanityLengthInches)
	target := fmt.Sprintf("vanity <= %d inches", maxVanityLengthInches)
	return de.findProduct(ctx, target, urlQueryParams, productFamilyName, color)
}

func (de *DesignEvolver) findMirror(ctx context.Context,
	maxMirrorWidthInches int, productFamilyName *string, color *ColorGroup) (*uuid.UUID, error) {

	urlQueryParams := fmt.Sprintf("category=Mirror&mirror_width_lte=%d", maxMirrorWidthInches)
	target := fmt.Sprintf("mirror <= %d inches", maxMirrorWidthInches)
	return de.findProduct(ctx, target, urlQueryParams, productFamilyName, color)
}

func (de *DesignEvolver) findLighting(ctx context.Context,
	maxLightingLengthInches int, productFamilyName *string, color *ColorGroup) (*uuid.UUID, error) {

	urlQueryParams := fmt.Sprintf("category=Decorative%%20Lighting&lighting_length_lte=%d", maxLightingLengthInches)
	target := fmt.Sprintf("decorative lighting <= %d inches", maxLightingLengthInches)
	return de.findProduct(ctx, target, urlQueryParams, productFamilyName, color)
}

func calculateMaxRelativeProductLength(numberOfSinks uint8, sinkOffset float64, vanityLength float64) float64 {
	sinkOffsetAbs := math.Abs(sinkOffset)
	if numberOfSinks == 1 {
		return vanityLength
	}
	return math.Min(sinkOffsetAbs, (vanityLength/2)-sinkOffsetAbs) * 2
}
