package usecases

import (
	"context"
	"fmt"
	"strconv"

	"github.com/google/uuid"
)

func (de *DesignEvolver) evolveShowerProducts(ctx context.Context,
	design Design, hasAlcoveTub, hasWalkinShower bool, maxShowerGlassLength float64) (Design, error) {

	if !hasAlcoveTub && !hasWalkinShower {
		design.ShowerWallTile = nil
		design.ShowerShortWallTile = nil
		design.NicheTile = nil
		design.ShowerSystem = nil
		return design, nil
	}
	if hasAlcoveTub && hasWalkinShower {
		return design, fmt.Errorf("both alcove & walk-in shower found in layout")
	}

	if design.FloorTile == nil {
		return design, fmt.Errorf("no floor tile found in design %s", design.ID.String())
	}
	floorTile, err := de.catalog.ProductInfo(ctx, "tiles", *design.FloorTile)
	if err != nil {
		return design, err
	}
	if floorTile.category() != CategoryTile {
		return design, fmt.Errorf("expected tile type for %s but got %s", design.FloorTile.String(), floorTile.category())
	}
	currentFloorTile := floorTile.(Tile)

	if hasAlcoveTub {
		if design.ShowerSystem, err = de.addOrReplaceShowerSystem(ctx, design.ShowerSystem, true); err != nil {
			return design, err
		}
	} else if hasWalkinShower {
		if design.ShowerSystem, err = de.addOrReplaceShowerSystem(ctx, design.ShowerSystem, false); err != nil {
			return design, err
		}
		if design.ShowerFloorTile == nil {
			if currentFloorTile.AvailableForShowerFloor != nil && *currentFloorTile.AvailableForShowerFloor {
				design.ShowerFloorTile = design.FloorTile
			} else if design.ShowerFloorTile, err = de.findTile(ctx,
				"shower_floor", &currentFloorTile.ProductFamilyName, currentFloorTile.Color); err != nil {
				return design, err
			}
		}
		if design.ShowerGlass == nil && maxShowerGlassLength >= 56 {
			if design.ShowerGlass, err = de.findShowerEnclosure(ctx, "Shower%%20Glass", maxShowerGlassLength); err != nil {
				return design, err
			}
		}
	} else {
		return design, fmt.Errorf("layout has seemingly impossible wet area configuration")
	}

	if design.ShowerWallTile == nil {
		if design.WallTile != nil {
			wallTile, err := de.catalog.ProductInfo(ctx, "tiles", *design.WallTile)
			if err != nil {
				return design, err
			}
			if wallTile.category() != CategoryTile {
				return design, fmt.Errorf("expected tile type for %s but got %s", design.WallTile.String(), wallTile.category())
			}
			currentWallTile := wallTile.(Tile)
			if currentWallTile.AvailableForShowerWall != nil && *currentWallTile.AvailableForShowerWall {
				design.ShowerWallTile = design.WallTile
			} else if design.ShowerWallTile, err = de.findTile(ctx,
				"shower_wall", &currentWallTile.ProductFamilyName, currentWallTile.Color); err != nil {
				return design, err
			}
		} else if currentFloorTile.AvailableForShowerWall != nil && *currentFloorTile.AvailableForShowerWall {
			design.ShowerWallTile = design.FloorTile
		} else if design.ShowerWallTile, err = de.findTile(ctx,
			"shower_wall", &currentFloorTile.ProductFamilyName, currentFloorTile.Color); err != nil {
			return design, err
		}
	}

	return design, nil
}

func (de *DesignEvolver) evolveTubProducts(ctx context.Context,
	design Design, hasAlcoveTub, hasFreestandingTub bool, maxTubDoorLength float64) (Design, error) {

	if !hasAlcoveTub && !hasFreestandingTub {
		design.Tub = nil
		design.TubDoor = nil
		design.TubFiller = nil
		return design, nil
	}

	if hasAlcoveTub && hasFreestandingTub {
		return design, fmt.Errorf("both alcove & freestanding tubs found in layout")
	}

	var err error
	var tubType TubType
	if hasAlcoveTub {
		tubType = Alcove
		design.TubFiller = nil
		if maxTubDoorLength < 56 {
			design.TubDoor = nil
		} else if design.TubDoor == nil {
			if design.TubDoor, err = de.findShowerEnclosure(ctx, "Tub%20Doors", maxTubDoorLength); err != nil {
				return design, err
			}
		}
	} else if hasFreestandingTub {
		tubType = Freestanding
		design.TubDoor = nil
		if design.TubFiller == nil {
			var color *ColorGroup
			if design.ShowerSystem != nil {
				currentShowerSystemInfo, err := de.catalog.ProductInfo(ctx, "shower-systems", *design.ShowerSystem)
				if err != nil {
					return design, err
				}
				currentShowerSystem := currentShowerSystemInfo.(Shower)
				if currentShowerSystem.Color != nil {
					color = currentShowerSystem.Color
				}
			} else if design.Faucet != nil {
				currentFaucetInfo, err := de.catalog.ProductInfo(ctx, "faucets", *design.Faucet)
				if err != nil {
					return design, err
				}
				currentFaucet := currentFaucetInfo.(Faucet)
				if currentFaucet.Color != nil {
					color = currentFaucet.Color
				}
			}
			if design.TubFiller, err = de.findProduct(ctx, "floor-mounted tub fillers",
				"category=Tub%%20Filler&tub_filler_mounting_position=FLOOR", nil, color); err != nil {
				return design, err
			}
		}
	} else {
		return design, fmt.Errorf("seemingly impossible tub configuration in layout")
	}

	var currentTub Tub
	if design.Tub != nil {
		tub, err := de.catalog.ProductInfo(ctx, "tubs", *design.Tub)
		if err != nil {
			return design, err
		}
		currentTub = tub.(Tub)
		if currentTub.Type == "" {
			return design, fmt.Errorf("no type found for tub %s", design.Tub.String())
		}
	}
	if design.Tub == nil || currentTub.Type != tubType {
		if design.Tub, err = de.findProduct(ctx,
			string(tubType)+" tubs", fmt.Sprintf("category=Tubs&tub_type=%s", tubType), nil, nil); err != nil {
			return design, err
		}
	}

	return design, nil
}

func (de *DesignEvolver) addOrReplaceShowerSystem(ctx context.Context,
	ssId *uuid.UUID, needTubSpout bool) (*uuid.UUID, error) {

	var familyName *string
	var color *ColorGroup
	needHandShower := false
	if ssId != nil {
		shower, err := de.catalog.ProductInfo(ctx, "shower-systems", *ssId)
		if err != nil {
			return nil, err
		}
		if shower.category() != CategoryShower {
			return nil, fmt.Errorf("expected shower system type for %s but got %s", ssId.String(), shower.category())
		}
		currentShowerSystem := shower.(Shower)
		if currentShowerSystem.HasTubSpout == needTubSpout {
			return ssId, nil
		}
		familyName = &currentShowerSystem.ProductFamilyName
		color = currentShowerSystem.Color
		needHandShower = *currentShowerSystem.HasHandheldShowerhead
	}
	// We shouldn't have to compromise on handshower kit inclusion.
	urlQueryParams := fmt.Sprintf("category=Shower%%20Systems&shower_has_tub_spout=%s&handshower_kit_included=%s",
		strconv.FormatBool(needTubSpout), strconv.FormatBool(needHandShower))
	return de.findProduct(ctx, "suitable shower systems", urlQueryParams, familyName, color)
}

func (de *DesignEvolver) findShowerEnclosure(ctx context.Context,
	enclosureCategory string, maxLen float64) (*uuid.UUID, error) {

	var enclosureTypeFilter string
	if maxLen > 59.625 {
		enclosureTypeFilter = "&shower_enclosure_type=Fixed"
	}
	urlQueryParams := fmt.Sprintf("category=%s%s", enclosureCategory, enclosureTypeFilter)
	return de.findProduct(ctx, enclosureCategory, urlQueryParams, nil, nil)
}

func (de *DesignEvolver) findTile(ctx context.Context,
	location string, productFamilyName *string, color *ColorGroup) (*uuid.UUID, error) {

	urlQueryParams := fmt.Sprintf("category=Tile&tile_location=%s", location)
	target := fmt.Sprintf("tile for the %s", location)
	return de.findProduct(ctx, target, urlQueryParams, productFamilyName, color)
}
