name: Build and Deploy

on:
  push:
    branches:
      - main
  workflow_dispatch:

jobs:
  build-and-deploy:
    uses: bond-studio-ai/shared-workflows/.github/workflows/build-and-deploy.yml@main
    secrets: inherit
    with:
      runtime: go
      aws-ecs-cluster: BackendCluster
      aws-ecs-service: room-design-service
      pulumi-aws-environment: bond/aws/prod
      cache-path: |
        .cache
      build-args: |
        -o server
