package main

import (
	"context"
	"encoding/json"
	"log"
	"log/slog"
	"net/http"
	"net/url"
	"os"
	"strings"

	"github.com/alexflint/go-arg"
	"github.com/amacneil/dbmate/v2/pkg/dbmate"
	"github.com/openai/openai-go"
	"github.com/santhosh-tekuri/jsonschema/v6"

	_ "github.com/amacneil/dbmate/v2/pkg/driver/postgres"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/frameworks/db"
	"gitlab.com/arc-studio-ai/services/room-design/frameworks/web"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

const (
	jsonSchemaFilename        = "room-design.schema.json"
	dbName                    = "projects"
	defaultRoomLayoutFilename = "default_room_layout.json"
)

type Command string

const (
	CmdSrv Command = "srv"
)

type args struct {
	PgHost     string `arg:"env" default:"alpha-database.cin8ix3jru5g.us-west-2.rds.amazonaws.com"`
	PgDatabase string `arg:"env" default:"alpha-prototype"`
	ApiHost    string `arg:"env:BASE_API_HOSTNAME" default:"api.averyapi.com"`
	CdnHost    string `arg:"env:BASE_CDN_HOSTNAME" default:"cdn.arcstudio.ai"`
	Cmd        string `arg:"positional" default:"srv"`
}

func main() {
	ctx := context.Background()
	logger := setupLogger()

	schema := controllers.Schema(jsonSchemaFilename)
	defaultRoomLayout, err := os.ReadFile(defaultRoomLayoutFilename)
	if err != nil {
		log.Fatalf("Error reading default room layout JSON file: %v", err)
	}

	var args args
	arg.MustParse(&args)

	web.InitTracing()
	defer web.StopTracing()

	var pgPassword string
	if args.PgHost == "localhost" {
		pgPassword = ""
	} else if pgPassword, err = db.GetPostgresPassword(ctx); err != nil {
		log.Fatal(err)
	}

	u := &url.URL{
		Scheme: "postgres",
		User:   url.UserPassword("postgres", pgPassword), // Automatically handles encoding
		Host:   args.PgHost,
		Path:   "/" + dbName,
	}
	if args.PgHost == "localhost" {
		u.RawQuery = "sslmode=disable"
	}
	dbm := dbmate.New(u)
	dbm.MigrationsDir = []string{"frameworks/db"}
	dbm.SchemaFile = "adapters/gateways/schema.sql"
	logger.Info("Running DB migrations",
		slog.String("host", args.PgHost), slog.String("database", dbName))
	if err = dbm.CreateAndMigrate(); err != nil {
		log.Fatalf("Unable to run migrations: %v", err)
	}
	pgConnStr := u.String()

	switch Command(args.Cmd) {
	case CmdSrv:
		relDb := db.NewRelationalDb(ctx, pgConnStr, logger, nil)
		defer relDb.Close()

		catalog := gateways.NewCatalog(args.ApiHost)
		textGenerator := gateways.NewOpenAI(openai.NewClient())
		maker := usecases.NewDesignCreater(relDb, relDb, catalog, textGenerator, logger)
		updater := usecases.NewDesignUpdater(relDb)
		saver := usecases.NewDesignSaver(relDb, catalog, textGenerator, logger)
		proseGenerator := usecases.NewDesignProseRegenerator(relDb, catalog, textGenerator, logger)
		monolith := gateways.NewMonolith(args.ApiHost)
		bulkReplacer := usecases.NewBulkDesignReplacer(relDb, monolith, logger)
		eraser := usecases.NewDesignDeleter(relDb)
		productSearch := gateways.NewProductSearch(args.ApiHost)
		evolver := usecases.NewDesignEvolver(catalog, productSearch, relDb, logger)
		designRetriever := usecases.NewDesignRetriever(relDb)
		cmdController := controllers.NewDesignWriteController(maker, updater, saver, proseGenerator, bulkReplacer,
			eraser, evolver, designRetriever, logger)
		projectIdRetriever := usecases.NewProjectIdRetriever(relDb)
		defaultScan := gateways.NewRooms(defaultRoomLayout)
		designGenerator := usecases.NewDesignGenerator(relDb, monolith, defaultScan, productSearch, catalog, textGenerator, logger)
		projectSynthesizer := usecases.NewProjectSynthesizer(relDb, monolith)
		designQueryController := controllers.NewDesignRetrievalController(designRetriever, projectIdRetriever, projectSynthesizer, designGenerator)

		designPackageRetriever := usecases.NewDesignPackageRetriever(relDb)
		designPackageRetrievalController := controllers.NewDesignPackageRetrievalController(designPackageRetriever, designGenerator)
		designPackageCreator := usecases.NewDesignPackageCreator(relDb, logger)
		designPackageWriteController := controllers.NewDesignPackageWriteController(designPackageCreator, logger)
		renditionRetriever := usecases.NewRenditionRetriever(relDb)
		renditionRetrievalController := controllers.NewRenditionRetrievalController(renditionRetriever)
		renditionMaker := usecases.NewRenditionCreator(relDb, logger)
		renditionSaver := usecases.NewRenditionSaver(relDb)
		renditionUpdater := usecases.NewRenditionUpdater(relDb, textGenerator, relDb, catalog, logger)
		renditionDeleter := usecases.NewRenditionDeleter(relDb)
		renditionWriteController := controllers.NewRenditionWriteController(logger,
			renditionMaker, renditionSaver, renditionUpdater, renditionDeleter)

		presetRetriever := usecases.NewPresetRetriever(relDb)
		presetRetrievalController := controllers.NewPresetRetrievalController(presetRetriever)

		// Cart inclusion use cases and controllers
		cartInclusionSaver := usecases.NewCartInclusionReplacer(relDb, logger)
		cartInclusionDeleter := usecases.NewCartInclusionDeleter(relDb, logger)
		cartInclusionMerger := usecases.NewCartInclusionMerger(relDb, logger)
		cartInclusionRetriever := usecases.NewCartInclusionRetriever(relDb, logger)
		cartInclusionWriteController := controllers.NewCartInclusionWriteController(cartInclusionSaver, cartInclusionDeleter, cartInclusionMerger, logger)
		cartInclusionRetrievalController := controllers.NewCartInclusionRetrievalController(cartInclusionRetriever)

		startSrv(logger, schema, monolith, designQueryController, cmdController,
			renditionRetrievalController, renditionWriteController,
			defaultRoomLayout, args.CdnHost, presetRetrievalController,
			designPackageRetrievalController, designPackageWriteController,
			cartInclusionRetrievalController, cartInclusionWriteController)

	default:
		log.Fatalf("Unknown command: %s", args.Cmd)
	}
}

func setupLogger() *slog.Logger {
	logFormat := os.Getenv("LOG_FORMAT")
	if logFormat == "" {
		logFormat = "json"
	}

	logLevel := slog.LevelInfo
	if levelStr := os.Getenv("LOG_LEVEL"); levelStr != "" {
		switch strings.ToLower(levelStr) {
		case "debug":
			logLevel = slog.LevelDebug
		case "info":
			logLevel = slog.LevelInfo
		case "warn":
			logLevel = slog.LevelWarn
		case "error":
			logLevel = slog.LevelError
		}
	}

	opts := &slog.HandlerOptions{
		Level:     logLevel,
		AddSource: logLevel == slog.LevelDebug,
	}

	var handler slog.Handler
	switch logFormat {
	case "text":
		handler = slog.NewTextHandler(os.Stderr, opts)
	default:
		handler = slog.NewJSONHandler(os.Stderr, opts)
	}

	logger := slog.New(handler)
	slog.SetDefault(logger) // Set the global logger, since this is main.
	return logger
}

func startSrv(logger *slog.Logger, schema *jsonschema.Schema, monolith *gateways.Monolith,
	designQueryController *controllers.DesignRetrievalController, cmdController *controllers.DesignWriteController,
	renditionQueryController *controllers.RenditionRetrievalController, renditionWriter *controllers.RenditionWriteController,
	defaultRoomLayout json.RawMessage, baseCdnHostname string, presetRetrievalController *controllers.PresetRetrievalController,
	designPackageRetrievalController *controllers.DesignPackageRetrievalController, designPackageWriteController *controllers.DesignPackageWriteController,
	cartInclusionRetrievalController *controllers.CartInclusionRetrievalController, cartInclusionWriteController *controllers.CartInclusionWriteController) {

	mux := web.NewServeMux()

	designPackageViewHandler := web.NewDesignPackageQueryHandler(logger, designPackageRetrievalController)
	designPackageWriteHandler := web.NewDesignPackageWriteHandler(logger, designPackageWriteController)
	// Register handlers under new /design-packages routes
	web.RegisterDesignPackageHandlers(mux, "/studio/v1/design-packages", designPackageViewHandler, designPackageWriteHandler)
	// Keep existing /v2/templates route for backwards compatibility
	web.RegisterDesignPackageHandlers(mux, "/v2/templates", designPackageViewHandler, designPackageWriteHandler)
	web.RegisterDesignPackageHandlers(mux, "/studio/v1/templates", designPackageViewHandler, designPackageWriteHandler)
	writeHandler := web.NewCommandHandler(logger, schema, cmdController, designQueryController, monolith)
	designViewHandler := web.NewDesignQueryHandler(logger, designQueryController)
	web.RegisterGlobalHandlers(mux, designViewHandler)
	web.RegisterDesignHandlers(mux, "", designViewHandler, writeHandler)
	web.RegisterDesignHandlers(mux, "/studio/v1", designViewHandler, writeHandler)
	renditionViewHandler := web.NewRenditionQueryHandler(logger, renditionQueryController, baseCdnHostname)
	renditionWriteHandler := web.NewRenditionWriteHandler(logger, renditionWriter)
	web.RegisterRenditionHandlers(mux, "/studio/v1", renditionViewHandler, renditionWriteHandler)
	presetViewHandler := web.NewPresetQueryHandler(logger, defaultRoomLayout, presetRetrievalController)
	web.RegisterPresetHandlers(mux, "/studio/v1", presetViewHandler)

	projectHandler := web.NewProjectHandler(designQueryController, logger)
	web.RegisterProjectHandlers(mux, projectHandler)

	cartInclusionQueryHandler := web.NewCartInclusionQueryHandler(logger, cartInclusionRetrievalController)
	cartInclusionWriteHandler := web.NewCartInclusionWriteHandler(logger, cartInclusionWriteController)
	web.RegisterCartInclusionHandlers(mux, "/studio/v1/designs/{designId}", cartInclusionQueryHandler, cartInclusionWriteHandler)

	// Create middleware chain with correlation and logging, excluding health check from logs
	middleware := web.StandardMiddlewareWithExclusions(logger, []string{"/healthz"}).
		Use(web.CORSMiddleware()).
		Use(web.SecurityHeadersMiddleware()).
		Use(web.RequestSizeLimitMiddleware(10 * 1024 * 1024)) // 10MB limit

	// Wrap the mux with middleware
	handler := middleware.Then(mux)

	logger.Info("Starting server", slog.String("port", ":8080"))
	if err := http.ListenAndServe(":8080", handler); err != nil {
		log.Fatal(err)
	}
}
