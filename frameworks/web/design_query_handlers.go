package web

import (
	"fmt"
	"log/slog"
	"net/http"
	"strings"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/presenters"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

type DesignQueryHandler struct {
	logger           *slog.Logger
	designController *controllers.DesignRetrievalController
}

func NewDesignQueryHandler(logger *slog.Logger, dc *controllers.DesignRetrievalController) *DesignQueryHandler {
	if dc == nil {
		panic("design controller cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &DesignQueryHandler{logger: logger, designController: dc}
}

func (h *DesignQueryHandler) HandleListDesignsForProject(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	dp := presenters.NewDesignsPresenter(h.logger, w)
	projectId := r.PathValue("projectId")
	useLegacyIds := !r.URL.Query().Has("useUUIDs")
	h.designController.FetchAllDesignsForProject(ctx, entities.NewProjectId(projectId), dp, useLegacyIds)
}

func (h *DesignQueryHandler) HandleGetSpecifiedDesign(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	designId := r.PathValue("designId")
	designUUID, err := uuid.Parse(designId)
	if err != nil {
		http.Error(w, "Invalid design UUID", http.StatusBadRequest)
		return
	}
	dp := presenters.NewDesignsPresenter(h.logger, w)
	h.designController.FetchDesign(ctx, designUUID, dp)
}

func (h *DesignQueryHandler) HandleGetProjectIdForDesign(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	designId := r.PathValue("designId")
	designUUID, err := uuid.Parse(designId)
	if err != nil {
		http.Error(w, "Invalid design UUID", http.StatusBadRequest)
		return
	}
	presenter := presenters.NewProjectIdPresenter(h.logger, w)
	h.designController.GetProjectIdForDesign(ctx, designUUID, presenter)
}

func (h *DesignQueryHandler) HandleGetAllDesignsForMultipleProjects(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	dp := presenters.NewDesignsPresenter(h.logger, w)
	h.logger.DebugContext(ctx, "Handling GET request for multiple projects...", slog.String("url", r.URL.RawQuery))
	projectIdsParam := r.URL.Query().Get("ids")
	if projectIdsParam == "" {
		http.Error(w, "No project IDs provided", http.StatusBadRequest)
		return
	}
	h.logger.DebugContext(ctx, "Designs for multiple projects requested", slog.String("projectIds", projectIdsParam))
	var projectIds []entities.ProjectId
	for id := range strings.SplitSeq(projectIdsParam, ",") {
		projectId := entities.NewProjectId(id)
		if !projectId.IsValid() {
			http.Error(w, fmt.Sprintf("Invalid project ID: '%v'.", projectId.String()), http.StatusBadRequest)
			return
		}
		projectIds = append(projectIds, projectId)
	}
	h.logger.DebugContext(ctx, "Fetching designs for multiple projects", slog.Int("count", len(projectIds)))
	h.designController.FetchDesignsForMultipleProjects(ctx, projectIds, dp)
}

func (h *DesignQueryHandler) HandleGenerateDesignViaAI(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	projectIdStr := r.PathValue("projectId")
	if projectIdStr == "" {
		http.Error(w, "projectId is required in path", http.StatusBadRequest)
		return
	}
	projectId := entities.NewProjectId(projectIdStr)

	userInput := r.URL.Query().Get("userInput")
	if userInput == "" {
		http.Error(w, "userInput is a required query parameter", http.StatusBadRequest)
		return
	}
	h.logger.InfoContext(ctx, "Handling request to generate designs for project via AI...",
		slog.String("projectId", projectIdStr), slog.String("userInput", userInput))

	presenter := presenters.NewDesignStreamer(h.logger, w)
	h.designController.GenerateDesignForProjectViaAI(ctx, projectId, userInput, presenter)
}
