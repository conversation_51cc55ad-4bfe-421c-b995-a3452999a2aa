package web

import (
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net/http"
	"strings"

	"github.com/google/uuid"
	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/presenters"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

type DesignPackageQueryHandler struct {
	logger                  *slog.Logger
	designPackageController *controllers.DesignPackageRetrievalController
}

func NewDesignPackageQueryHandler(logger *slog.Logger, designPackageController *controllers.DesignPackageRetrievalController) *DesignPackageQueryHandler {
	if logger == nil {
		logger = slog.Default()
	}
	return &DesignPackageQueryHandler{
		logger:                  logger,
		designPackageController: designPackageController,
	}
}

func (h *DesignPackageQueryHandler) HandleGetDesignPackage(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	designPkgID := r.PathValue("templateId")
	useLegacyIds := !r.URL.Query().Has("useUUIDs")
	presenter := presenters.NewDesignPackagesPresenter(h.logger, w, useLegacyIds)

	if len(designPkgID) == 2 {
		h.designPackageController.FetchDesignPackageByLegacyId(ctx, designPkgID, presenter)
		return
	}

	designPkgUUID, err := uuid.Parse(designPkgID)
	if err != nil {
		http.Error(w, "Invalid design package ID: must be either a 2-character legacy ID or a valid UUID", http.StatusBadRequest)
		return
	}
	h.designPackageController.FetchDesignPackage(ctx, designPkgUUID, presenter)
}

func (h *DesignPackageQueryHandler) HandleGetAllDesignPackages(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	useLegacyIds := !r.URL.Query().Has("useUUIDs")
	presenter := presenters.NewDesignPackagesPresenter(h.logger, w, useLegacyIds)
	h.designPackageController.FetchAllDesignPackages(ctx, presenter)
}

func (h *DesignPackageQueryHandler) HandleGenerateDesignsFromDesignPackages(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()

	projectIdStr := r.PathValue("projectId")
	if projectIdStr == "" {
		http.Error(w, "projectId is required in path", http.StatusBadRequest)
		return
	}
	projectId := entities.NewProjectId(projectIdStr)
	idsQueryParam := r.URL.Query().Get("templateIds")
	if idsQueryParam == "" {
		http.Error(w, "templateIds are required (via 'templateIds' query parameter)", http.StatusBadRequest)
		return
	}
	h.logger.InfoContext(ctx, "Handling request to generate designs for project from design packages...",
		slog.String("projectId", projectIdStr), slog.String("templateIds", idsQueryParam))

	idStrings := strings.Split(idsQueryParam, ",")
	designPkgIDs := make([]uuid.UUID, 0, len(idStrings))
	for _, idStr := range idStrings {
		id, err := uuid.Parse(idStr)
		if err != nil {
			http.Error(w, fmt.Sprintf("Invalid design package UUID in 'ids' query parameter: %s", idStr), http.StatusBadRequest)
			return
		}
		designPkgIDs = append(designPkgIDs, id)
	}
	if len(designPkgIDs) == 0 {
		http.Error(w, "templateIds must not be empty", http.StatusBadRequest)
		return
	}

	presenter := presenters.NewDesignsPresenter(h.logger, w)
	h.designPackageController.GenerateDesignsFromDesignPackages(ctx, projectId, designPkgIDs, presenter)
}

// DesignPackageWriteHandler handles design package write operations
type DesignPackageWriteHandler struct {
	logger     *slog.Logger
	controller *controllers.DesignPackageWriteController
}

func NewDesignPackageWriteHandler(logger *slog.Logger, controller *controllers.DesignPackageWriteController) *DesignPackageWriteHandler {
	if logger == nil {
		logger = slog.Default()
	}
	return &DesignPackageWriteHandler{
		logger:     logger,
		controller: controller,
	}
}

func (h *DesignPackageWriteHandler) parseDesignPackage(r *http.Request) (adapters.DesignPackage, error) {
	var designPkg adapters.DesignPackage
	body, err := io.ReadAll(r.Body)
	if err != nil {
		return designPkg, fmt.Errorf("error reading request body: %w", err)
	}
	if len(body) == 0 {
		return designPkg, fmt.Errorf("empty request body")
	}
	var payload map[string]any
	err = json.Unmarshal(body, &payload)
	if err != nil {
		return designPkg, fmt.Errorf("invalid JSON payload: %w", err)
	}
	// TODO: Add schema validation
	if err := json.Unmarshal(body, &designPkg); err != nil {
		return designPkg, err
	}
	return designPkg, nil
}

func (h *DesignPackageWriteHandler) HandlePutDesignPackage(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	h.logger.InfoContext(ctx, "Handling PUT design package request...")

	designPkgID := r.PathValue("templateId")
	designPkgUUID, err := uuid.Parse(designPkgID)
	if err != nil {
		http.Error(w, "Invalid design package UUID", http.StatusBadRequest)
		return
	}

	presenter := presenters.NewDesignPackageCreationOutcomePresenter(h.logger, w)
	designPkg, err := h.parseDesignPackage(r)
	if err != nil {
		h.logger.ErrorContext(ctx, "Error validating/parsing design package payload", slog.String("error", err.Error()))
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	if err := designPkg.AlignId(designPkgID); err != nil {
		http.Error(w, "Design package ID in payload does not match URL", http.StatusBadRequest)
		return
	}

	h.controller.SaveDesignPackage(ctx, designPkgUUID, designPkg, presenter)
}
