package web_test

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"net/http"
	"net/http/httptest"
	"net/url"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/frameworks/web"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

const (
	jsonSchemaFilename = "room-design.schema.json"
	projId             = "PRJ-FOOBAR"
)

type testHandlers struct {
	writeHandler *web.CommandHandler
	readHandler  *web.DesignQueryHandler
	memStore     *gateways.FakeRelDb
	fakeMonolith *gateways.FakeMonolith
}

func setup(t *testing.T) *testHandlers {
	t.Helper()
	logger := slog.Default()
	schema := controllers.Schema(jsonSchemaFilename)
	r := gateways.NewFakeRelDb()
	catalog := gateways.NewFakeCatalog()
	designFetcher := usecases.NewDesignRetriever(r)
	projectIdRetriever := usecases.NewProjectIdRetriever(r)
	projectSynthesizer := usecases.NewProjectSynthesizer(r, gateways.NewFakeMonolith())
	ai := gateways.NewFakeLLM()
	generator := usecases.NewDesignGenerator(r, gateways.NewFakeMonolith(), gateways.NewRooms([]byte(`{}`)),
		gateways.NewFakeProductSearch(), catalog, ai, logger)
	queryController := controllers.NewDesignRetrievalController(designFetcher, projectIdRetriever, projectSynthesizer, generator)
	readHandler := web.NewDesignQueryHandler(logger, queryController)
	designCreator := usecases.NewDesignCreater(r, r, catalog, ai, logger)
	designUpdater := usecases.NewDesignUpdater(r)
	designSaver := usecases.NewDesignSaver(r, catalog, ai, logger)
	proseRegenerator := usecases.NewDesignProseRegenerator(r, catalog, ai, logger)
	fakeMonolith := gateways.NewFakeMonolith()
	bulkDesignReplacer := usecases.NewBulkDesignReplacer(r, fakeMonolith, logger)
	designDeleter := usecases.NewDesignDeleter(r)
	productSearch := gateways.NewFakeProductSearch()
	designEvolver := usecases.NewDesignEvolver(catalog, productSearch, r, logger)
	cmdController := controllers.NewDesignWriteController(
		designCreator, designUpdater, designSaver, proseRegenerator, bulkDesignReplacer, designDeleter,
		designEvolver, designFetcher, logger)
	writeHandler := web.NewCommandHandler(logger, schema, cmdController, queryController, fakeMonolith)
	return &testHandlers{writeHandler, readHandler, r, fakeMonolith}
}

func genDesign() adapters.Design {
	floorTile := uuid.NewString()
	toilet := uuid.NewString()
	vanity := uuid.NewString()
	faucet := uuid.NewString()
	mirror := uuid.NewString()
	title := "Test Design"
	return adapters.Design{
		FloorTile: &floorTile,
		Toilet:    &toilet,
		Vanity:    &vanity,
		Faucet:    &faucet,
		Mirror:    &mirror,
		Title:     &title,
	}
}

// Helper functions for common test operations
func makeJSONRequest(t *testing.T, method, url string, payload interface{}) *http.Request {
	t.Helper()
	data, err := json.Marshal(payload)
	require.NoError(t, err)
	return httptest.NewRequest(method, url, bytes.NewReader(data))
}

func assertHTTPStatus(t *testing.T, recorder *httptest.ResponseRecorder, expectedStatus int) {
	t.Helper()
	assert.Equal(t, expectedStatus, recorder.Code, "HTTP status mismatch")
}

func assertDesignCount(t *testing.T, memStore *gateways.FakeRelDb, projectId entities.ProjectId, expectedCount int) {
	t.Helper()
	designs, err := memStore.DesignsForProject(context.Background(), projectId)
	require.NoError(t, err)
	assert.Len(t, designs, expectedCount, "Design count mismatch")
}

func createAndStoreDesign(t *testing.T, memStore *gateways.FakeRelDb, design adapters.Design, projectId entities.ProjectId) {
	t.Helper()
	ucDesign, err := design.ToUsecaseDesign(projectId)
	require.NoError(t, err)
	_, err = memStore.UpsertDesign(context.Background(), ucDesign)
	require.NoError(t, err)
}

func testCRUDOperation(t *testing.T, handlers *testHandlers, design adapters.Design, operation string) {
	t.Helper()
	switch operation {
	case "CREATE":
		req := makeJSONRequest(t, "POST", fmt.Sprintf("/projects/%s/designs", projId), design)
		req.SetPathValue("projectId", projId)
		recorder := httptest.NewRecorder()
		handlers.writeHandler.HandlePost(recorder, req)
		assertHTTPStatus(t, recorder, http.StatusCreated)
		assertDesignCount(t, handlers.memStore, projId, 1)
	case "READ":
		req := httptest.NewRequest("GET", fmt.Sprintf("/projects/%s/designs", projId), nil)
		req.SetPathValue("projectId", projId)
		recorder := httptest.NewRecorder()
		handlers.readHandler.HandleListDesignsForProject(recorder, req)
		assertHTTPStatus(t, recorder, http.StatusOK)
	}
}

func TestDesignCRUDOperations(t *testing.T) {
	handlers := setup(t)

	t.Run("adding new design", func(t *testing.T) {
		testDesign := genDesign()
		testCRUDOperation(t, handlers, testDesign, "CREATE")
		testCRUDOperation(t, handlers, testDesign, "READ")
	})

	t.Run("adding minimal design", func(t *testing.T) {
		handlers := setup(t) // Fresh setup for this test
		testDesign := adapters.Design{}
		testCRUDOperation(t, handlers, testDesign, "CREATE")
		testCRUDOperation(t, handlers, testDesign, "READ")
	})
}

func TestChangingDesign(t *testing.T) {
	handlers := setup(t)
	testDesign := genDesign()
	testDesign.ID = uuid.NewString()
	createAndStoreDesign(t, handlers.memStore, testDesign, projId)

	// Update design
	vanityWall := usecases.VanityWall
	testDesign.WallpaperPlacement = &vanityWall

	url := fmt.Sprintf("/projects/%s/designs/%s", projId, testDesign.ID)
	req := makeJSONRequest(t, "PUT", url, testDesign)
	req.SetPathValue("projectId", projId)
	req.SetPathValue("designId", testDesign.ID)
	recorder := httptest.NewRecorder()
	handlers.writeHandler.HandlePut(recorder, req)
	assertHTTPStatus(t, recorder, http.StatusOK)

	// Verify update
	req = httptest.NewRequest("GET", url, nil)
	req.SetPathValue("projectId", projId)
	req.SetPathValue("designId", testDesign.ID)
	recorder = httptest.NewRecorder()
	handlers.readHandler.HandleGetSpecifiedDesign(recorder, req)
	assertHTTPStatus(t, recorder, http.StatusOK)

	var design adapters.Design
	require.NoError(t, json.Unmarshal(recorder.Body.Bytes(), &design))
	assert.Equal(t, vanityWall, *design.WallpaperPlacement)
}

func TestRemovingDesign(t *testing.T) {
	handlers := setup(t)
	testDesign := genDesign()
	testDesign.ID = uuid.NewString()
	createAndStoreDesign(t, handlers.memStore, testDesign, projId)

	url := fmt.Sprintf("/projects/%s/designs/%s", projId, testDesign.ID)
	req := httptest.NewRequest("DELETE", url, nil)
	req.SetPathValue("projectId", projId)
	req.SetPathValue("designId", testDesign.ID)
	recorder := httptest.NewRecorder()
	handlers.writeHandler.HandleDelete(recorder, req)
	assertHTTPStatus(t, recorder, http.StatusNoContent)

	// Verify deletion
	req = httptest.NewRequest("GET", url, nil)
	req.SetPathValue("projectId", projId)
	req.SetPathValue("designId", testDesign.ID)
	recorder = httptest.NewRecorder()
	handlers.readHandler.HandleGetSpecifiedDesign(recorder, req)
	assertHTTPStatus(t, recorder, http.StatusNotFound)
}

func TestRetrievingDesignsForMultipleProjects(t *testing.T) {
	handlers := setup(t)
	projId2 := entities.NewProjectId("PRJ-FOOBAR2")

	// Create designs for both projects
	testDesign := genDesign()
	testDesign.ID = uuid.NewString()
	createAndStoreDesign(t, handlers.memStore, testDesign, projId)

	testDesign2 := genDesign()
	testDesign2.ID = uuid.NewString()
	createAndStoreDesign(t, handlers.memStore, testDesign2, projId2)

	// Test retrieving designs for multiple projects
	params := url.Values{}
	params.Add("ids", fmt.Sprintf("%s,%s", projId, projId2))
	req := httptest.NewRequest("GET", fmt.Sprintf("/projects/designs?%s", params.Encode()), nil)
	recorder := httptest.NewRecorder()
	handlers.readHandler.HandleGetAllDesignsForMultipleProjects(recorder, req)
	assertHTTPStatus(t, recorder, http.StatusMultiStatus)

	// Test with non-existent project
	params.Set("ids", fmt.Sprintf("%s,%s", projId, "PRJ-PHANTOM"))
	req = httptest.NewRequest("GET", fmt.Sprintf("/projects/designs?%s", params.Encode()), nil)
	recorder = httptest.NewRecorder()
	handlers.readHandler.HandleGetAllDesignsForMultipleProjects(recorder, req)
	assertHTTPStatus(t, recorder, http.StatusMultiStatus)
}

func TestAddingMultipleDesignsForProject(t *testing.T) {
	handlers := setup(t)
	testDesign := genDesign()
	testDesign.ID = uuid.NewString()
	testDesign2 := genDesign()
	testDesign2.ID = uuid.NewString()

	url := fmt.Sprintf("/projects/%s/designs", projId)
	req := makeJSONRequest(t, "PUT", url, []adapters.Design{testDesign, testDesign2})
	req.SetPathValue("projectId", projId)
	recorder := httptest.NewRecorder()
	handlers.writeHandler.HandlePutAllDesignsForProject(recorder, req)
	assertHTTPStatus(t, recorder, http.StatusNoContent)
	assertDesignCount(t, handlers.memStore, projId, 2)
}

func createTestLayoutPayload() map[string]any {
	return map[string]any{
		"scan": map[string]any{
			"floors": []map[string]any{
				{
					"identifier": uuid.NewString(),
					"position":   map[string]float64{"x": 0, "y": 0, "z": 0},
					"rotation":   map[string]float64{"x": 0, "y": 0, "z": 0},
					"shape": []map[string]float64{
						{"x": 0, "y": 0}, {"x": 10, "y": 0}, {"x": 10, "y": 10}, {"x": 0, "y": 10},
					},
				},
			},
			"walls": []map[string]any{}, "ceilings": []map[string]any{}, "doors": []map[string]any{},
			"lights": []map[string]any{}, "linenCabinets": []map[string]any{}, "mirrors": []map[string]any{},
			"niches": []map[string]any{}, "openings": []map[string]any{}, "robeHooks": []map[string]any{},
			"shelves": []map[string]any{}, "showerSystems": []map[string]any{}, "sinks": []map[string]any{},
			"toilets": []map[string]any{}, "towelBars": []map[string]any{}, "towelRings": []map[string]any{},
			"tpHolders": []map[string]any{}, "tubFillers": []map[string]any{}, "tubs": []map[string]any{},
			"vanities": []map[string]any{}, "windows": []map[string]any{},
			"areas": map[string]any{"showers": []map[string]any{}},
		},
		"measurements": map[string]any{"floorArea": 100.0},
	}
}

func TestEvolveDesignsForLayout(t *testing.T) {
	handlers := setup(t)

	// Add test designs
	testDesign1 := genDesign()
	testDesign1.ID = uuid.NewString()
	createAndStoreDesign(t, handlers.memStore, testDesign1, projId)

	testDesign2 := genDesign()
	testDesign2.ID = uuid.NewString()
	createAndStoreDesign(t, handlers.memStore, testDesign2, projId)

	// Test evolve endpoint
	url := fmt.Sprintf("/projects/%s/designs/evolve", projId)
	req := makeJSONRequest(t, "POST", url, createTestLayoutPayload())
	req.SetPathValue("projectId", projId)
	recorder := httptest.NewRecorder()
	handlers.writeHandler.EvolveProjectDesignsForNewLayout(recorder, req)
	assertHTTPStatus(t, recorder, http.StatusOK)

	// Verify response
	var evolvedDesigns []adapters.Design
	require.NoError(t, json.Unmarshal(recorder.Body.Bytes(), &evolvedDesigns))
	assert.Len(t, evolvedDesigns, 2, "Should return evolved designs")

	// Verify monolith was called
	calls := handlers.fakeMonolith.GetUpdateScanAndMeasurementsCalls()
	assert.Len(t, calls, 1, "Should call monolith once")
	if len(calls) > 0 {
		assert.Equal(t, entities.ProjectId(projId), calls[0].ProjectId)
	}
}

func TestHandlePatch(t *testing.T) {
	handlers := setup(t)
	testDesign := genDesign()
	testDesign.ID = uuid.NewString()
	createAndStoreDesign(t, handlers.memStore, testDesign, projId)

	t.Run("field updates", func(t *testing.T) {
		newTitle := "Updated Title"
		newColorScheme := usecases.Bold
		patchPayload := map[string]any{
			"id":          testDesign.ID,
			"title":       newTitle,
			"colorScheme": newColorScheme,
		}

		url := fmt.Sprintf("/projects/%s/designs/%s", projId, testDesign.ID)
		req := makeJSONRequest(t, "PATCH", url, patchPayload)
		req.SetPathValue("projectId", projId)
		req.SetPathValue("designId", testDesign.ID)
		recorder := httptest.NewRecorder()
		handlers.writeHandler.HandlePatch(recorder, req)
		assertHTTPStatus(t, recorder, http.StatusOK)

		// Verify update
		req = httptest.NewRequest("GET", url, nil)
		req.SetPathValue("projectId", projId)
		req.SetPathValue("designId", testDesign.ID)
		recorder = httptest.NewRecorder()
		handlers.readHandler.HandleGetSpecifiedDesign(recorder, req)

		var updatedDesign adapters.Design
		require.NoError(t, json.Unmarshal(recorder.Body.Bytes(), &updatedDesign))
		assert.Equal(t, newTitle, *updatedDesign.Title)
		assert.Equal(t, newColorScheme, *updatedDesign.ColorScheme)
	})

	t.Run("field removals", func(t *testing.T) {
		patchPayload := map[string]any{
			"id":       testDesign.ID,
			"faucet":   nil,
			"lighting": nil,
		}

		url := fmt.Sprintf("/projects/%s/designs/%s", projId, testDesign.ID)
		req := makeJSONRequest(t, "PATCH", url, patchPayload)
		req.SetPathValue("projectId", projId)
		req.SetPathValue("designId", testDesign.ID)
		recorder := httptest.NewRecorder()
		handlers.writeHandler.HandlePatch(recorder, req)
		assertHTTPStatus(t, recorder, http.StatusOK)
	})

}

func TestHandlePatchErrors(t *testing.T) {
	handlers := setup(t)
	testDesign := genDesign()
	testDesign.ID = uuid.NewString()
	createAndStoreDesign(t, handlers.memStore, testDesign, projId)

	testCases := []struct {
		name           string
		payload        interface{}
		designId       string
		expectedStatus int
		expectedBody   string
	}{
		{
			name:           "empty request body",
			payload:        "",
			designId:       testDesign.ID,
			expectedStatus: http.StatusBadRequest,
			expectedBody:   "empty request body",
		},
		{
			name:           "invalid JSON",
			payload:        "invalid json",
			designId:       testDesign.ID,
			expectedStatus: http.StatusBadRequest,
			expectedBody:   "invalid JSON payload",
		},
		{
			name: "design ID mismatch",
			payload: map[string]any{
				"id":    uuid.NewString(),
				"title": "Updated Title",
			},
			designId:       testDesign.ID,
			expectedStatus: http.StatusBadRequest,
			expectedBody:   "design ID mismatch",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			var req *http.Request
			if tc.payload == "" {
				req = httptest.NewRequest("PATCH", fmt.Sprintf("/projects/%s/designs/%s", projId, tc.designId), bytes.NewReader([]byte{}))
			} else if str, ok := tc.payload.(string); ok {
				req = httptest.NewRequest("PATCH", fmt.Sprintf("/projects/%s/designs/%s", projId, tc.designId), bytes.NewReader([]byte(str)))
			} else {
				req = makeJSONRequest(t, "PATCH", fmt.Sprintf("/projects/%s/designs/%s", projId, tc.designId), tc.payload)
			}
			req.SetPathValue("projectId", projId)
			req.SetPathValue("designId", tc.designId)
			recorder := httptest.NewRecorder()
			handlers.writeHandler.HandlePatch(recorder, req)
			assertHTTPStatus(t, recorder, tc.expectedStatus)
			if tc.expectedBody != "" {
				assert.Contains(t, recorder.Body.String(), tc.expectedBody)
			}
		})
	}

	// Test 404 case separately with matching IDs
	t.Run("non-existent design with matching ID", func(t *testing.T) {
		nonExistentId := uuid.NewString()
		payload := map[string]any{
			"id":    nonExistentId,
			"title": "Updated Title",
		}
		req := makeJSONRequest(t, "PATCH", fmt.Sprintf("/projects/%s/designs/%s", projId, nonExistentId), payload)
		req.SetPathValue("projectId", projId)
		req.SetPathValue("designId", nonExistentId)
		recorder := httptest.NewRecorder()
		handlers.writeHandler.HandlePatch(recorder, req)
		assertHTTPStatus(t, recorder, http.StatusNotFound)
	})
}

func TestHandleGetProjectIdForDesign(t *testing.T) {
	fakeDb := gateways.NewFakeRelDb()
	designRetriever := usecases.NewDesignRetriever(fakeDb)
	projectIdRetriever := usecases.NewProjectIdRetriever(fakeDb)
	designGenerator := usecases.NewDesignGenerator(fakeDb, gateways.NewFakeMonolith(), gateways.NewRooms([]byte(`{}`)),
		gateways.NewFakeProductSearch(), gateways.NewFakeCatalog(), gateways.NewFakeLLM(), nil)
	projSynth := usecases.NewProjectSynthesizer(fakeDb, gateways.NewFakeMonolith())
	controller := controllers.NewDesignRetrievalController(designRetriever, projectIdRetriever, projSynth, designGenerator)
	handler := web.NewDesignQueryHandler(nil, controller)

	// Create test design
	projectId := entities.NewProjectId("PRJ-12345")
	designId := uuid.New()
	testDesign := usecases.Design{
		ID:                 designId,
		ProjectID:          projectId,
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.NoWallpaper,
		WallTilePlacement:  usecases.NoWallTile,
	}
	_, err := fakeDb.UpsertDesign(context.Background(), testDesign)
	require.NoError(t, err)

	testCases := []struct {
		name           string
		designId       string
		expectedStatus int
		expectedBody   string
	}{
		{
			name:           "valid design ID",
			designId:       designId.String(),
			expectedStatus: http.StatusOK,
			expectedBody:   `"projectId":"PRJ-12345"`,
		},
		{
			name:           "invalid design ID",
			designId:       "invalid-uuid",
			expectedStatus: http.StatusBadRequest,
			expectedBody:   "Invalid design UUID",
		},
		{
			name:           "non-existent design ID",
			designId:       uuid.New().String(),
			expectedStatus: http.StatusNotFound,
			expectedBody:   usecases.ErrNotFound.Error(),
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			req := httptest.NewRequest(http.MethodGet, "/designs/"+tc.designId+"/project", nil)
			req.SetPathValue("designId", tc.designId)
			recorder := httptest.NewRecorder()
			handler.HandleGetProjectIdForDesign(recorder, req)
			assertHTTPStatus(t, recorder, tc.expectedStatus)
			assert.Contains(t, recorder.Body.String(), tc.expectedBody)
		})
	}
}
