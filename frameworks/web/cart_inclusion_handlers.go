package web

import (
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net/http"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/presenters"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// CartInclusionsQueryHandler handles cart inclusion read operations
type CartInclusionsQueryHandler struct {
	logger     *slog.Logger
	controller *controllers.CartInclusionRetrievalController
}

func NewCartInclusionQueryHandler(logger *slog.Logger, controller *controllers.CartInclusionRetrievalController) *CartInclusionsQueryHandler {
	if controller == nil {
		panic("controller cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &CartInclusionsQueryHandler{
		logger:     logger,
		controller: controller,
	}
}

// HandleGetAllCartInclusionsForDesign handles GET requests to retrieve all cart inclusions for a specific design.
// Route: GET /studio/v1/designs/{designId}/cart-inclusions
// Returns: JSON array of cart inclusions with their product IDs, locations, include flags, and quantity differences.
func (h *CartInclusionsQueryHandler) HandleGetAllCartInclusionsForDesign(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	designId := r.PathValue("designId")
	designUUID, err := uuid.Parse(designId)
	if err != nil {
		http.Error(w, "Invalid design UUID", http.StatusBadRequest)
		return
	}

	h.logger.DebugContext(ctx, "Fetching cart inclusions for design",
		slog.String("designId", designUUID.String()))

	presenter := presenters.NewCartInclusionsPresenter(h.logger, w)
	h.controller.FetchCartInclusions(ctx, designUUID, presenter)
}

// CartInclusionsWriteHandler handles cart inclusion write operations
type CartInclusionsWriteHandler struct {
	logger     *slog.Logger
	controller *controllers.CartInclusionWriteController
}

func NewCartInclusionWriteHandler(logger *slog.Logger, controller *controllers.CartInclusionWriteController) *CartInclusionsWriteHandler {
	if controller == nil {
		panic("controller cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &CartInclusionsWriteHandler{
		logger:     logger,
		controller: controller,
	}
}

// HandlePut handles PUT requests to completely replace all cart inclusions for a design.
// Route: PUT /studio/v1/designs/{designId}/cart-inclusions
// Body: JSON array of cart inclusions to set as the complete collection
// Behavior: Replaces all existing inclusions with the provided set (full replacement).
func (h *CartInclusionsWriteHandler) HandlePut(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	designId := r.PathValue("designId")
	designUUID, err := uuid.Parse(designId)
	if err != nil {
		http.Error(w, "Invalid design UUID", http.StatusBadRequest)
		return
	}

	body, err := io.ReadAll(r.Body)
	if err != nil {
		http.Error(w, fmt.Sprintf("Error reading request body: %s", err.Error()), http.StatusBadRequest)
		return
	}
	if len(body) == 0 {
		http.Error(w, "Empty request body", http.StatusBadRequest)
		return
	}

	// Parse as array of cart inclusions and convert to map
	var inclusionsArray []adapters.CartInclusion
	err = json.Unmarshal(body, &inclusionsArray)
	if err != nil {
		http.Error(w, fmt.Sprintf("Error parsing payload: %s", err.Error()), http.StatusBadRequest)
		return
	}

	// Convert array to map with composite keys
	inclusions := adapters.ConvertCartInclusionsArrayToMap(inclusionsArray)

	h.logger.DebugContext(ctx, "Updating cart inclusions",
		slog.String("designId", designUUID.String()),
		slog.Int("inclusionCount", len(inclusions)))

	presenter := presenters.NewOutcomePresenter(h.logger, w)
	h.controller.ReplaceCartInclusions(ctx, designUUID, inclusions, presenter)
}

// HandlePatch handles PATCH requests for partial cart inclusion updates.
// Route: PATCH /studio/v1/designs/{designId}/cart-inclusions
// Body: JSON array of cart inclusions to partially update
// Behavior: Merges provided inclusions with existing ones (additive operation).
func (h *CartInclusionsWriteHandler) HandlePatch(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	designId := r.PathValue("designId")
	designUUID, err := uuid.Parse(designId)
	if err != nil {
		http.Error(w, "Invalid design ID", http.StatusBadRequest)
		return
	}

	body, err := io.ReadAll(r.Body)
	if err != nil {
		http.Error(w, "Error reading request body", http.StatusBadRequest)
		return
	}

	// Parse as array of cart inclusions and convert to map
	var inclusionsArray []adapters.CartInclusion
	err = json.Unmarshal(body, &inclusionsArray)
	if err != nil {
		http.Error(w, fmt.Sprintf("Error parsing payload: %s", err.Error()), http.StatusBadRequest)
		return
	}

	// Convert array to map with composite keys
	inclusions := adapters.ConvertCartInclusionsArrayToMap(inclusionsArray)

	h.logger.DebugContext(ctx, "Partially updating cart inclusions",
		slog.String("designId", designUUID.String()),
		slog.Int("inclusionCount", len(inclusions)))

	presenter := presenters.NewOutcomePresenter(h.logger, w)
	h.controller.MergeCartInclusions(ctx, designUUID, inclusions, presenter)
}

// HandleDelete handles DELETE requests to remove cart inclusions.
// Routes:
//   - DELETE /studio/v1/designs/{designId}/cart-inclusions - clears all cart inclusions for the design
//   - DELETE /studio/v1/designs/{designId}/cart-inclusions/{productId}/{location} - removes specific inclusion
//
// Behavior: If no productId and location provided, clears all inclusions; otherwise removes the specific product/location combination.
func (h *CartInclusionsWriteHandler) HandleDelete(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	designId := r.PathValue("designId")
	designUUID, err := uuid.Parse(designId)
	if err != nil {
		http.Error(w, "Invalid design UUID", http.StatusBadRequest)
		return
	}

	productId := r.PathValue("productId")
	location := r.PathValue("location")

	if productId == "" {
		// No product ID means clear all inclusions
		h.logger.DebugContext(ctx, "Clearing all cart inclusions",
			slog.String("designId", designUUID.String()))

		presenter := presenters.NewOutcomePresenter(h.logger, w)
		h.controller.ClearCartInclusions(ctx, designUUID, presenter)
		return
	}

	productUUID, err := uuid.Parse(productId)
	if err != nil {
		http.Error(w, "Invalid product UUID", http.StatusBadRequest)
		return
	}

	if location == "" {
		http.Error(w, "Location parameter is required", http.StatusBadRequest)
		return
	}

	h.logger.DebugContext(ctx, "Deleting cart inclusion",
		slog.String("designId", designUUID.String()),
		slog.String("productId", productUUID.String()),
		slog.String("location", location))

	presenter := presenters.NewOutcomePresenter(h.logger, w)
	h.controller.DeleteCartInclusion(ctx, designUUID, productUUID, usecases.Location(location), presenter)
}
