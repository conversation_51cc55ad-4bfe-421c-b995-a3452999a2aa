package web

import (
	"context"
	"crypto/rand"
	"encoding/hex"
	"log/slog"
	"net/http"
	"time"

	"github.com/google/uuid"
)

// Context keys for request correlation
type contextKey string

const (
	RequestIDKey     contextKey = "request_id"
	CorrelationIDKey contextKey = "correlation_id"
	UserIDKey        contextKey = "user_id"
	SessionIDKey     contextKey = "session_id"
)

// HTTP headers for correlation
const (
	HeaderRequestID     = "X-Request-ID"
	HeaderCorrelationID = "X-Correlation-ID"
	HeaderUserID        = "X-User-ID"
	HeaderSessionID     = "X-Session-ID"
)

// CorrelationContext holds correlation information for a request
type CorrelationContext struct {
	RequestID     string
	CorrelationID string
	UserID        string
	SessionID     string
	StartTime     time.Time
}

// NewCorrelationContext creates a new correlation context
func NewCorrelationContext() *CorrelationContext {
	return &CorrelationContext{
		RequestID:     generateRequestID(),
		CorrelationID: generateCorrelationID(),
		StartTime:     time.Now(),
	}
}

// generateRequestID creates a unique request ID
func generateRequestID() string {
	return "req_" + uuid.New().String()[:8]
}

// generateCorrelationID creates a unique correlation ID
func generateCorrelationID() string {
	bytes := make([]byte, 8)
	if _, err := rand.Read(bytes); err != nil {
		// Fallback to UUID if random generation fails
		return "corr_" + uuid.New().String()[:8]
	}
	return "corr_" + hex.EncodeToString(bytes)
}

// WithCorrelationContext adds correlation context to the given context
func WithCorrelationContext(ctx context.Context, corrCtx *CorrelationContext) context.Context {
	ctx = context.WithValue(ctx, RequestIDKey, corrCtx.RequestID)
	ctx = context.WithValue(ctx, CorrelationIDKey, corrCtx.CorrelationID)
	if corrCtx.UserID != "" {
		ctx = context.WithValue(ctx, UserIDKey, corrCtx.UserID)
	}
	if corrCtx.SessionID != "" {
		ctx = context.WithValue(ctx, SessionIDKey, corrCtx.SessionID)
	}
	return ctx
}

// GetCorrelationContext extracts correlation context from the given context
func GetCorrelationContext(ctx context.Context) *CorrelationContext {
	corrCtx := &CorrelationContext{}

	if requestID, ok := ctx.Value(RequestIDKey).(string); ok {
		corrCtx.RequestID = requestID
	}
	if correlationID, ok := ctx.Value(CorrelationIDKey).(string); ok {
		corrCtx.CorrelationID = correlationID
	}
	if userID, ok := ctx.Value(UserIDKey).(string); ok {
		corrCtx.UserID = userID
	}
	if sessionID, ok := ctx.Value(SessionIDKey).(string); ok {
		corrCtx.SessionID = sessionID
	}

	return corrCtx
}

// CorrelationMiddleware is HTTP middleware that adds correlation context to requests
func CorrelationMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		corrCtx := NewCorrelationContext()

		// Check for existing correlation ID in headers
		if existingCorrelationID := r.Header.Get(HeaderCorrelationID); existingCorrelationID != "" {
			corrCtx.CorrelationID = existingCorrelationID
		}

		// Extract user and session information from headers if present
		if userID := r.Header.Get(HeaderUserID); userID != "" {
			corrCtx.UserID = userID
		}
		if sessionID := r.Header.Get(HeaderSessionID); sessionID != "" {
			corrCtx.SessionID = sessionID
		}

		// Add correlation context to request context
		ctx := WithCorrelationContext(r.Context(), corrCtx)
		r = r.WithContext(ctx)

		// Add correlation headers to response
		w.Header().Set(HeaderRequestID, corrCtx.RequestID)
		w.Header().Set(HeaderCorrelationID, corrCtx.CorrelationID)

		// Call next handler
		next.ServeHTTP(w, r)
	})
}

// LoggingMiddleware is HTTP middleware that logs requests with correlation context
func LoggingMiddleware(logger *slog.Logger) func(http.Handler) http.Handler {
	return LoggingMiddlewareWithExclusions(logger, nil)
}

// LoggingMiddlewareWithExclusions is HTTP middleware that logs requests with correlation context,
// excluding specified paths from logging
func LoggingMiddlewareWithExclusions(logger *slog.Logger, excludePaths []string) func(http.Handler) http.Handler {
	// Create a map for faster lookup
	excludeMap := make(map[string]bool)
	for _, path := range excludePaths {
		excludeMap[path] = true
	}

	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			start := time.Now()
			corrCtx := GetCorrelationContext(r.Context())

			// Check if this path should be excluded from logging
			shouldLog := !excludeMap[r.URL.Path]

			// Create a response writer wrapper to capture status code
			wrapped := &responseWriter{ResponseWriter: w, statusCode: http.StatusOK}

			// Log request start (only if not excluded)
			if shouldLog {
				logArgs := []any{
					slog.String("method", r.Method),
					slog.String("path", r.URL.Path),
					slog.String("remote_addr", r.RemoteAddr),
					slog.String("user_agent", r.UserAgent()),
					slog.String("request_id", corrCtx.RequestID),
					slog.String("correlation_id", corrCtx.CorrelationID),
				}

				// Add query parameters if present
				if r.URL.RawQuery != "" {
					logArgs = append(logArgs, slog.String("query_params", r.URL.RawQuery))
				}

				logger.InfoContext(r.Context(), "Request started", logArgs...)
			}

			// Call next handler
			next.ServeHTTP(wrapped, r)

			// Log request completion (only if not excluded)
			if shouldLog {
				duration := time.Since(start)
				logArgs := []any{
					slog.String("method", r.Method),
					slog.String("path", r.URL.Path),
					slog.Int("status_code", wrapped.statusCode),
					slog.Duration("duration", duration),
					slog.String("request_id", corrCtx.RequestID),
					slog.String("correlation_id", corrCtx.CorrelationID),
				}

				// Add query parameters if present
				if r.URL.RawQuery != "" {
					logArgs = append(logArgs, slog.String("query_params", r.URL.RawQuery))
				}

				logger.InfoContext(r.Context(), "Request completed", logArgs...)
			}
		})
	}
}

// responseWriter wraps http.ResponseWriter to capture status code
type responseWriter struct {
	http.ResponseWriter
	statusCode int
}

func (rw *responseWriter) WriteHeader(code int) {
	rw.statusCode = code
	rw.ResponseWriter.WriteHeader(code)
}

// Flush implements http.Flusher interface if the underlying ResponseWriter supports it
func (rw *responseWriter) Flush() {
	if flusher, ok := rw.ResponseWriter.(http.Flusher); ok {
		flusher.Flush()
	}
}
