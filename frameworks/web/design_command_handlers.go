package web

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"log/slog"
	"net/http"

	"github.com/cacticloud/cactikit/tools/pretty"
	"github.com/google/uuid"
	"github.com/santhosh-tekuri/jsonschema/v6"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/presenters"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type monolith interface {
	UpdateScanAndMeasurementsForProject(ctx context.Context,
		projectId entities.ProjectId, scan json.RawMessage, measurements json.RawMessage) error
}

type CommandHandler struct {
	logger          *slog.Logger
	schema          *jsonschema.Schema
	controller      *controllers.DesignWriteController
	queryController *controllers.DesignRetrievalController
	monolith        monolith
}

func NewCommandHandler(logger *slog.Logger, schema *jsonschema.Schema,
	c *controllers.DesignWriteController, qc *controllers.DesignRetrievalController, monolith monolith) *CommandHandler {

	if schema == nil {
		panic("schema cannot be nil")
	}
	if c == nil {
		panic("controller cannot be nil")
	}
	if qc == nil {
		panic("query controller cannot be nil")
	}
	if usecases.IsNil(monolith) {
		panic("monolith cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &CommandHandler{schema: schema, logger: logger, controller: c, queryController: qc, monolith: monolith}
}

func (h *CommandHandler) parseDesign(r *http.Request, validate bool) (adapters.Design, error) {
	var design adapters.Design
	body, err := io.ReadAll(r.Body)
	if err != nil {
		return design, fmt.Errorf("error reading request body: %w", err)
	}
	if len(body) == 0 {
		return design, fmt.Errorf("empty request body")
	}
	var payload map[string]any
	err = json.Unmarshal(body, &payload)
	if err != nil {
		return design, fmt.Errorf("invalid JSON payload: %w", err)
	}
	if validate {
		if err := h.schema.Validate(payload); err != nil {
			h.logger.ErrorContext(r.Context(), "Invalid design",
				slog.String("design", fmt.Sprintf("%#v", payload)))
			return design, err
		}
	}
	if err := json.Unmarshal(body, &design); err != nil {
		return design, err
	}
	h.logger.InfoContext(r.Context(), "Parsed design",
		slog.String("projectId", r.PathValue("projectId")),
		slog.String("designId", design.ID))
	return design, nil
}

func (h *CommandHandler) HandlePost(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	h.logger.InfoContext(ctx, "Handling POST request to create design...")
	presenter := presenters.NewDesignMutationOutcomePresenter(h.logger, w)
	design, err := h.parseDesign(r, true)
	if err != nil {
		h.logger.ErrorContext(ctx, "Error validating/parsing design payload", slog.String("error", err.Error()))
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}
	projectId := entities.NewProjectId(r.PathValue("projectId"))
	if design.ID != "" {
		h.logger.WarnContext(ctx, "Attempt to create design with specified ID",
			slog.String("designId", design.ID), slog.String("projectId", projectId.String()))
	}
	h.controller.SaveDesign(ctx, projectId, design, presenter)
}

func (h *CommandHandler) HandlePut(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	h.logger.InfoContext(ctx, "Handling PUT request to replace design...")
	presenter := presenters.NewDesignMutationOutcomePresenter(h.logger, w)
	design, err := h.parseDesign(r, true)
	if err != nil {
		h.logger.ErrorContext(ctx, "Error validating/parsing design payload", slog.String("error", err.Error()))
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}
	if err := design.AlignId(r.PathValue("designId")); err != nil {
		http.Error(w, "Design ID in payload does not match URL", http.StatusBadRequest)
		return
	}
	projectId := entities.NewProjectId(r.PathValue("projectId"))
	h.controller.SaveDesign(ctx, projectId, design, presenter)
}

func (h *CommandHandler) parseDiff(r *http.Request) (usecases.DesignDiff, error) {
	var diff usecases.DesignDiff
	body, err := io.ReadAll(r.Body)
	if err != nil {
		return diff, fmt.Errorf("error reading request body: %w", err)
	}
	if len(body) == 0 {
		return diff, fmt.Errorf("empty request body")
	}
	var payload map[string]any
	err = json.Unmarshal(body, &payload)
	if err != nil {
		return diff, fmt.Errorf("invalid JSON payload: %w", err)
	}
	h.logger.DebugContext(r.Context(), "Parsing design diff",
		slog.String("designId", r.PathValue("designId")), slog.String("payload", pretty.ToJSON(payload)))
	var design adapters.Design
	if err := json.Unmarshal(body, &design); err != nil {
		return diff, err
	}
	if err := design.AlignId(r.PathValue("designId")); err != nil {
		return diff, err
	}
	diff, err = design.ToUsecaseDesignDiff()
	if err != nil {
		return diff, err
	}

	if val, ok := payload["faucet"]; ok && val == nil {
		diff.Removals["faucet"] = nil
	}
	if val, ok := payload["floorTile"]; ok && val == nil {
		diff.Removals["floorTile"] = nil
	}
	if val, ok := payload["lighting"]; ok && val == nil {
		diff.Removals["lighting"] = nil
	}
	if val, ok := payload["mirror"]; ok && val == nil {
		diff.Removals["mirror"] = nil
	}
	if val, ok := payload["paint"]; ok && val == nil {
		diff.Removals["paint"] = nil
	}
	if val, ok := payload["shelves"]; ok && val == nil {
		diff.Removals["shelves"] = nil
	}
	if val, ok := payload["showerFloorTile"]; ok && val == nil {
		diff.Removals["showerFloorTile"] = nil
	}
	if val, ok := payload["showerSystem"]; ok && val == nil {
		diff.Removals["showerSystem"] = nil
	}
	if val, ok := payload["showerWallTile"]; ok && val == nil {
		diff.Removals["showerWallTile"] = nil
	}
	if val, ok := payload["toilet"]; ok && val == nil {
		diff.Removals["toilet"] = nil
	}
	if val, ok := payload["tub"]; ok && val == nil {
		diff.Removals["tub"] = nil
	}
	if val, ok := payload["tubDoor"]; ok && val == nil {
		diff.Removals["tubDoor"] = nil
	}
	if val, ok := payload["vanity"]; ok && val == nil {
		diff.Removals["vanity"] = nil
	}
	if val, ok := payload["wallpaper"]; ok && val == nil {
		diff.Removals["wallpaper"] = nil
	}
	if val, ok := payload["wallTile"]; ok && val == nil {
		diff.Removals["wallTile"] = nil
	}
	return diff, nil
}

func (h *CommandHandler) HandlePatch(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	h.logger.DebugContext(ctx, "Handling PATCH request to modify design...")
	presenter := presenters.NewDesignMutationOutcomePresenter(h.logger, w)
	diff, err := h.parseDiff(r)
	if err != nil {
		h.logger.ErrorContext(ctx, "Error parsing payload", slog.String("error", err.Error()))
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}
	h.logger.DebugContext(ctx, "Modifying design",
		slog.String("projectId", r.PathValue("projectId")), slog.String("designId", diff.ID.String()))
	h.controller.ModifyDesign(ctx, diff, presenter)
}

func (h *CommandHandler) HandleRegenerateProse(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	h.logger.InfoContext(ctx, "Handling PATCH request to regenerate prose for design...")
	designUUID := r.PathValue("designId")
	designId, err := uuid.Parse(designUUID)
	if err != nil {
		http.Error(w, "Invalid design UUID", http.StatusBadRequest)
		return
	}
	presenter := presenters.NewDesignMutationOutcomePresenter(h.logger, w)
	h.controller.RegenerateDesignProse(ctx, designId, presenter)
}

func (h *CommandHandler) HandlePutAllDesignsForProject(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	projectId := entities.NewProjectId(r.PathValue("projectId"))
	h.logger.InfoContext(ctx, "Handling PUT request for multiple designs to project.",
		slog.String("projectId", projectId.String()))
	body, err := io.ReadAll(r.Body)
	if err != nil {
		http.Error(w, fmt.Sprintf("Error reading request body: %s", err.Error()), http.StatusBadRequest)
		return
	}
	if len(body) == 0 {
		http.Error(w, "Empty request body", http.StatusBadRequest)
		return
	}
	var payload []map[string]any
	err = json.Unmarshal(body, &payload)
	if err != nil {
		http.Error(w, fmt.Sprintf("Invalid JSON payload: %s", err.Error()), http.StatusBadRequest)
		return
	}
	designs := []adapters.Design{}
	err = json.Unmarshal(body, &designs)
	if err != nil {
		http.Error(w, fmt.Sprintf("Error parsing designs payload: %s", err.Error()), http.StatusBadRequest)
		return
	}
	errors := []error{}
	validDesigns := []adapters.Design{}
	for i, d := range payload {
		if err := h.schema.Validate(d); err != nil {
			id, ok := d["id"]
			if !ok {
				h.logger.ErrorContext(r.Context(), "Design has no ID", slog.String("design", fmt.Sprintf("%#v", d)))
				errors = append(errors, err)
				continue
			}
			if len(id.(string)) == 2 {
				h.logger.ErrorContext(r.Context(), "Design ID is 2 chars long but should be a UUID.",
					slog.String("designId", id.(string)))
				errors = append(errors, err)
				continue
			}
			h.logger.ErrorContext(r.Context(), "Invalid design",
				slog.String("designId", id.(string)), slog.String("error", err.Error()))
			errors = append(errors, err)
			continue
		}
		validDesigns = append(validDesigns, designs[i])
	}
	presenter := presenters.NewDesignMutationOutcomePresenter(h.logger, w)
	if len(errors) > 0 {
		h.logger.InfoContext(ctx, "Some designs are invalid", slog.Int("count", len(errors)))
		presenter.PresentError(usecases.ErrInvalidPayload)
		return
	}
	h.controller.ReplaceAllDesignsForProject(ctx, projectId, validDesigns, presenter)
}

func (h *CommandHandler) HandleDelete(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	h.logger.InfoContext(ctx, "Handling DELETE request for design...")
	designId := r.PathValue("designId")
	designUUID, err := uuid.Parse(designId)
	if err != nil {
		http.Error(w, "Invalid design UUID", http.StatusBadRequest)
		return
	}
	presenter := presenters.NewDesignMutationOutcomePresenter(h.logger, w)
	h.controller.DeleteDesign(ctx, designUUID, presenter)
}

func (h *CommandHandler) parseLayoutAndMeasurements(ctx context.Context,
	r *http.Request) (adapters.RoomLayout, adapters.Measurements, error) {

	var layout adapters.RoomLayout
	var measurements adapters.Measurements

	body, err := io.ReadAll(r.Body)
	if err != nil {
		return layout, measurements, fmt.Errorf("error reading request body: %w", err)
	}
	if len(body) == 0 {
		return layout, measurements, fmt.Errorf("empty request body")
	}

	req := struct {
		Scan         json.RawMessage `json:"scan"`
		Measurements json.RawMessage `json:"measurements"`
	}{}
	err = json.Unmarshal(body, &req)
	if err != nil {
		return layout, measurements, fmt.Errorf("invalid JSON payload: %w", err)
	}
	layoutBytes := req.Scan
	measurementsBytes := req.Measurements

	if err := json.Unmarshal(layoutBytes, &layout); err != nil {
		return layout, measurements, fmt.Errorf("error parsing layout from scan: %w", err)
	}
	if err := json.Unmarshal(measurementsBytes, &measurements); err != nil {
		return layout, measurements, fmt.Errorf("error parsing measurements: %w", err)
	}
	h.logger.InfoContext(r.Context(), "Parsed layout and measurements",
		slog.String("projectId", r.PathValue("projectId")))

	err = h.monolith.UpdateScanAndMeasurementsForProject(ctx,
		entities.NewProjectId(r.PathValue("projectId")), layoutBytes, measurementsBytes)
	return layout, measurements, err
}

func (h *CommandHandler) EvolveProjectDesignsForNewLayout(w http.ResponseWriter, r *http.Request) {
	ctx := r.Context()
	projectId := entities.NewProjectId(r.PathValue("projectId"))
	h.logger.InfoContext(ctx, "Handling POST request to evolve designs for new layout...",
		slog.String("projectId", projectId.String()))

	// Parse layout and measurements from request body, and update the monolith with them.
	layout, measurements, err := h.parseLayoutAndMeasurements(ctx, r)
	if err != nil {
		h.logger.ErrorContext(ctx, "Error parsing layout and measurements or updating monolith",
			slog.String("error", err.Error()))
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	presenter := presenters.NewDesignsPresenter(h.logger, w)
	h.controller.EvolveProjectDesignsForLayout(ctx, projectId, layout, measurements, presenter)
}
