-- migrate:up

-- Create new schema
CREATE SCHEMA design_pkg;

-- Drop old dependent view
DROP VIEW IF EXISTS template.template_details;

-- Drop new trigger
DROP TRIGGER IF EXISTS refresh_templates_updated_at ON template.templates;

-- Move and rename tables into new schema
ALTER TABLE template.template_options SET SCHEMA design_pkg;
ALTER TABLE design_pkg.template_options RENAME TO options;

ALTER TABLE template.template_product_selections SET SCHEMA design_pkg;
ALTER TABLE design_pkg.template_product_selections RENAME TO product_selections;

ALTER TABLE template.template_provenance SET SCHEMA design_pkg;
ALTER TABLE design_pkg.template_provenance RENAME TO provenance;

ALTER TABLE template.templates SET SCHEMA design_pkg;
ALTER TABLE design_pkg.templates RENAME TO packages;

ALTER TABLE template.vanity_scaling_options SET SCHEMA design_pkg;

-- Rename the FK columns
ALTER TABLE design_pkg.options                  RENAME COLUMN template_id TO design_pkg_id;
ALTER TABLE design_pkg.product_selections       RENAME COLUMN template_id TO design_pkg_id;
ALTER TABLE design_pkg.provenance               RENAME COLUMN template_id TO design_pkg_id;
ALTER TABLE design_pkg.vanity_scaling_options   RENAME COLUMN template_id TO design_pkg_id;
ALTER TABLE public.legacy_lookup                RENAME COLUMN template_id TO design_pkg_id;

-- Rename the FK constraint
ALTER TABLE design_pkg.options                  RENAME CONSTRAINT template_options_pkey TO design_pkg_options_pkey;
ALTER TABLE design_pkg.product_selections       RENAME CONSTRAINT template_product_selections_pkey TO design_pkg_product_selections_pkey;
ALTER TABLE design_pkg.provenance               RENAME CONSTRAINT template_provenance_pkey TO design_pkg_provenance_pkey;
ALTER TABLE design_pkg.packages                 RENAME CONSTRAINT templates_pkey TO design_pkg_packages_pkey;
ALTER TABLE design_pkg.packages                 RENAME CONSTRAINT templates_name_key TO design_pkg_packages_name_key;
ALTER TABLE design_pkg.packages                 RENAME CONSTRAINT templates_image_url_key TO design_pkg_packages_image_url_key;
ALTER TABLE design_pkg.vanity_scaling_options   RENAME CONSTRAINT vanity_scaling_options_template_id_min_vanity_length_inches_key TO vanity_scaling_options_design_pkg_id_min_vanity_length_inches_key;
ALTER TABLE public.legacy_lookup                RENAME CONSTRAINT legacy_lookup_template_id_key TO legacy_lookup_design_pkg_id_key;

-- Recreate the trigger
CREATE TRIGGER refresh_design_pkg_packages_updated_at BEFORE
UPDATE ON design_pkg.packages FOR EACH ROW EXECUTE FUNCTION maintain_updated_at_column();

-- Recreate view in new schema
CREATE VIEW design_pkg.details AS
SELECT
    -- Main design package fields
    dp.id,
    dp.created_at,
    dp.updated_at,
    dp.color_scheme,
    dp.style,
    dp.render_priority,
    dp.name,
    dp.description,
    dp.image_url,
    dp.inspiration,
    dp.atmosphere,
    dp.color_palette,
    dp.material_palette,
    dp.highlighted_brand_urls,

    -- Product selections
    ps.floor_tile,
    ps.lighting,
    ps.mirror,
    ps.paint,
    ps.shelving,
    ps.toilet,
    ps.shower_floor_tile,
    ps.shower_wall_tile,
    ps.tub_filler,
    ps.wall_tile_placement,
    ps.wall_tile,
    ps.wallpaper_placement,
    ps.wallpaper,

    -- Design package options
    o.alcove_tub,
    o.freestanding_tub,
    o.shower_glass_fixed,
    o.shower_glass_sliding,
    o.shower_system_full,
    o.shower_system_shower,
    o.tub_door_fixed,
    o.tub_door_sliding,

    -- Design package provenance (brand information) - nullable
    p.lighting_brand,
    p.plumbing_brand,
    p.toilet_brand,
    p.vanity_brand,
    p.vanity_storage,

    -- Legacy lookup - nullable
    ll.id as legacy_id

FROM design_pkg.packages dp
LEFT JOIN design_pkg.product_selections ps ON dp.id = ps.design_pkg_id
LEFT JOIN design_pkg.options o ON dp.id = o.design_pkg_id
LEFT JOIN design_pkg.provenance p ON dp.id = p.design_pkg_id
LEFT JOIN public.legacy_lookup ll ON dp.id = ll.design_pkg_id;

-- Add a comment explaining the view's purpose
COMMENT ON VIEW design_pkg.details IS
'Comprehensive view that joins all tables related to design packages except vanity_scaling_options.
This view simplifies design package retrieval by consolidating complex joins into a single queryable view.
Note: vanity_scaling_options are still fetched separately due to their one-to-many relationship.';

-- Create a temporary view of active view in old schema to avoid downtime for DB reads
CREATE VIEW template.template_details AS
SELECT
    -- Main design package fields
    dp.id,
    dp.created_at,
    dp.updated_at,
    dp.color_scheme,
    dp.style,
    dp.render_priority,
    dp.name,
    dp.description,
    dp.image_url,
    dp.inspiration,
    dp.atmosphere,
    dp.color_palette,
    dp.material_palette,
    dp.highlighted_brand_urls,

    -- Product selections
    ps.floor_tile,
    ps.lighting,
    ps.mirror,
    ps.paint,
    ps.shelving,
    ps.toilet,
    ps.shower_floor_tile,
    ps.shower_wall_tile,
    ps.tub_filler,
    ps.wall_tile_placement,
    ps.wall_tile,
    ps.wallpaper_placement,
    ps.wallpaper,

    -- Design package options
    o.alcove_tub,
    o.freestanding_tub,
    o.shower_glass_fixed,
    o.shower_glass_sliding,
    o.shower_system_full,
    o.shower_system_shower,
    o.tub_door_fixed,
    o.tub_door_sliding,

    -- Design package provenance (brand information) - nullable
    p.lighting_brand,
    p.plumbing_brand,
    p.toilet_brand,
    p.vanity_brand,
    p.vanity_storage,

    -- Legacy lookup - nullable
    ll.id as legacy_id

FROM design_pkg.packages dp
LEFT JOIN design_pkg.product_selections ps ON dp.id = ps.design_pkg_id
LEFT JOIN design_pkg.options o ON dp.id = o.design_pkg_id
LEFT JOIN design_pkg.provenance p ON dp.id = p.design_pkg_id
LEFT JOIN public.legacy_lookup ll ON dp.id = ll.design_pkg_id;

-- Add a comment explaining the view's purpose
COMMENT ON VIEW template.template_details IS
'Comprehensive view that joins all tables related to design packages except vanity_scaling_options.
This view simplifies design package retrieval by consolidating complex joins into a single queryable view.
Note: vanity_scaling_options are still fetched separately due to their one-to-many relationship.
Duplicated from design_pkg.details for backwards compatibility.';

-- Create temporary view of active table in old schema to avoid downtime for DB reads
CREATE VIEW template.vanity_scaling_options AS
SELECT
    design_pkg_id AS template_id,
    min_vanity_length_inches,
    vanity_product_id,
    faucet_product_id
FROM design_pkg.vanity_scaling_options;


-- migrate:down
-- Recreate old schema
CREATE SCHEMA IF NOT EXISTS template;

-- Drop temporary views previously created
DROP VIEW IF EXISTS template.vanity_scaling_options;
DROP VIEW IF EXISTS template.template_details;

-- Drop new dependent view
DROP VIEW IF EXISTS design_pkg.details;

-- Drop new trigger
DROP TRIGGER IF EXISTS refresh_design_pkg_packages_updated_at ON design_pkg.packages;

-- Rename the FK constraint
ALTER TABLE design_pkg.options               RENAME CONSTRAINT design_pkg_options_pkey TO template_options_pkey;
ALTER TABLE design_pkg.product_selections    RENAME CONSTRAINT design_pkg_product_selections_pkey TO template_product_selections_pkey;
ALTER TABLE design_pkg.provenance            RENAME CONSTRAINT design_pkg_provenance_pkey TO template_provenance_pkey;
ALTER TABLE design_pkg.packages                      RENAME CONSTRAINT design_pkg_packages_pkey TO templates_pkey;
ALTER TABLE design_pkg.packages                      RENAME CONSTRAINT design_pkg_packages_name_key TO templates_name_key;
ALTER TABLE design_pkg.packages                      RENAME CONSTRAINT design_pkg_packages_image_url_key TO templates_image_url_key;
ALTER TABLE design_pkg.vanity_scaling_options               RENAME CONSTRAINT vanity_scaling_options_design_pkg_id_min_vanity_length_inches_key TO vanity_scaling_options_template_id_min_vanity_length_inches_key;
ALTER TABLE public.legacy_lookup                            RENAME CONSTRAINT legacy_lookup_design_pkg_id_key TO legacy_lookup_template_id_key;

-- Rename the FK columns
ALTER TABLE design_pkg.options               RENAME COLUMN design_pkg_id TO template_id;
ALTER TABLE design_pkg.product_selections    RENAME COLUMN design_pkg_id TO template_id;
ALTER TABLE design_pkg.provenance            RENAME COLUMN design_pkg_id TO template_id;
ALTER TABLE design_pkg.vanity_scaling_options               RENAME COLUMN design_pkg_id TO template_id;
ALTER TABLE public.legacy_lookup                            RENAME COLUMN design_pkg_id TO template_id;

-- Move and rename tables back to original schema and names
ALTER TABLE design_pkg.options RENAME TO template_options;
ALTER TABLE design_pkg.template_options SET SCHEMA template;

ALTER TABLE design_pkg.product_selections RENAME TO template_product_selections;
ALTER TABLE design_pkg.template_product_selections SET SCHEMA template;

ALTER TABLE design_pkg.provenance RENAME TO template_provenance;
ALTER TABLE design_pkg.template_provenance SET SCHEMA template;

ALTER TABLE design_pkg.packages RENAME TO templates;
ALTER TABLE design_pkg.templates SET SCHEMA template;

ALTER TABLE design_pkg.vanity_scaling_options SET SCHEMA template;

-- Recreate old trigger
CREATE TRIGGER refresh_templates_updated_at BEFORE
UPDATE ON template.templates FOR EACH ROW EXECUTE FUNCTION maintain_updated_at_column();

-- Recreate view in old schema
CREATE VIEW template.template_details AS
SELECT
    -- Main template fields
    t.id,
    t.created_at,
    t.updated_at,
    t.color_scheme,
    t.style,
    t.render_priority,
    t.name,
    t.description,
    t.image_url,
    t.inspiration,
    t.atmosphere,
    t.color_palette,
    t.material_palette,
    t.highlighted_brand_urls,

    -- Product selections
    tps.floor_tile,
    tps.lighting,
    tps.mirror,
    tps.paint,
    tps.shelving,
    tps.toilet,
    tps.shower_floor_tile,
    tps.shower_wall_tile,
    tps.tub_filler,
    tps.wall_tile_placement,
    tps.wall_tile,
    tps.wallpaper_placement,
    tps.wallpaper,

    -- Template options
    to_.alcove_tub,
    to_.freestanding_tub,
    to_.shower_glass_fixed,
    to_.shower_glass_sliding,
    to_.shower_system_full,
    to_.shower_system_shower,
    to_.tub_door_fixed,
    to_.tub_door_sliding,

    -- Template provenance (brand information) - nullable
    tp.lighting_brand,
    tp.plumbing_brand,
    tp.toilet_brand,
    tp.vanity_brand,
    tp.vanity_storage,

    -- Legacy lookup - nullable
    ll.id as legacy_id

FROM template.templates t
LEFT JOIN template.template_product_selections tps ON t.id = tps.template_id
LEFT JOIN template.template_options to_ ON t.id = to_.template_id
LEFT JOIN template.template_provenance tp ON t.id = tp.template_id
LEFT JOIN public.legacy_lookup ll ON t.id = ll.template_id;

-- Add a comment explaining the view's purpose
COMMENT ON VIEW template.template_details IS
'Comprehensive view that joins all template-related tables except vanity_scaling_options.
This view simplifies template retrieval by consolidating complex joins into a single queryable view.
Note: vanity_scaling_options are still fetched separately due to their one-to-many relationship.';

-- Drop new schema
DROP SCHEMA design_pkg;
