package entities_test

import (
	"testing"
	"time"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

// Helper function to create a test room layout with consistent data
func createTestRoomLayout() entities.RoomLayout {
	// Use fixed UUIDs and timestamp for consistent comparison
	fixedId := uuid.MustParse("00000000-0000-0000-0000-000000000000")
	fixedTime := time.Date(2023, 1, 1, 0, 0, 0, 0, time.UTC)

	floorId := uuid.MustParse("11111111-1111-1111-1111-111111111111")
	toiletId := uuid.MustParse("*************-2222-2222-************")
	wallId := uuid.MustParse("*************-3333-3333-************")
	nicheId := uuid.MustParse("*************-4444-4444-************")
	wetAreaId := uuid.MustParse("*************-5555-5555-************")
	showerId := uuid.MustParse("*************-6666-6666-************")
	vanityId := uuid.MustParse("*************-7777-7777-************")

	maxLength := 1.5
	maxShowerGlassLength := 2.0
	maxTubLength := 1.8

	return entities.RoomLayout{
		Id:        fixedId,
		UpdatedAt: fixedTime,
		AreaSqFt:  25.0,
		FloorIds:  []uuid.UUID{floorId},
		ToiletIds: []uuid.UUID{toiletId},
		Walls: []entities.Wall{
			{
				LayoutId: wallId,
				NicheIds: []uuid.UUID{nicheId},
			},
		},
		WetAreas: []entities.WetArea{
			{
				LayoutId:             wetAreaId,
				GlassType:            entities.FixedShowerEnclosure,
				ShowerIds:            []uuid.UUID{showerId},
				MaxShowerGlassLength: &maxShowerGlassLength,
				MaxTubLength:         &maxTubLength,
			},
		},
		Vanities: []entities.Vanity{
			{
				LayoutId:  vanityId,
				MaxLength: &maxLength,
			},
		},
	}
}

func TestRoomLayoutCompare(t *testing.T) {
	// Test identical room layouts
	t.Run("identical layouts should be equal", func(t *testing.T) {
		layout1 := createTestRoomLayout()
		layout2 := createTestRoomLayout()

		equal, diff := layout1.Compare(layout2)
		if !equal {
			t.Errorf("Expected identical layouts to be equal, but got diff: %s", diff)
		}
		if diff != "" {
			t.Errorf("Expected empty diff for identical layouts, got: %s", diff)
		}
	})

	// Test different FloorIds
	t.Run("different floor IDs should not be equal", func(t *testing.T) {
		layout1 := createTestRoomLayout()
		layout2 := createTestRoomLayout()
		layout2.FloorIds = []uuid.UUID{uuid.New()}

		equal, diff := layout1.Compare(layout2)
		if equal {
			t.Error("Expected layouts with different floor IDs to not be equal")
		}
		if diff == "" {
			t.Error("Expected non-empty diff for different layouts")
		}
	})

	// Test different ToiletIds
	t.Run("different toilet IDs should not be equal", func(t *testing.T) {
		layout1 := createTestRoomLayout()
		layout2 := createTestRoomLayout()
		layout2.ToiletIds = []uuid.UUID{uuid.New()}

		equal, diff := layout1.Compare(layout2)
		if equal {
			t.Error("Expected layouts with different toilet IDs to not be equal")
		}
		if diff == "" {
			t.Error("Expected non-empty diff for different layouts")
		}
	})

	// Test different Vanities
	t.Run("different vanities should not be equal", func(t *testing.T) {
		layout1 := createTestRoomLayout()
		layout2 := createTestRoomLayout()
		maxLength := 2.5
		layout2.Vanities = []entities.Vanity{
			{
				LayoutId:  uuid.New(),
				MaxLength: &maxLength,
			},
		}

		equal, diff := layout1.Compare(layout2)
		if equal {
			t.Error("Expected layouts with different vanities to not be equal")
		}
		if diff == "" {
			t.Error("Expected non-empty diff for different layouts")
		}
	})

	// Test different Walls
	t.Run("different walls should not be equal", func(t *testing.T) {
		layout1 := createTestRoomLayout()
		layout2 := createTestRoomLayout()
		layout2.Walls = []entities.Wall{
			{
				LayoutId: uuid.New(),
				NicheIds: []uuid.UUID{uuid.New()},
			},
		}

		equal, diff := layout1.Compare(layout2)
		if equal {
			t.Error("Expected layouts with different walls to not be equal")
		}
		if diff == "" {
			t.Error("Expected non-empty diff for different layouts")
		}
	})

	// Test different WetAreas
	t.Run("different wet areas should not be equal", func(t *testing.T) {
		layout1 := createTestRoomLayout()
		layout2 := createTestRoomLayout()
		layout2.WetAreas = []entities.WetArea{
			{
				LayoutId:  uuid.New(),
				GlassType: entities.SlidingShowerEnclosure,
				ShowerIds: []uuid.UUID{uuid.New()},
			},
		}

		equal, diff := layout1.Compare(layout2)
		if equal {
			t.Error("Expected layouts with different wet areas to not be equal")
		}
		if diff == "" {
			t.Error("Expected non-empty diff for different layouts")
		}
	})

	// Test empty layouts
	t.Run("empty layouts should be equal", func(t *testing.T) {
		layout1 := entities.RoomLayout{}
		layout2 := entities.RoomLayout{}

		equal, diff := layout1.Compare(layout2)
		if !equal {
			t.Errorf("Expected empty layouts to be equal, but got diff: %s", diff)
		}
		if diff != "" {
			t.Errorf("Expected empty diff for empty layouts, got: %s", diff)
		}
	})

	// Test nil vs empty slices
	t.Run("nil vs empty slices should be equal", func(t *testing.T) {
		layout1 := entities.RoomLayout{
			FloorIds:  nil,
			ToiletIds: nil,
			Walls:     nil,
			WetAreas:  nil,
			Vanities:  nil,
		}
		layout2 := entities.RoomLayout{
			FloorIds:  []uuid.UUID{},
			ToiletIds: []uuid.UUID{},
			Walls:     []entities.Wall{},
			WetAreas:  []entities.WetArea{},
			Vanities:  []entities.Vanity{},
		}

		equal, diff := layout1.Compare(layout2)
		if !equal {
			t.Errorf("Expected nil vs empty slices to be equal, but got diff: %s", diff)
		}
	})

	// Test order independence for slices
	t.Run("different order in slices should not be equal", func(t *testing.T) {
		id1 := uuid.New()
		id2 := uuid.New()

		layout1 := entities.RoomLayout{
			FloorIds: []uuid.UUID{id1, id2},
		}
		layout2 := entities.RoomLayout{
			FloorIds: []uuid.UUID{id2, id1},
		}

		equal, diff := layout1.Compare(layout2)
		if equal {
			t.Error("Expected layouts with different order to not be equal")
		}
		if diff == "" {
			t.Error("Expected non-empty diff for different order")
		}
	})

	// Test complex wet area differences
	t.Run("wet areas with different alcove tubs should not be equal", func(t *testing.T) {
		layout1 := createTestRoomLayout()
		layout2 := createTestRoomLayout()

		showerId := uuid.New()
		layout2.WetAreas[0].AlcoveTubs = []entities.AlcoveTub{
			{
				LayoutId: uuid.New(),
				DoorType: entities.SlidingShowerEnclosure,
				ShowerId: &showerId,
			},
		}

		equal, diff := layout1.Compare(layout2)
		if equal {
			t.Error("Expected layouts with different alcove tubs to not be equal")
		}
		if diff == "" {
			t.Error("Expected non-empty diff for different alcove tubs")
		}
	})

	// Test wet areas with freestanding tubs
	t.Run("wet areas with different freestanding tubs should not be equal", func(t *testing.T) {
		layout1 := createTestRoomLayout()
		layout2 := createTestRoomLayout()

		layout2.WetAreas[0].FreestandingTubIds = []uuid.UUID{uuid.New()}

		equal, diff := layout1.Compare(layout2)
		if equal {
			t.Error("Expected layouts with different freestanding tubs to not be equal")
		}
		if diff == "" {
			t.Error("Expected non-empty diff for different freestanding tubs")
		}
	})

	// Test pointer field differences (MaxLength, MaxShowerGlassLength, MaxTubLength)
	t.Run("different pointer values should not be equal", func(t *testing.T) {
		layout1 := createTestRoomLayout()
		layout2 := createTestRoomLayout()

		differentLength := 3.0
		layout2.Vanities[0].MaxLength = &differentLength

		equal, diff := layout1.Compare(layout2)
		if equal {
			t.Error("Expected layouts with different vanity max length to not be equal")
		}
		if diff == "" {
			t.Error("Expected non-empty diff for different vanity max length")
		}
	})

	// Test nil vs non-nil pointer fields
	t.Run("nil vs non-nil pointer fields should not be equal", func(t *testing.T) {
		layout1 := createTestRoomLayout()
		layout2 := createTestRoomLayout()

		layout2.Vanities[0].MaxLength = nil

		equal, diff := layout1.Compare(layout2)
		if equal {
			t.Error("Expected layouts with nil vs non-nil pointer to not be equal")
		}
		if diff == "" {
			t.Error("Expected non-empty diff for nil vs non-nil pointer")
		}
	})

	// Test wall equals functionality
	t.Run("walls with same layout ID but different niches should not be equal", func(t *testing.T) {
		layout1 := createTestRoomLayout()
		layout2 := createTestRoomLayout()

		// Same layout ID but different niche IDs
		layout2.Walls[0].NicheIds = []uuid.UUID{uuid.New(), uuid.New()}

		equal, diff := layout1.Compare(layout2)
		if equal {
			t.Error("Expected layouts with different wall niches to not be equal")
		}
		if diff == "" {
			t.Error("Expected non-empty diff for different wall niches")
		}
	})

	// Test wet area equals functionality
	t.Run("wet areas with same layout ID but different glass type should not be equal", func(t *testing.T) {
		layout1 := createTestRoomLayout()
		layout2 := createTestRoomLayout()

		layout2.WetAreas[0].GlassType = entities.SlidingShowerEnclosure

		equal, diff := layout1.Compare(layout2)
		if equal {
			t.Error("Expected layouts with different wet area glass type to not be equal")
		}
		if diff == "" {
			t.Error("Expected non-empty diff for different wet area glass type")
		}
	})

	// Test that non-essential fields are ignored when essential parts are equal
	t.Run("when essential parts are equal, non-essential fields should be ignored", func(t *testing.T) {
		// Create one layout and copy it to ensure identical essential parts
		layout1 := createTestRoomLayout()
		layout2 := layout1 // Copy the struct

		// Only change non-essential fields (Id, UpdatedAt, RawData, Hash, AreaSqFt)
		// These fields are not part of the essential comparison in lines 77-81
		layout2.Id = uuid.New()
		layout2.UpdatedAt = time.Now().Add(time.Hour)
		layout2.RawData = []byte("different raw data")
		layout2.Hash = 999999
		layout2.AreaSqFt = 50.0

		equal, diff := layout1.Compare(layout2)
		if !equal {
			t.Errorf("Expected layouts with same essential parts to be equal, but got diff: %s", diff)
		}
		if diff != "" {
			t.Errorf("Expected empty diff when essential parts are the same, got: %s", diff)
		}
	})

	// Test that when essential parts differ, JSON comparison shows all differences
	t.Run("when essential parts differ, JSON comparison includes all fields", func(t *testing.T) {
		layout1 := createTestRoomLayout()
		layout2 := createTestRoomLayout()

		// Change both essential and non-essential fields
		layout2.FloorIds = []uuid.UUID{uuid.New()} // Essential field
		layout2.Id = uuid.New()                    // Non-essential field
		layout2.AreaSqFt = 50.0                    // Non-essential field

		equal, diff := layout1.Compare(layout2)
		if equal {
			t.Error("Expected layouts with different essential parts to not be equal")
		}
		if diff == "" {
			t.Error("Expected non-empty diff when essential parts differ")
		}
		// The diff should include both essential and non-essential field differences
		// since it falls back to JSON comparison
	})
}

func TestWallEquals(t *testing.T) {
	t.Run("identical walls should be equal", func(t *testing.T) {
		layoutId := uuid.New()
		nicheId1 := uuid.New()
		nicheId2 := uuid.New()

		wall1 := entities.Wall{
			LayoutId: layoutId,
			NicheIds: []uuid.UUID{nicheId1, nicheId2},
		}
		wall2 := entities.Wall{
			LayoutId: layoutId,
			NicheIds: []uuid.UUID{nicheId1, nicheId2},
		}

		if !wall1.Equals(wall2) {
			t.Error("Expected identical walls to be equal")
		}
	})

	t.Run("different layout IDs should not be equal", func(t *testing.T) {
		wall1 := entities.Wall{
			LayoutId: uuid.New(),
			NicheIds: []uuid.UUID{uuid.New()},
		}
		wall2 := entities.Wall{
			LayoutId: uuid.New(),
			NicheIds: wall1.NicheIds,
		}

		if wall1.Equals(wall2) {
			t.Error("Expected walls with different layout IDs to not be equal")
		}
	})

	t.Run("different niche IDs should not be equal", func(t *testing.T) {
		layoutId := uuid.New()
		wall1 := entities.Wall{
			LayoutId: layoutId,
			NicheIds: []uuid.UUID{uuid.New()},
		}
		wall2 := entities.Wall{
			LayoutId: layoutId,
			NicheIds: []uuid.UUID{uuid.New()},
		}

		if wall1.Equals(wall2) {
			t.Error("Expected walls with different niche IDs to not be equal")
		}
	})

	t.Run("nil vs empty niche IDs should be equal", func(t *testing.T) {
		layoutId := uuid.New()
		wall1 := entities.Wall{
			LayoutId: layoutId,
			NicheIds: nil,
		}
		wall2 := entities.Wall{
			LayoutId: layoutId,
			NicheIds: []uuid.UUID{},
		}

		if !wall1.Equals(wall2) {
			t.Error("Expected walls with nil vs empty niche IDs to be equal")
		}
	})
}

func TestWetAreaEquals(t *testing.T) {
	t.Run("identical wet areas should be equal", func(t *testing.T) {
		layoutId := uuid.New()
		showerId := uuid.New()
		tubId := uuid.New()
		maxGlassLength := 2.0
		maxTubLength := 1.8

		wetArea1 := entities.WetArea{
			LayoutId:             layoutId,
			GlassType:            entities.FixedShowerEnclosure,
			ShowerIds:            []uuid.UUID{showerId},
			FreestandingTubIds:   []uuid.UUID{tubId},
			MaxShowerGlassLength: &maxGlassLength,
			MaxTubLength:         &maxTubLength,
		}
		wetArea2 := entities.WetArea{
			LayoutId:             layoutId,
			GlassType:            entities.FixedShowerEnclosure,
			ShowerIds:            []uuid.UUID{showerId},
			FreestandingTubIds:   []uuid.UUID{tubId},
			MaxShowerGlassLength: &maxGlassLength,
			MaxTubLength:         &maxTubLength,
		}

		if !wetArea1.Equals(wetArea2) {
			t.Error("Expected identical wet areas to be equal")
		}
	})

	t.Run("different layout IDs should not be equal", func(t *testing.T) {
		wetArea1 := entities.WetArea{
			LayoutId:  uuid.New(),
			GlassType: entities.FixedShowerEnclosure,
		}
		wetArea2 := entities.WetArea{
			LayoutId:  uuid.New(),
			GlassType: entities.FixedShowerEnclosure,
		}

		if wetArea1.Equals(wetArea2) {
			t.Error("Expected wet areas with different layout IDs to not be equal")
		}
	})

	t.Run("different glass types should not be equal", func(t *testing.T) {
		layoutId := uuid.New()
		wetArea1 := entities.WetArea{
			LayoutId:  layoutId,
			GlassType: entities.FixedShowerEnclosure,
		}
		wetArea2 := entities.WetArea{
			LayoutId:  layoutId,
			GlassType: entities.SlidingShowerEnclosure,
		}

		if wetArea1.Equals(wetArea2) {
			t.Error("Expected wet areas with different glass types to not be equal")
		}
	})

	t.Run("different shower IDs should not be equal", func(t *testing.T) {
		layoutId := uuid.New()
		wetArea1 := entities.WetArea{
			LayoutId:  layoutId,
			GlassType: entities.FixedShowerEnclosure,
			ShowerIds: []uuid.UUID{uuid.New()},
		}
		wetArea2 := entities.WetArea{
			LayoutId:  layoutId,
			GlassType: entities.FixedShowerEnclosure,
			ShowerIds: []uuid.UUID{uuid.New()},
		}

		if wetArea1.Equals(wetArea2) {
			t.Error("Expected wet areas with different shower IDs to not be equal")
		}
	})

	t.Run("different alcove tubs should not be equal", func(t *testing.T) {
		layoutId := uuid.New()
		showerId1 := uuid.New()
		showerId2 := uuid.New()

		wetArea1 := entities.WetArea{
			LayoutId:  layoutId,
			GlassType: entities.FixedShowerEnclosure,
			AlcoveTubs: []entities.AlcoveTub{
				{
					LayoutId: uuid.New(),
					DoorType: entities.SlidingShowerEnclosure,
					ShowerId: &showerId1,
				},
			},
		}
		wetArea2 := entities.WetArea{
			LayoutId:  layoutId,
			GlassType: entities.FixedShowerEnclosure,
			AlcoveTubs: []entities.AlcoveTub{
				{
					LayoutId: uuid.New(),
					DoorType: entities.SlidingShowerEnclosure,
					ShowerId: &showerId2,
				},
			},
		}

		if wetArea1.Equals(wetArea2) {
			t.Error("Expected wet areas with different alcove tubs to not be equal")
		}
	})

	t.Run("different max shower glass length should not be equal", func(t *testing.T) {
		layoutId := uuid.New()
		length1 := 2.0
		length2 := 2.5

		wetArea1 := entities.WetArea{
			LayoutId:             layoutId,
			GlassType:            entities.FixedShowerEnclosure,
			MaxShowerGlassLength: &length1,
		}
		wetArea2 := entities.WetArea{
			LayoutId:             layoutId,
			GlassType:            entities.FixedShowerEnclosure,
			MaxShowerGlassLength: &length2,
		}

		if wetArea1.Equals(wetArea2) {
			t.Error("Expected wet areas with different max shower glass length to not be equal")
		}
	})

	t.Run("nil vs non-nil max shower glass length should not be equal", func(t *testing.T) {
		layoutId := uuid.New()
		length := 2.0

		wetArea1 := entities.WetArea{
			LayoutId:             layoutId,
			GlassType:            entities.FixedShowerEnclosure,
			MaxShowerGlassLength: nil,
		}
		wetArea2 := entities.WetArea{
			LayoutId:             layoutId,
			GlassType:            entities.FixedShowerEnclosure,
			MaxShowerGlassLength: &length,
		}

		if wetArea1.Equals(wetArea2) {
			t.Error("Expected wet areas with nil vs non-nil max shower glass length to not be equal")
		}
	})
}
