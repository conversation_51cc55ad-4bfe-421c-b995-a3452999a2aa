# Architecture Overview

This document provides an overview of the software architecture for the Design Server.
The system follows the principles of **Clean Architecture**.

## 1. Core Principles

The architecture is designed to create a system that is:

- **Independent of Frameworks:** The core business logic has no dependency on external frameworks like web servers or databases.
- **Testable:** Business rules can be tested without the UI, Database, Web Server, or any other external element.
- **Independent of UI:** The UI can change easily, without changing the rest of the system.
- **Independent of Database:** You can swap out your database technology without affecting the business rules.

### Clean Architecture

The architecture is organized in concentric layers, each with a distinct responsibility.
The key rule is the **Dependency Rule**: source code dependencies can only point inwards.
Nothing in an inner layer can know anything about an outer layer.

1. **Entities (Inner-most):** Domain entities & aggregates.
2. **Use Cases:** Application-specific business logic.
3. **Interface Adapters:** Convert data between use cases & external systems (UI, database).
4. **Frameworks & Drivers (Outer-most):** The tools & frameworks that drive the application.

### CQRS (Command Query Responsibility Segregation)

The application separates operations that mutate state (Commands) from those that retrieve state (Queries).

- **Commands:** Handle create, update, and delete operations (e.g., `DesignCreater`, `DesignUpdater`, `DesignDeleter`).
- **Queries:** Handle read operations (e.g., `DesignPackageRetriever`, `DesignRetriever`). They return data but do not change the system's state.

This separation simplifies the models and logic for each type of operation.

## 2. Architectural Diagram

The following diagram illustrates the layers and the flow of control. Dependencies point inwards, toward the `Entities`.

```mermaid
graph TD
    subgraph "Frameworks & Drivers"
        Web[net/http]
        DB[(PostgreSQL)]
    end

    subgraph "Interface Adapters"
        Controllers[controllers]
        Presenters[presenters]
        Gateways[gateways]
    end

    subgraph "Business Logic"
        UseCases[usecases]
        Boundaries[interfaces]
    end

    subgraph "Domain"
        Entities[(entities)]
    end

    %% Dependencies
    Web -- uses --> Controllers
    Controllers --> UseCases
    UseCases -- uses --> Entities
    UseCases -- uses --> Boundaries
    Gateways -- implements --> Boundaries
    Presenters -- implements --> Boundaries

    class Entities entities;
    class UseCases,Boundaries usecases;
    class Controllers,Gateways,Presenters adapters;
    class Web,DB frameworks;
```

## 3. Layers and Directory Structure

The project's directory structure maps directly to the architectural layers.

- **Entities (`entities/`)**
    This is the core of the application, containing the business objects and their rules. They are plain Go structs with no dependencies on other layers.
  - **Core Entities**: `DesignPackage`, `Design`, `Rendition`.
  - **Value Objects**: Product selections, metadata, render preferences
  - **Business Rules**: Validation logic, domain constraints, and entity behavior

- **Use Cases (`usecases/`)**
    This layer contains the application-specific business logic. It orchestrates the flow of data to and from the entities.
  - **Interactors** (e.g., `DesignEvolver`): Implement the logic for a specific user story.
  - **Interfaces (Ports)**: Defines contracts for what the outer layers must implement (e.g., `DesignRepository`). This is a key part of dependency inversion.

- **Interface Adapters (`adapters/`)**
    This layer converts data between the format needed by the use cases and the format of external agencies like the database or the web.
  - **Controllers (`adapters/controllers/`)**: Handle coordination, parse input, and invoke the appropriate use case.
  - **Gateways (`adapters/gateways/`)**: Implement interfaces defined by use cases; responsible for all communication with external systems like PostgreSQL, OpenAI, and legacy APIs.
  - **Presenters (`adapters/presenters/`)**: Format the output from a use case for the response, setting status codes and response bodies.
  - **Data Transfer Objects**: Adapter structs (e.g., `adapters.Design`) that match HTTP API format and convert to/from use case types.

- **Frameworks & Drivers (`frameworks/`, `main.go`)**
    The outermost layer contains the specific implementations and tools.
  - **`main.go`**: The application entry point, responsible for dependency injection and wiring all components together.
  - **`frameworks/web/`**: Contains HTTP handlers, middleware (correlation, logging, recovery), routing, and testing utilities.
  - **`frameworks/db/`**: Contains database migrations managed by `dbmate`, connection pool management, and AWS Secrets Manager integration.

## 4. Request Flow Example: Deleting a Design

To illustrate how the layers interact, here is the flow for a `DELETE /projects/{projectId}/designs/{designId}` request:

1. An HTTP `DELETE` request arrives at the web server.
2. The router in `frameworks/web/routes.go` directs the request to the `CommandHandler.HandleDelete()` method.
3. The **Web Handler** (`frameworks/web`) applies middleware (correlation, logging, recovery) and parses the design ID from the URL.
4. The Handler creates a `DesignMutationOutcomePresenter` and calls the `DesignWriteController.DeleteDesign()` method.
5. The **Controller** (`adapters/controllers.DesignWriteController`) calls the injected `DesignDeleter` use case.
6. The **Use Case** (`usecases.DesignDeleter`) validates the input (e.g., ensures the ID is not a zero UUID).
7. The Use Case calls the `DeleteDesign` method on its repository **interface**.
8. The **Gateway** (`adapters/gateways.RelationalDb`), which implements the repository interface, executes an `UPDATE` statement to archive the design (soft delete).
9. The Use Case receives the result from the gateway.
    - On success, it calls `presenter.ConveySuccess()`.
    - On failure (e.g., "not found"), it calls `presenter.PresentError(err)`.
10. The **Presenter** (`adapters/presenters`) writes the final HTTP response (e.g., `204 No Content` or `404 Not Found`) to the client.

## 5. Key Patterns

### Dependency Injection

Dependencies are "injected" into components, usually during construction. For example, a `DesignDeleter` use case receives a `DesignRepository` implementation when it's created.
This decouples the use case from the specific database implementation and makes it easy to swap in a fake repository for testing.

```go
// From usecases/delete_design_test.go
repo := gateways.NewFakeRelDb()
deleter := usecases.NewDesignDeleter(repo) // repo is injected
```

### Presenter Pattern

Use cases do not know about HTTP. When a use case completes, it notifies a presenter through an interface.
The presenter's implementation is responsible for creating the specific HTTP response. This keeps the core logic clean of web-specific concerns.

```go
// Use case calls presenter interface
presenter.PresentError(usecases.ErrNotFound)

// Presenter implementation maps to HTTP status codes
func (p *DataPresenter) PresentError(err error) {
    switch err {
    case usecases.ErrNotFound:
        http.Error(p.w, err.Error(), http.StatusNotFound)
    case usecases.ErrInvalidPayload:
        http.Error(p.w, err.Error(), http.StatusBadRequest)
    // ...
    }
}
```

## 6. Additional Architectural Components

### Error Handling Strategy

The system uses a centralized error handling approach with well-defined error types:

- **Domain Errors** (`usecases/errors.go`): Standard errors like `ErrNotFound`, `ErrConflict`, `ErrInvalidPayload`
- **Error Mapping**: Presenters map domain errors to appropriate HTTP status codes
- **Panic Recovery**: Middleware catches panics and logs them with correlation context

### Testing Architecture

The system follows a comprehensive testing strategy:

- **Unit Tests**: Each layer is tested in isolation using dependency injection
- **Integration Tests**: Use fake implementations (`gateways.NewFakeRelDb()`) rather than mocks
- **Test Helpers**: Centralized testing utilities in `frameworks/web/testing.go`
- **Property-Based Testing**: Uses `gopter` for property-based testing of entities

```go
// Example: Testing with fakes instead of mocks
repo := gateways.NewFakeRelDb()
deleter := usecases.NewDesignDeleter(repo)
```

### Middleware Architecture

The web layer implements a composable middleware system:

- **Standard Middleware Chain**: Correlation, logging, and recovery middleware
- **Security Middleware**: Headers, request size limits, CORS handling
- **Correlation Context**: Request tracking across the entire request lifecycle

```go
// Standard middleware applied to all requests
func StandardMiddleware(logger *slog.Logger) *MiddlewareChain {
    return NewMiddlewareChain().
        Use(CorrelationMiddleware).
        Use(LoggingMiddleware(logger)).
        Use(RecoveryMiddleware(logger))
}
```

### Data Conversion Patterns

The system uses explicit conversion between layers to maintain separation:

- **Adapter Structs**: HTTP API format in `adapters/` package
- **Entity Structs**: Domain format in `entities/` package
- **Use Case Structs**: Application format in `usecases/` package
- **Conversion Functions**: Explicit conversion methods (e.g., `ToUsecaseDesign()`)

### External System Integration

The architecture integrates with several external systems through gateways:

- **OpenAI Gateway**: For AI-generated content (titles, descriptions)
- **Monolith Gateway**: Legacy system integration for project data
- **Catalog Gateway**: Product information retrieval
- **Product Search Gateway**: Product discovery functionality

### Database Architecture

- **Migration Management**: Uses `dbmate` for database migrations in `frameworks/db/`
- **Connection Pooling**: PostgreSQL connection pool management
- **Soft Deletes**: Designs are archived rather than hard deleted
- **Transaction Management**: Atomic operations for complex data changes

## 7. System Scope and Boundaries

### Current Responsibilities

The Design Server is responsible for:

- **Room Design Management**: CRUD operations for room designs with product selections
- **Design Package System**: Design packages from which designs can be generated
- **Rendition Management**: Images rendered from designs
- **AI Integration**: Automated generation of design titles and descriptions

### External Dependencies

- **Legacy Monolith**: Project data & room layout tracking
- **Catalog+Search**: Product catalog & AI-powered search
- **OpenAI**: AI-generated content for designs
- **PostgreSQL**: Primary data persistence
- **CDN**: Static asset delivery for renditions

### Future Architectural Considerations

Based on the current codebase comments and structure:

1. **Aggregate Refinement**: Currently designs are aggregates with renditions. Future consideration is to make rooms the primary aggregate with designs hanging off them.
1. **Domain Evolution**: Room layouts and measurements are currently value objects without identity. They should evolve into full entities.
1. **Monolith Migration**: The system currently integrates with a legacy monolith. Over time, more functionality should be extracted from the monolith.
1. **Event Sourcing**: The current CQRS implementation could evolve to include event sourcing for better audit trails and temporal queries.

## 8. Development and Deployment

### Local Development

- Database: Uses local PostgreSQL instance (configurable via `PGHOST` environment variable)
- Schema: Automatically managed through `dbmate` migrations
- Testing: Comprehensive test suite with integration tests (`go test --tags=integration ./...`)

### Configuration

The system uses environment variables for configuration:

- `PGHOST`: Database host (default: AWS RDS instance)
- `BASE_API_HOSTNAME`: External API hostname
- `BASE_CDN_HOSTNAME`: CDN hostname for static assets

### Observability

- **Structured Logging**: Uses `slog` with correlation IDs for request tracing
- **Error Tracking**: Centralized error handling with proper HTTP status mapping
- **Request Correlation**: Every request gets a correlation ID for distributed tracing
