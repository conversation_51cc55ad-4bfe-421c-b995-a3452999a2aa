# Design server

A Go server offering a REST API for room designs that adhere to a JSON Schema.
This replaces the prototype monolith as the canonical home for room designs
as well as the design packages from which they are generated & images rendered from them.

The architecture is based on the Clean Architecture with separation of responsibilities
between commands that mutate state and queries that retrieve it.

It persists data to a database called `projects` that resides on the monolith's RDS instance.

## Getting started

Initialize repo after cloning: `go mod tidy`

Run all tests (takes ~3s): `go test --tags=integration,dev ./...`

## Run the server

Set the `PGPASSWORD` env var for the monolith's RDS instance, then execute `go run .`
Alternately, run a local Postgres containing the `projects` database, then run the following command:

```sh
go run -tags=dev . --pghost=localhost
```

Try it by opening a URL like this in a browser:
<http://localhost:8080/projects/DEFAULT/designs>

## Change the Postgres schema

The schema is managed by [dbmate](https://github.com/amacneil/dbmate).
You can see the current schema `frameworks/db/schema.sql`.
The settings are stored in the `.env` file.
To create a new migration, run the following command:

```sh
dbmate new <description>
```

This will create a new file in the `frameworks/db` directory.
Edit the file to add the SQL statements for the migration.
Then run `dbmate up` to apply the migration.

## Database Connection Pools

The service uses PostgreSQL connection pools for efficient database connection management. Connection pools can be configured using environment variables:

- `DB_MAX_CONNS`: Maximum number of connections (default: 30)
- `DB_MIN_CONNS`: Minimum number of connections (default: 5)
- `DB_MAX_CONN_LIFETIME`: Maximum connection lifetime (default: 1h)
- `DB_MAX_CONN_IDLE_TIME`: Maximum connection idle time (default: 30m)
- `DB_HEALTH_CHECK_PERIOD`: Health check period (default: 1m)

See [docs/database-connection-pools.md](docs/database-connection-pools.md) for detailed configuration guidance and best practices.
