package adapters

import "gitlab.com/arc-studio-ai/services/room-design/usecases"

// CartInclusion is an alias to the usecase type since they're identical
type CartInclusion = usecases.CartInclusion

// CartInclusionKey is an alias to the usecase type since they're identical
type CartInclusionKey = usecases.CartInclusionKey

// CartInclusions is an alias to the usecase type since they're identical
type CartInclusions = usecases.CartInclusions

// ConvertCartInclusionsArrayToMap converts an array of cart inclusions to a map with composite keys.
// This is a helper function to avoid code duplication in HTTP handlers.
func ConvertCartInclusionsArrayToMap(inclusionsArray []CartInclusion) CartInclusions {
	inclusions := make(CartInclusions, len(inclusionsArray))
	for _, inclusion := range inclusionsArray {
		key := CartInclusionKey{
			ProductID: inclusion.ProductID,
			Location:  inclusion.Location,
		}
		inclusions[key] = inclusion
	}
	return inclusions
}
