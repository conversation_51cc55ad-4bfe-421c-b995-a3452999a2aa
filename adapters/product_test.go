package adapters_test

import (
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

func ptrTo[T any](v T) *T {
	return &v
}

func TestFaucetHoleSpacingConversions(t *testing.T) {
	t.Run("valid conversions", func(t *testing.T) {
		tests := []struct {
			adapter adapters.FaucetHoleSpacing
			usecase usecases.FaucetHoleSpacing
		}{
			{adapters.SingleHole, usecases.SingleHole},
			{adapters.WideSpread, usecases.WideSpread},
			{adapters.CenterSet, usecases.CenterSet},
		}

		for _, tt := range tests {
			// Test ToUsecase
			result, err := tt.adapter.ToUsecase()
			require.NoError(t, err)
			assert.Equal(t, tt.usecase, result)

			// Test FromUsecase
			back := adapters.FaucetHoleSpacingFromUsecase(tt.usecase)
			assert.Equal(t, tt.adapter, back)
		}
	})

	t.Run("error cases", func(t *testing.T) {
		_, err := adapters.FaucetHoleSpacing("UNKNOWN").ToUsecase()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "unknown faucet hole spacing")

		// Unknown usecase defaults to SingleHole
		result := adapters.FaucetHoleSpacingFromUsecase(usecases.FaucetHoleSpacing("UNKNOWN"))
		assert.Equal(t, adapters.SingleHole, result)
	})
}

func TestFromUsecaseProductInfo(t *testing.T) {
	testUUID := uuid.New()
	colorGroup := usecases.White

	t.Run("basic conversion", func(t *testing.T) {
		input := usecases.ProductInfo{
			Id:                testUUID,
			Category:          usecases.CategoryFaucet,
			Name:              "Test Faucet",
			Length:            12.5,
			ProductFamilyName: "Test Family",
		}

		result := adapters.FromUsecaseProductInfo(input)

		assert.Equal(t, testUUID, result.Id)
		assert.Equal(t, usecases.CategoryFaucet, result.Category.Name)
		assert.Equal(t, "Test Faucet", result.Name)
		assert.Equal(t, "12.50000", result.Length)
		assert.Equal(t, "Test Family", result.ProductFamilyName)
		assert.Nil(t, result.ColorGroup)
	})

	t.Run("with color", func(t *testing.T) {
		input := usecases.ProductInfo{
			Id:       testUUID,
			Category: usecases.CategoryMirror,
			Length:   24.0,
			Color:    &colorGroup,
		}

		result := adapters.FromUsecaseProductInfo(input)

		assert.Equal(t, usecases.White, result.ColorGroup.Name)
	})
}

func TestFromUsecaseRenderableProduct(t *testing.T) {
	testUUID := uuid.New()

	t.Run("faucet", func(t *testing.T) {
		input := usecases.Faucet{
			ProductInfo: usecases.ProductInfo{
				Id:       testUUID,
				Category: usecases.CategoryFaucet,
				Length:   12.5,
			},
			HoleSpacingCompatibility: []usecases.FaucetHoleSpacing{usecases.SingleHole, usecases.WideSpread},
		}

		result := adapters.FromUsecaseRenderableProduct(input)

		assert.Equal(t, usecases.CategoryFaucet, result.Category.Name)
		assert.Equal(t, "12.50000", result.Length)
		assert.Len(t, result.HoleSpacingCompatibility, 2)
		assert.Contains(t, result.HoleSpacingCompatibility, adapters.SingleHole)
	})

	t.Run("lighting", func(t *testing.T) {
		input := usecases.Lighting{
			ProductInfo:   usecases.ProductInfo{Id: testUUID, Category: usecases.CategoryLighting, Length: 6.0},
			NumberOfBulbs: 3,
		}

		result := adapters.FromUsecaseRenderableProduct(input)

		assert.Equal(t, usecases.CategoryLighting, result.Category.Name)
		assert.Equal(t, uint8(3), *result.NumberOfLights)
	})

	t.Run("mirror types", func(t *testing.T) {
		// Medicine cabinet
		medCab := usecases.Mirror{
			ProductInfo:       usecases.ProductInfo{Id: testUUID, Category: usecases.CategoryMirror, Length: 30.0},
			IsMedicineCabinet: true,
		}
		result := adapters.FromUsecaseRenderableProduct(medCab)
		assert.Equal(t, string(adapters.MirrorTypeMedicineCabinet), *result.Type)

		// Regular mirror
		mirror := usecases.Mirror{
			ProductInfo:       usecases.ProductInfo{Id: testUUID, Category: usecases.CategoryMirror, Length: 24.0},
			IsMedicineCabinet: false,
		}
		result = adapters.FromUsecaseRenderableProduct(mirror)
		assert.Equal(t, string(adapters.MirrorTypeMirror), *result.Type)
	})

	t.Run("vanity", func(t *testing.T) {
		input := usecases.Vanity{
			ProductInfo:       usecases.ProductInfo{Id: testUUID, Category: usecases.CategoryVanity, Length: 48.0},
			FaucetHoleSpacing: usecases.WideSpread,
			NumberOfSinks:     2,
			SinkOffset:        12.5,
		}

		result := adapters.FromUsecaseRenderableProduct(input)

		assert.Equal(t, adapters.WideSpread, *result.FaucetHoleSpacing)
		assert.Equal(t, uint8(2), *result.NumberOfSinks)
		assert.Equal(t, "12.50000", *result.SinkOffset)
	})

	t.Run("additional product types", func(t *testing.T) {
		// Shelving
		shelving := usecases.Shelving{
			ProductInfo: usecases.ProductInfo{Id: testUUID, Category: usecases.CategoryShelving, Length: 18.0},
			ShelfCount:  3,
		}
		result := adapters.FromUsecaseRenderableProduct(shelving)
		assert.Equal(t, uint8(3), *result.ShelfCount)

		// Shower
		shower := usecases.Shower{
			ProductInfo:           usecases.ProductInfo{Id: testUUID, Category: usecases.CategoryShower, Length: 8.0},
			HasTubSpout:           true,
			HasHandheldShowerhead: ptrTo(false),
		}
		result = adapters.FromUsecaseRenderableProduct(shower)
		assert.Equal(t, true, *result.HasTubSpout)
		assert.Equal(t, false, *result.HandshowerKitIncluded)

		// Tile
		tile := usecases.Tile{
			ProductInfo:             usecases.ProductInfo{Id: testUUID, Category: usecases.CategoryTile, Length: 12.0},
			AvailableForShowerWall:  ptrTo(true),
			AvailableForShowerFloor: ptrTo(false),
			AvailableForFloor:       ptrTo(true),
		}
		result = adapters.FromUsecaseRenderableProduct(tile)
		assert.Equal(t, true, *result.AvailableForShowerWall)
		assert.Equal(t, false, *result.AvailableForShowerFloor)
		assert.Equal(t, true, *result.AvailableForFloor)

		// Toilet with mounting positions
		toiletFloor := usecases.Toilet{
			ProductInfo:      usecases.ProductInfo{Id: testUUID, Category: usecases.CategoryToilet, Length: 28.0},
			MountingPosition: ptrTo(usecases.ToiletMountingPositionFloor),
		}
		result = adapters.FromUsecaseRenderableProduct(toiletFloor)
		assert.Equal(t, string(adapters.ToiletTypeFreestanding), *result.Type)

		toiletWall := usecases.Toilet{
			ProductInfo:      usecases.ProductInfo{Id: testUUID, Category: usecases.CategoryToilet, Length: 24.0},
			MountingPosition: ptrTo(usecases.ToiletMountingPositionWall),
		}
		result = adapters.FromUsecaseRenderableProduct(toiletWall)
		assert.Equal(t, string(adapters.ToiletTypeWallHung), *result.Type)

		// Tub
		tub := usecases.Tub{
			ProductInfo: usecases.ProductInfo{Id: testUUID, Category: usecases.CategoryTub, Length: 60.0},
			Type:        usecases.Freestanding,
			Shape:       ptrTo(usecases.TubShapeRectangle),
		}
		result = adapters.FromUsecaseRenderableProduct(tub)
		assert.Equal(t, usecases.Freestanding, *result.TubType)
		assert.Equal(t, usecases.TubShapeRectangle, *result.Shape)

		// TubFiller
		tubFiller := usecases.TubFiller{
			ProductInfo:      usecases.ProductInfo{Id: testUUID, Category: usecases.CategoryTubFiller, Length: 8.0},
			MountingPosition: ptrTo(usecases.TubFillerMountingPositionFloor),
		}
		result = adapters.FromUsecaseRenderableProduct(tubFiller)
		assert.Equal(t, usecases.TubFillerMountingPositionFloor, *result.MountingPosition)
	})
}

func TestProductInfoToUsecase(t *testing.T) {
	testUUID := uuid.New()

	t.Run("faucet success", func(t *testing.T) {
		input := adapters.ProductInfo{
			Id: testUUID,
			Category: struct {
				Name usecases.Category `json:"name"`
			}{Name: usecases.CategoryFaucet},
			Length:                   "12.50000",
			HoleSpacingCompatibility: []adapters.FaucetHoleSpacing{adapters.SingleHole, adapters.WideSpread},
		}

		result, err := input.ToUsecase()
		require.NoError(t, err)

		faucet, ok := result.(usecases.Faucet)
		require.True(t, ok)
		assert.Equal(t, testUUID, faucet.Id)
		assert.Equal(t, 12.5, faucet.Length)
		assert.Len(t, faucet.HoleSpacingCompatibility, 2)
	})

	t.Run("vanity success", func(t *testing.T) {
		input := adapters.ProductInfo{
			Id: testUUID,
			Category: struct {
				Name usecases.Category `json:"name"`
			}{Name: usecases.CategoryVanity},
			Length:            "48.00000",
			FaucetHoleSpacing: ptrTo(adapters.WideSpread),
			NumberOfSinks:     ptrTo(uint8(2)),
			SinkOffset:        ptrTo("12.50000"),
		}

		result, err := input.ToUsecase()
		require.NoError(t, err)

		vanity, ok := result.(usecases.Vanity)
		require.True(t, ok)
		assert.Equal(t, usecases.WideSpread, vanity.FaucetHoleSpacing)
		assert.Equal(t, uint8(2), vanity.NumberOfSinks)
		assert.Equal(t, 12.5, vanity.SinkOffset)
	})

	t.Run("mirror types", func(t *testing.T) {
		// Medicine cabinet
		input := adapters.ProductInfo{
			Id: testUUID,
			Category: struct {
				Name usecases.Category `json:"name"`
			}{Name: usecases.CategoryMirror},
			Length: "30.00000",
			Type:   ptrTo(string(adapters.MirrorTypeMedicineCabinet)),
		}

		result, err := input.ToUsecase()
		require.NoError(t, err)

		mirror, ok := result.(usecases.Mirror)
		require.True(t, ok)
		assert.True(t, mirror.IsMedicineCabinet)
	})

	t.Run("with color", func(t *testing.T) {
		colorGroup := usecases.White
		input := adapters.ProductInfo{
			Id: testUUID,
			Category: struct {
				Name usecases.Category `json:"name"`
			}{Name: usecases.CategoryFaucet},
			Length: "12.50000",
			ColorGroup: &struct {
				Name usecases.ColorGroup `json:"name"`
			}{Name: colorGroup},
			HoleSpacingCompatibility: []adapters.FaucetHoleSpacing{adapters.SingleHole},
		}

		result, err := input.ToUsecase()
		require.NoError(t, err)

		faucet, ok := result.(usecases.Faucet)
		require.True(t, ok)
		require.NotNil(t, faucet.Color)
		assert.Equal(t, colorGroup, *faucet.Color)
	})

	t.Run("AbovePlacementDefaultRotation logic", func(t *testing.T) {
		// Normal case - no rotation, uses Length
		input := adapters.ProductInfo{
			Id: testUUID,
			Category: struct {
				Name usecases.Category `json:"name"`
			}{Name: usecases.CategoryMirror},
			Length: "24.00000",
			Height: "36.00000",
			Type:   ptrTo(string(adapters.MirrorTypeMirror)),
		}
		result, err := input.ToUsecase()
		require.NoError(t, err)
		mirror, ok := result.(usecases.Mirror)
		require.True(t, ok)
		assert.Equal(t, 24.0, mirror.Length) // Uses Length field

		// 90-degree rotation - uses Height instead of Length
		input.AbovePlacementDefaultRotation = ptrTo(uint16(90))
		result, err = input.ToUsecase()
		require.NoError(t, err)
		mirror, ok = result.(usecases.Mirror)
		require.True(t, ok)
		assert.Equal(t, 36.0, mirror.Length) // Uses Height field when rotated 90°

		// 0-degree rotation - still uses Length
		input.AbovePlacementDefaultRotation = ptrTo(uint16(0))
		result, err = input.ToUsecase()
		require.NoError(t, err)
		mirror, ok = result.(usecases.Mirror)
		require.True(t, ok)
		assert.Equal(t, 24.0, mirror.Length) // Uses Length field

		// 180-degree rotation - uses Length (not Height)
		input.AbovePlacementDefaultRotation = ptrTo(uint16(180))
		result, err = input.ToUsecase()
		require.NoError(t, err)
		mirror, ok = result.(usecases.Mirror)
		require.True(t, ok)
		assert.Equal(t, 24.0, mirror.Length) // Uses Length field
	})

	t.Run("additional product types", func(t *testing.T) {
		// Lighting
		lightingInput := adapters.ProductInfo{
			Id: testUUID,
			Category: struct {
				Name usecases.Category `json:"name"`
			}{Name: usecases.CategoryLighting},
			Length:         "6.00000",
			NumberOfLights: ptrTo(uint8(3)),
		}
		result, err := lightingInput.ToUsecase()
		require.NoError(t, err)
		lighting, ok := result.(usecases.Lighting)
		require.True(t, ok)
		assert.Equal(t, uint8(3), lighting.NumberOfBulbs)

		// Shelving
		shelvingInput := adapters.ProductInfo{
			Id: testUUID,
			Category: struct {
				Name usecases.Category `json:"name"`
			}{Name: usecases.CategoryShelving},
			Length:     "18.00000",
			ShelfCount: ptrTo(uint8(2)),
		}
		result, err = shelvingInput.ToUsecase()
		require.NoError(t, err)
		shelving, ok := result.(usecases.Shelving)
		require.True(t, ok)
		assert.Equal(t, uint8(2), shelving.ShelfCount)

		// Shower
		showerInput := adapters.ProductInfo{
			Id: testUUID,
			Category: struct {
				Name usecases.Category `json:"name"`
			}{Name: usecases.CategoryShower},
			Length:                "8.00000",
			HasTubSpout:           ptrTo(true),
			HandshowerKitIncluded: ptrTo(false),
		}
		result, err = showerInput.ToUsecase()
		require.NoError(t, err)
		shower, ok := result.(usecases.Shower)
		require.True(t, ok)
		assert.Equal(t, true, shower.HasTubSpout)
		assert.Equal(t, false, *shower.HasHandheldShowerhead)

		// Tub
		tubInput := adapters.ProductInfo{
			Id: testUUID,
			Category: struct {
				Name usecases.Category `json:"name"`
			}{Name: usecases.CategoryTub},
			Length:  "60.00000",
			TubType: ptrTo(usecases.Freestanding),
			Shape:   ptrTo(usecases.TubShapeRectangle),
		}
		result, err = tubInput.ToUsecase()
		require.NoError(t, err)
		tub, ok := result.(usecases.Tub)
		require.True(t, ok)
		assert.Equal(t, usecases.Freestanding, tub.Type)
		assert.Equal(t, usecases.TubShapeRectangle, *tub.Shape)

		// TubFiller
		tubFillerInput := adapters.ProductInfo{
			Id: testUUID,
			Category: struct {
				Name usecases.Category `json:"name"`
			}{Name: usecases.CategoryTubFiller},
			Length:           "8.00000",
			MountingPosition: ptrTo(usecases.TubFillerMountingPositionFloor),
		}
		result, err = tubFillerInput.ToUsecase()
		require.NoError(t, err)
		tubFiller, ok := result.(usecases.TubFiller)
		require.True(t, ok)
		assert.Equal(t, usecases.TubFillerMountingPositionFloor, *tubFiller.MountingPosition)

		// Tile (no required fields, just optional ones)
		tileInput := adapters.ProductInfo{
			Id: testUUID,
			Category: struct {
				Name usecases.Category `json:"name"`
			}{Name: usecases.CategoryTile},
			Length:                  "12.00000",
			AvailableForShowerWall:  ptrTo(true),
			AvailableForShowerFloor: ptrTo(false),
			AvailableForFloor:       ptrTo(true),
		}
		result, err = tileInput.ToUsecase()
		require.NoError(t, err)
		tile, ok := result.(usecases.Tile)
		require.True(t, ok)
		assert.Equal(t, true, *tile.AvailableForShowerWall)
		assert.Equal(t, false, *tile.AvailableForShowerFloor)
		assert.Equal(t, true, *tile.AvailableForFloor)
	})
}

func TestProductInfoToUsecaseErrors(t *testing.T) {
	testUUID := uuid.New()

	t.Run("missing required fields", func(t *testing.T) {
		tests := []struct {
			name     string
			input    adapters.ProductInfo
			errorMsg string
		}{
			{
				name: "faucet missing hole spacing",
				input: adapters.ProductInfo{
					Id: testUUID,
					Category: struct {
						Name usecases.Category `json:"name"`
					}{Name: usecases.CategoryFaucet},
					Length:                   "12.50000",
					HoleSpacingCompatibility: []adapters.FaucetHoleSpacing{},
				},
				errorMsg: "hole spacing compatibility missing for faucet",
			},
			{
				name: "vanity missing faucet hole spacing",
				input: adapters.ProductInfo{
					Id: testUUID,
					Category: struct {
						Name usecases.Category `json:"name"`
					}{Name: usecases.CategoryVanity},
					Length:            "48.00000",
					FaucetHoleSpacing: nil,
					NumberOfSinks:     ptrTo(uint8(2)),
					SinkOffset:        ptrTo("12.50000"),
				},
				errorMsg: "faucet hole spacing missing for faucet",
			},
			{
				name: "vanity invalid number of sinks",
				input: adapters.ProductInfo{
					Id: testUUID,
					Category: struct {
						Name usecases.Category `json:"name"`
					}{Name: usecases.CategoryVanity},
					Length:            "48.00000",
					FaucetHoleSpacing: ptrTo(adapters.WideSpread),
					NumberOfSinks:     ptrTo(uint8(4)),
					SinkOffset:        ptrTo("12.50000"),
				},
				errorMsg: "invalid number of sinks (4)",
			},
			{
				name: "lighting missing number of lights",
				input: adapters.ProductInfo{
					Id: testUUID,
					Category: struct {
						Name usecases.Category `json:"name"`
					}{Name: usecases.CategoryLighting},
					Length:         "6.00000",
					NumberOfLights: nil,
				},
				errorMsg: "number of lights missing for lighting",
			},
			{
				name: "shower missing tub spout indicator",
				input: adapters.ProductInfo{
					Id: testUUID,
					Category: struct {
						Name usecases.Category `json:"name"`
					}{Name: usecases.CategoryShower},
					Length:      "8.00000",
					HasTubSpout: nil,
				},
				errorMsg: "tub spout presence indicator missing for shower",
			},
			{
				name: "tub missing type",
				input: adapters.ProductInfo{
					Id: testUUID,
					Category: struct {
						Name usecases.Category `json:"name"`
					}{Name: usecases.CategoryTub},
					Length:  "60.00000",
					TubType: nil,
				},
				errorMsg: "type missing for tub",
			},
			{
				name: "tubfiller missing mounting position",
				input: adapters.ProductInfo{
					Id: testUUID,
					Category: struct {
						Name usecases.Category `json:"name"`
					}{Name: usecases.CategoryTubFiller},
					Length:           "8.00000",
					MountingPosition: nil,
				},
				errorMsg: "mounting position missing for tub filler",
			},
		}

		for _, tt := range tests {
			t.Run(tt.name, func(t *testing.T) {
				_, err := tt.input.ToUsecase()
				assert.Error(t, err)
				assert.Contains(t, err.Error(), tt.errorMsg)
			})
		}
	})

	t.Run("invalid data formats", func(t *testing.T) {
		// Invalid length string
		input := adapters.ProductInfo{
			Id: testUUID,
			Category: struct {
				Name usecases.Category `json:"name"`
			}{Name: usecases.CategoryFaucet},
			Length:                   "invalid",
			HoleSpacingCompatibility: []adapters.FaucetHoleSpacing{adapters.SingleHole},
		}

		_, err := input.ToUsecase()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid syntax")
	})

	t.Run("AbovePlacementDefaultRotation errors", func(t *testing.T) {
		// Invalid rotation > 180
		input := adapters.ProductInfo{
			Id: testUUID,
			Category: struct {
				Name usecases.Category `json:"name"`
			}{Name: usecases.CategoryMirror},
			Length:                        "24.00000",
			Height:                        "36.00000",
			Type:                          ptrTo(string(adapters.MirrorTypeMirror)),
			AbovePlacementDefaultRotation: ptrTo(uint16(270)),
		}
		_, err := input.ToUsecase()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid above placement default rotation (270)")

		// 90 degree rotation with zero height
		input.AbovePlacementDefaultRotation = ptrTo(uint16(90))
		input.Height = "0.00000"
		_, err = input.ToUsecase()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "height of "+testUUID.String()+" is 0 but above placement default rotation is 90")

		// 90 degree rotation with invalid height string
		input.Height = "invalid"
		_, err = input.ToUsecase()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid syntax")
	})
}
