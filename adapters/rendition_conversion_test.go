package adapters_test

import (
	"database/sql"
	"net/url"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

func TestRenditionRoundTripConversion(t *testing.T) {
	t.Run("minimal rendition conversion", func(t *testing.T) {
		// Create a minimal adapter rendition
		adapterRendition := adapters.Rendition{
			Id:        uuid.New(),
			CreatedAt: time.Now().Truncate(time.Second),
			UpdatedAt: time.Now().Truncate(time.Second),
			Status:    adapters.RenditionPending,
		}

		// Convert to entity
		entityRendition, err := adapterRendition.ToDomain()
		require.NoError(t, err)

		// Verify DesignId, Title, Description are not preserved (as expected)
		assert.Nil(t, entityRendition.DesignId)
		assert.False(t, entityRendition.Title.Valid)
		assert.False(t, entityRendition.Description.Valid)

		// Add DesignId, Title, Description to both entity and adapter for testing
		designId := uuid.New()
		title := "Test Rendition"
		description := "Test Description"

		entityRendition.DesignId = &designId
		entityRendition.Title = sql.NullString{String: title, Valid: true}
		entityRendition.Description = sql.NullString{String: description, Valid: true}

		adapterRendition.DesignId = &designId
		adapterRendition.Title = &title
		adapterRendition.Description = &description

		// Convert back to adapter
		roundTripped := adapters.FromDomainRendition(entityRendition)

		// Verify all fields match
		assert.Equal(t, adapterRendition.Id, roundTripped.Id)
		assert.Equal(t, adapterRendition.CreatedAt, roundTripped.CreatedAt)
		assert.Equal(t, adapterRendition.UpdatedAt, roundTripped.UpdatedAt)
		assert.Equal(t, adapterRendition.Status, roundTripped.Status)
		assert.Equal(t, adapterRendition.DesignId, roundTripped.DesignId)
		assert.Equal(t, adapterRendition.Title, roundTripped.Title)
		assert.Equal(t, adapterRendition.Description, roundTripped.Description)
		assert.Equal(t, adapterRendition.URL, roundTripped.URL)
	})

	t.Run("rendition with URL conversion", func(t *testing.T) {
		testURL := "https://example.com/rendition.jpg"

		// Create adapter rendition with URL
		adapterRendition := adapters.Rendition{
			Id:        uuid.New(),
			CreatedAt: time.Now().Truncate(time.Second),
			UpdatedAt: time.Now().Truncate(time.Second),
			Status:    adapters.RenditionCompleted,
			URL:       &testURL,
		}

		// Convert to entity
		entityRendition, err := adapterRendition.ToDomain()
		require.NoError(t, err)

		// Verify URL is properly parsed
		require.NotNil(t, entityRendition.URL)
		assert.Equal(t, testURL, entityRendition.URL.String())

		// Add DesignId, Title, Description to both for testing
		designId := uuid.New()
		title := "Completed Rendition"
		description := "A completed rendition with URL"

		entityRendition.DesignId = &designId
		entityRendition.Title = sql.NullString{String: title, Valid: true}
		entityRendition.Description = sql.NullString{String: description, Valid: true}

		adapterRendition.DesignId = &designId
		adapterRendition.Title = &title
		adapterRendition.Description = &description

		// Convert back to adapter
		roundTripped := adapters.FromDomainRendition(entityRendition)

		// Account for default value.
		adapterRendition.Designer = roundTripped.Designer

		// Verify all fields match
		assert.Equal(t, adapterRendition, roundTripped)
	})

	t.Run("all status values conversion", func(t *testing.T) {
		statusPairs := []struct {
			adapterStatus adapters.RenditionStatus
			entityStatus  entities.RenditionStatus
		}{
			{adapters.RenditionPending, entities.RenditionPending},
			{adapters.RenditionStarted, entities.RenditionStarted},
			{adapters.RenditionCompleted, entities.RenditionCompleted},
			{adapters.RenditionOutdated, entities.RenditionOutdated},
			{adapters.RenditionArchived, entities.RenditionArchived},
		}

		for _, pair := range statusPairs {
			t.Run(string(pair.adapterStatus), func(t *testing.T) {
				// Create adapter rendition with specific status
				adapterRendition := adapters.Rendition{
					Id:        uuid.New(),
					CreatedAt: time.Now().Truncate(time.Second),
					UpdatedAt: time.Now().Truncate(time.Second),
					Status:    pair.adapterStatus,
				}

				// Convert to entity
				entityRendition, err := adapterRendition.ToDomain()
				require.NoError(t, err)
				assert.Equal(t, pair.entityStatus, entityRendition.Status)

				// Add fields for testing
				designId := uuid.New()
				title := "Status Test"
				description := "Testing status conversion"

				entityRendition.DesignId = &designId
				entityRendition.Title = sql.NullString{String: title, Valid: true}
				entityRendition.Description = sql.NullString{String: description, Valid: true}

				adapterRendition.DesignId = &designId
				adapterRendition.Title = &title
				adapterRendition.Description = &description

				// Convert back to adapter
				roundTripped := adapters.FromDomainRendition(entityRendition)

				// Account for default value.
				adapterRendition.Designer = roundTripped.Designer

				// Verify status and all other fields
				assert.Equal(t, adapterRendition, roundTripped)
			})
		}
	})
}

func TestRenditionConversionEdgeCases(t *testing.T) {
	t.Run("empty URL string", func(t *testing.T) {
		emptyURL := ""
		adapterRendition := adapters.Rendition{
			Id:        uuid.New(),
			CreatedAt: time.Now().Truncate(time.Second),
			UpdatedAt: time.Now().Truncate(time.Second),
			Status:    adapters.RenditionPending,
			URL:       &emptyURL,
		}

		// Convert to entity
		entityRendition, err := adapterRendition.ToDomain()
		require.NoError(t, err)

		// Empty URL should result in nil URL in entity
		assert.Nil(t, entityRendition.URL)

		// Convert back to adapter
		roundTripped := adapters.FromDomainRendition(entityRendition)

		// URL should be nil in the round-tripped adapter
		assert.Nil(t, roundTripped.URL)
	})

	t.Run("nil values in entity", func(t *testing.T) {
		// Create entity with nil values
		entityRendition := entities.Rendition{
			Id:          uuid.New(),
			CreatedAt:   time.Now().Truncate(time.Second),
			UpdatedAt:   time.Now().Truncate(time.Second),
			Status:      entities.RenditionPending,
			URL:         nil,
			DesignId:    nil,
			Title:       sql.NullString{Valid: false},
			Description: sql.NullString{Valid: false},
		}

		// Convert to adapter
		adapterRendition := adapters.FromDomainRendition(entityRendition)

		// All pointer fields should be nil
		assert.Nil(t, adapterRendition.URL)
		assert.Nil(t, adapterRendition.DesignId)
		assert.Nil(t, adapterRendition.Title)
		assert.Nil(t, adapterRendition.Description)

		// Convert back to entity
		roundTripped, err := adapterRendition.ToDomain()
		require.NoError(t, err)

		// Core fields should match
		assert.Equal(t, entityRendition.Id, roundTripped.Id)
		assert.Equal(t, entityRendition.CreatedAt, roundTripped.CreatedAt)
		assert.Equal(t, entityRendition.UpdatedAt, roundTripped.UpdatedAt)
		assert.Equal(t, entityRendition.Status, roundTripped.Status)
		assert.Nil(t, roundTripped.URL)
		// Note: DesignId, Title, Description are not preserved in ToDomain()
	})
}

func TestRenditionFieldsNotPreservedInToDomain(t *testing.T) {
	t.Run("DesignId, Title, Description not preserved in ToDomain", func(t *testing.T) {
		designId := uuid.New()
		title := "Test Title"
		description := "Test Description"
		testURL := "https://example.com/test.jpg"

		// Create adapter rendition with all fields
		adapterRendition := adapters.Rendition{
			Id:          uuid.New(),
			CreatedAt:   time.Now().Truncate(time.Second),
			UpdatedAt:   time.Now().Truncate(time.Second),
			Status:      adapters.RenditionCompleted,
			DesignId:    &designId,
			URL:         &testURL,
			Title:       &title,
			Description: &description,
		}

		// Convert to entity
		entityRendition, err := adapterRendition.ToDomain()
		require.NoError(t, err)

		// Verify that DesignId, Title, Description are NOT preserved
		assert.Nil(t, entityRendition.DesignId)
		assert.False(t, entityRendition.Title.Valid)
		assert.False(t, entityRendition.Description.Valid)

		// But other fields should be preserved
		assert.Equal(t, adapterRendition.Id, entityRendition.Id)
		assert.Equal(t, adapterRendition.CreatedAt, entityRendition.CreatedAt)
		assert.Equal(t, adapterRendition.UpdatedAt, entityRendition.UpdatedAt)
		assert.Equal(t, entities.RenditionCompleted, entityRendition.Status)
		require.NotNil(t, entityRendition.URL)
		assert.Equal(t, testURL, entityRendition.URL.String())
	})

	t.Run("FromDomainRendition includes DesignId, Title, Description", func(t *testing.T) {
		designId := uuid.New()
		title := "Entity Title"
		description := "Entity Description"
		testURL, _ := url.Parse("https://example.com/entity.jpg")

		// Create entity rendition with all fields
		entityRendition := entities.Rendition{
			Id:          uuid.New(),
			CreatedAt:   time.Now().Truncate(time.Second),
			UpdatedAt:   time.Now().Truncate(time.Second),
			Status:      entities.RenditionCompleted,
			URL:         testURL,
			DesignId:    &designId,
			Title:       sql.NullString{String: title, Valid: true},
			Description: sql.NullString{String: description, Valid: true},
		}

		// Convert to adapter
		adapterRendition := adapters.FromDomainRendition(entityRendition)

		// Verify that ALL fields are included
		assert.Equal(t, entityRendition.Id, adapterRendition.Id)
		assert.Equal(t, entityRendition.CreatedAt, adapterRendition.CreatedAt)
		assert.Equal(t, entityRendition.UpdatedAt, adapterRendition.UpdatedAt)
		assert.Equal(t, adapters.RenditionCompleted, adapterRendition.Status)
		assert.Equal(t, &designId, adapterRendition.DesignId)
		assert.Equal(t, &title, adapterRendition.Title)
		assert.Equal(t, &description, adapterRendition.Description)
		require.NotNil(t, adapterRendition.URL)
		assert.Equal(t, testURL.String(), *adapterRendition.URL)
	})
}

func TestRenditionToDiff(t *testing.T) {
	t.Run("converts rendition with URL to diff", func(t *testing.T) {
		testURL, err := url.Parse("https://example.com/rendition.jpg")
		require.NoError(t, err)
		urlString := testURL.String()

		adapterRendition := adapters.Rendition{
			Id:        uuid.New(),
			CreatedAt: time.Now().Truncate(time.Second),
			UpdatedAt: time.Now().Truncate(time.Second),
			Status:    adapters.RenditionCompleted,
			URL:       &urlString,
		}

		diff, err := adapterRendition.ToDiff()
		require.NoError(t, err)

		// Verify all fields are correctly converted
		assert.Equal(t, adapterRendition.Id, diff.Id)
		assert.Equal(t, entities.RenditionCompleted, diff.Status)
		require.NotNil(t, diff.URL)
		assert.Equal(t, testURL.String(), diff.URL.String())
	})

	t.Run("converts rendition without URL to diff", func(t *testing.T) {
		adapterRendition := adapters.Rendition{
			Id:        uuid.New(),
			CreatedAt: time.Now().Truncate(time.Second),
			UpdatedAt: time.Now().Truncate(time.Second),
			Status:    adapters.RenditionPending,
			URL:       nil,
		}

		diff, err := adapterRendition.ToDiff()
		require.NoError(t, err)

		// Verify fields are correctly converted
		assert.Equal(t, adapterRendition.Id, diff.Id)
		assert.Equal(t, entities.RenditionPending, diff.Status)
		assert.Nil(t, diff.URL)
	})

	t.Run("converts rendition with empty URL string to diff", func(t *testing.T) {
		emptyURL := ""
		adapterRendition := adapters.Rendition{
			Id:        uuid.New(),
			CreatedAt: time.Now().Truncate(time.Second),
			UpdatedAt: time.Now().Truncate(time.Second),
			Status:    adapters.RenditionStarted,
			URL:       &emptyURL,
		}

		diff, err := adapterRendition.ToDiff()
		require.NoError(t, err)

		// Empty URL should result in nil URL in diff
		assert.Equal(t, adapterRendition.Id, diff.Id)
		assert.Equal(t, entities.RenditionStarted, diff.Status)
		assert.Nil(t, diff.URL)
	})

	t.Run("handles all status types correctly", func(t *testing.T) {
		testCases := []struct {
			adapterStatus adapters.RenditionStatus
			entityStatus  entities.RenditionStatus
		}{
			{adapters.RenditionPending, entities.RenditionPending},
			{adapters.RenditionStarted, entities.RenditionStarted},
			{adapters.RenditionCompleted, entities.RenditionCompleted},
			{adapters.RenditionOutdated, entities.RenditionOutdated},
			{adapters.RenditionArchived, entities.RenditionArchived},
		}

		for _, tc := range testCases {
			t.Run(string(tc.adapterStatus), func(t *testing.T) {
				adapterRendition := adapters.Rendition{
					Id:        uuid.New(),
					CreatedAt: time.Now().Truncate(time.Second),
					UpdatedAt: time.Now().Truncate(time.Second),
					Status:    tc.adapterStatus,
				}

				diff, err := adapterRendition.ToDiff()
				require.NoError(t, err)

				assert.Equal(t, adapterRendition.Id, diff.Id)
				assert.Equal(t, tc.entityStatus, diff.Status)
				assert.Nil(t, diff.URL)
			})
		}
	})

	t.Run("returns error for invalid URL", func(t *testing.T) {
		// Use a URL that will actually cause url.Parse to fail
		invalidURL := "ht tp://invalid url with spaces"
		adapterRendition := adapters.Rendition{
			Id:        uuid.New(),
			CreatedAt: time.Now().Truncate(time.Second),
			UpdatedAt: time.Now().Truncate(time.Second),
			Status:    adapters.RenditionCompleted,
			URL:       &invalidURL,
		}

		_, err := adapterRendition.ToDiff()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "first path segment in URL cannot contain colon")
	})
}
