package adapters

import (
	"errors"

	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type Design struct {
	ID   string `json:"id"`
	Tags int64  `json:"tags"`

	Status      usecases.DesignStatus `json:"status,omitempty"`
	ColorScheme *usecases.ColorScheme `json:"colorScheme,omitempty"`
	Style       *usecases.Style       `json:"style,omitempty"`

	LastUpdatedDateTime *string `json:"lastUpdatedDateTime,omitempty"`
	Title               *string `json:"title,omitempty"`
	Description         *string `json:"description,omitempty"`

	SKUCount     *int32 `json:"skuCount,omitempty"`
	TotalPrice   *int32 `json:"totalPrice,omitempty"`
	LeadTimeDays *int32 `json:"leadTimeDays,omitempty"`

	FloorTile *string `json:"floorTile,omitempty"`
	Toilet    *string `json:"toilet,omitempty"`
	Vanity    *string `json:"vanity,omitempty"`
	Faucet    *string `json:"faucet,omitempty"`
	Mirror    *string `json:"mirror,omitempty"`

	FloorTilePattern       *usecases.TilePattern        `json:"floorTilePattern,omitempty"`
	Lighting               *string                      `json:"lighting,omitempty"`
	NicheTile              *string                      `json:"nicheTile,omitempty"`
	Paint                  *string                      `json:"paint,omitempty"`
	Shelves                *string                      `json:"shelves,omitempty"`
	ShowerFloorTile        *string                      `json:"showerFloorTile,omitempty"`
	ShowerFloorTilePattern *usecases.TilePattern        `json:"showerFloorTilePattern,omitempty"`
	ShowerSystem           *string                      `json:"showerSystem,omitempty"`
	ShowerWallTile         *string                      `json:"showerWallTile,omitempty"`
	ShowerWallTilePattern  *usecases.TilePattern        `json:"showerWallTilePattern,omitempty"`
	ShowerShortWallTile    *string                      `json:"showerShortWallTile,omitempty"`
	ShowerGlass            *string                      `json:"showerGlass,omitempty"`
	Tub                    *string                      `json:"tub,omitempty"`
	TubDoor                *string                      `json:"tubDoor,omitempty"`
	TubFiller              *string                      `json:"tubFiller,omitempty"`
	Wallpaper              *string                      `json:"wallpaper,omitempty"`
	WallpaperPlacement     *usecases.WallpaperPlacement `json:"wallpaperPlacement,omitempty"`
	WallTile               *string                      `json:"wallTile,omitempty"`
	WallTilePlacement      *usecases.WallTilePlacement  `json:"wallTilePlacement,omitempty"`
	WallTilePattern        *usecases.TilePattern        `json:"wallTilePattern,omitempty"`

	IsShowerGlassVisible *bool `json:"isShowerGlassVisible,omitempty"`
	IsTubDoorVisible     *bool `json:"isTubDoorVisible,omitempty"`
	IsNichesVisible      *bool `json:"isNichesVisible,omitempty"`

	Renditions []Rendition `json:"renditions,omitempty"`
}

func (d *Design) AlignId(id string) error {
	if d.ID == "" {
		d.ID = id
	} else if d.ID != id {
		return errors.New("design ID mismatch")
	}
	return nil
}
