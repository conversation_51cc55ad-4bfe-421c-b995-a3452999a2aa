package presenters_test

import (
	"bytes"
	"context"
	"errors"
	"log/slog"
	"net/http/httptest"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// Test helper functions for common testing patterns

// CreateTestLogger creates a logger suitable for testing
func CreateTestLogger() *slog.Logger {
	return slog.Default()
}

// CreateBufferedLogger creates a logger that writes to a buffer for testing log output
func CreateBufferedLogger() (*slog.Logger, *bytes.Buffer) {
	var buf bytes.Buffer
	logger := slog.New(slog.NewTextHandler(&buf, &slog.HandlerOptions{
		Level: slog.LevelDebug,
	}))
	return logger, &buf
}

// CreateSilentLogger creates a logger that discards all output
func CreateSilentLogger() *slog.Logger {
	return slog.New(slog.NewTextHandler(bytes.NewBuffer(nil), &slog.HandlerOptions{
		Level: slog.LevelError + 1, // Higher than any log level to silence all logs
	}))
}

// FakeResponseWriter is a fake implementation that can simulate write failures
// This follows the fake pattern used throughout the codebase instead of mocks
type FakeResponseWriter struct {
	*httptest.ResponseRecorder
	ShouldFailOnWrite bool
	WriteError        error
	WriteCalls        [][]byte // Track all write calls for verification
}

func NewFakeResponseWriter() *FakeResponseWriter {
	return &FakeResponseWriter{
		ResponseRecorder: httptest.NewRecorder(),
		WriteError:       errors.New("fake write failure"),
		WriteCalls:       make([][]byte, 0),
	}
}

func (f *FakeResponseWriter) Write(data []byte) (int, error) {
	f.WriteCalls = append(f.WriteCalls, data)
	if f.ShouldFailOnWrite {
		return 0, f.WriteError
	}
	return f.ResponseRecorder.Write(data)
}

// GetWriteCalls returns all data that was written to this fake
func (f *FakeResponseWriter) GetWriteCalls() [][]byte {
	return f.WriteCalls
}

// Reset clears the state of the fake for reuse
func (f *FakeResponseWriter) Reset() {
	f.ResponseRecorder = httptest.NewRecorder()
	f.ShouldFailOnWrite = false
	f.WriteCalls = make([][]byte, 0)
}

// Test data generators

// GenerateTestDesign creates a test design with all fields populated
func GenerateTestDesign() adapters.Design {
	floorTile := uuid.NewString()
	toilet := uuid.NewString()
	vanity := uuid.NewString()
	faucet := uuid.NewString()
	mirror := uuid.NewString()
	lastUpdated := "2025-06-13T00:59:59Z"
	none := usecases.NoWallpaper
	halfWall := usecases.HalfWall
	defaultFloorTilePattern := usecases.HorizontalStacked
	nope := false
	return adapters.Design{
		ID:                   uuid.NewString(),
		FloorTile:            &floorTile,
		Toilet:               &toilet,
		Vanity:               &vanity,
		Faucet:               &faucet,
		Mirror:               &mirror,
		LastUpdatedDateTime:  &lastUpdated,
		WallpaperPlacement:   &none,
		WallTilePlacement:    &halfWall,
		FloorTilePattern:     &defaultFloorTilePattern,
		IsShowerGlassVisible: &nope,
		IsTubDoorVisible:     &nope,
		IsNichesVisible:      &nope,
	}
}

// GenerateMinimalTestDesign creates a test design with only required fields
func GenerateMinimalTestDesign() adapters.Design {
	return adapters.Design{
		ID: uuid.NewString(),
	}
}

// GenerateTestUsecaseDesign creates a test usecase design
func GenerateTestUsecaseDesign(projectId entities.ProjectId) usecases.Design {
	design := GenerateTestDesign()
	usecaseDesign, err := design.ToUsecaseDesign(projectId)
	if err != nil {
		panic("Failed to create test usecase design: " + err.Error())
	}
	return usecaseDesign
}

// Helper functions for creating pointers to tile patterns
func TilePatternPtr(pattern usecases.TilePattern) *usecases.TilePattern {
	return &pattern
}

// Helper functions for creating test project IDs
func NewTestProjectID(suffix string) entities.ProjectId {
	return entities.ProjectId("PRJ-TEST-" + suffix)
}

// Assertion helpers

// AssertHTTPResponse checks common HTTP response properties
func AssertHTTPResponse(t *testing.T, recorder *httptest.ResponseRecorder, expectedStatus int, expectedBody string) {
	t.Helper()
	assert.Equal(t, expectedStatus, recorder.Code, "HTTP status code mismatch")
	assert.Equal(t, expectedBody, recorder.Body.String(), "HTTP response body mismatch")
}

// AssertCORSHeaders checks that CORS headers are set correctly
func AssertCORSHeaders(t *testing.T, recorder *httptest.ResponseRecorder) {
	t.Helper()
	assert.Equal(t, "*", recorder.Header().Get("Access-Control-Allow-Origin"), "CORS header not set correctly")
}

// AssertJSONContentType checks that the content type is set to JSON
func AssertJSONContentType(t *testing.T, recorder *httptest.ResponseRecorder) {
	t.Helper()
	assert.Equal(t, "application/json", recorder.Header().Get("Content-Type"), "Content-Type header not set to JSON")
}

// AssertLocationHeader checks that the Location header is set correctly
func AssertLocationHeader(t *testing.T, recorder *httptest.ResponseRecorder, projectId entities.ProjectId, designId uuid.UUID) {
	t.Helper()
	expectedLocation := "/projects/" + string(projectId) + "/designs/" + designId.String()
	assert.Equal(t, expectedLocation, recorder.Header().Get("Location"), "Location header not set correctly")
}

// Test data for boundary cases

// UnmarshalableData creates data that cannot be marshaled to JSON (circular reference)
func UnmarshalableData() interface{} {
	type circular struct {
		Self *circular `json:"self"`
	}
	data := &circular{}
	data.Self = data
	return data
}

// Error generators for testing

// CommonTestErrors provides common test errors
var CommonTestErrors = struct {
	Generic    error
	Empty      error
	Database   error
	Network    error
	Validation error
}{
	Generic:    errors.New("generic test error"),
	Empty:      errors.New(""),
	Database:   errors.New("database connection failed"),
	Network:    errors.New("network timeout"),
	Validation: errors.New("validation failed"),
}

// Edge case data generators

// EdgeCaseProjectIDs provides project IDs for edge case testing
var EdgeCaseProjectIDs = struct {
	Empty       entities.ProjectId
	WithSlashes entities.ProjectId
	WithPercent entities.ProjectId
	WithSpaces  entities.ProjectId
	VeryLong    entities.ProjectId
}{
	Empty:       entities.ProjectId(""),
	WithSlashes: entities.ProjectId("PRJ-123/456/789"),
	WithPercent: entities.ProjectId("PRJ-123%456%789"),
	WithSpaces:  entities.ProjectId("PRJ 123 456"),
	VeryLong:    entities.ProjectId("PRJ-" + string(make([]byte, 1000))),
}

// EdgeCaseUUIDs provides UUIDs for edge case testing
var EdgeCaseUUIDs = struct {
	Zero uuid.UUID
	Max  uuid.UUID
}{
	Zero: uuid.UUID{},
	Max:  uuid.UUID{0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff, 0xff},
}

// Fake presenter implementations for integration testing

// FakeDesignsPresenter is a fake implementation of usecases.DesignsPresenter
type FakeDesignsPresenter struct {
	PresentDataCalls             []PresentDataCall
	PresentDesignCalls           []PresentDesignCall
	PresentDesignsCalls          []PresentDesignsCall
	PresentDesignsByProjectCalls []PresentDesignsByProjectCall
	PresentErrorCalls            []error
	LegacyIdMapping              map[uuid.UUID]string
}

type PresentDataCall struct {
	Ctx  context.Context
	Data interface{}
}

type PresentDesignCall struct {
	Ctx    context.Context
	Design usecases.Design
}

type PresentDesignsCall struct {
	Ctx     context.Context
	Designs []usecases.Design
}

type PresentDesignsByProjectCall struct {
	Ctx    context.Context
	Data   map[entities.ProjectId][]usecases.Design
	Errors []error
}

func NewFakeDesignsPresenter() *FakeDesignsPresenter {
	return &FakeDesignsPresenter{
		PresentDataCalls:             make([]PresentDataCall, 0),
		PresentDesignCalls:           make([]PresentDesignCall, 0),
		PresentDesignsCalls:          make([]PresentDesignsCall, 0),
		PresentDesignsByProjectCalls: make([]PresentDesignsByProjectCall, 0),
		PresentErrorCalls:            make([]error, 0),
		LegacyIdMapping:              make(map[uuid.UUID]string),
	}
}

func (f *FakeDesignsPresenter) PresentData(ctx context.Context, data interface{}) {
	f.PresentDataCalls = append(f.PresentDataCalls, PresentDataCall{Ctx: ctx, Data: data})
}

func (f *FakeDesignsPresenter) PresentDesign(ctx context.Context, design usecases.Design) {
	f.PresentDesignCalls = append(f.PresentDesignCalls, PresentDesignCall{Ctx: ctx, Design: design})
}

func (f *FakeDesignsPresenter) PresentDesigns(ctx context.Context, designs []usecases.Design) {
	f.PresentDesignsCalls = append(f.PresentDesignsCalls, PresentDesignsCall{Ctx: ctx, Designs: designs})
}

func (f *FakeDesignsPresenter) PresentDesignsByProject(ctx context.Context, data map[entities.ProjectId][]usecases.Design, errors []error) {
	f.PresentDesignsByProjectCalls = append(f.PresentDesignsByProjectCalls, PresentDesignsByProjectCall{
		Ctx: ctx, Data: data, Errors: errors,
	})
}

func (f *FakeDesignsPresenter) PresentError(err error) {
	f.PresentErrorCalls = append(f.PresentErrorCalls, err)
}

func (f *FakeDesignsPresenter) SetLegacyIdMapping(legacyIdMapping map[uuid.UUID]string) {
	f.LegacyIdMapping = legacyIdMapping
}

// FakeProjectIdPresenter is a fake implementation of usecases.ProjectIdPresenter
type FakeProjectIdPresenter struct {
	PresentProjectIdCalls []PresentProjectIdCall
	PresentErrorCalls     []error
}

type PresentProjectIdCall struct {
	Ctx       context.Context
	ProjectId entities.ProjectId
}

func NewFakeProjectIdPresenter() *FakeProjectIdPresenter {
	return &FakeProjectIdPresenter{
		PresentProjectIdCalls: make([]PresentProjectIdCall, 0),
		PresentErrorCalls:     make([]error, 0),
	}
}

func (f *FakeProjectIdPresenter) PresentProjectId(ctx context.Context, projectId entities.ProjectId) {
	f.PresentProjectIdCalls = append(f.PresentProjectIdCalls, PresentProjectIdCall{Ctx: ctx, ProjectId: projectId})
}

func (f *FakeProjectIdPresenter) PresentError(err error) {
	f.PresentErrorCalls = append(f.PresentErrorCalls, err)
}

// FakeDesignPackagesPresenter is a fake implementation of usecases.DesignPackagesPresenter
type FakeDesignPackagesPresenter struct {
	PresentDesignPackageCalls  []PresentDesignPackageCall
	PresentDesignPackagesCalls []PresentDesignPackagesCall
	PresentErrorCalls          []error
}

type PresentDesignPackageCall struct {
	Ctx       context.Context
	DesignPkg usecases.DesignPackage
}

type PresentDesignPackagesCall struct {
	Ctx        context.Context
	DesignPkgs []usecases.DesignPackage
}

func NewFakeDesignPackagesPresenter() *FakeDesignPackagesPresenter {
	return &FakeDesignPackagesPresenter{
		PresentDesignPackageCalls:  make([]PresentDesignPackageCall, 0),
		PresentDesignPackagesCalls: make([]PresentDesignPackagesCall, 0),
		PresentErrorCalls:          make([]error, 0),
	}
}

func (f *FakeDesignPackagesPresenter) PresentDesignPackage(ctx context.Context, designPkg usecases.DesignPackage) {
	f.PresentDesignPackageCalls = append(f.PresentDesignPackageCalls, PresentDesignPackageCall{Ctx: ctx, DesignPkg: designPkg})
}

func (f *FakeDesignPackagesPresenter) PresentDesignPackages(ctx context.Context, designPkgs []usecases.DesignPackage) {
	f.PresentDesignPackagesCalls = append(f.PresentDesignPackagesCalls, PresentDesignPackagesCall{Ctx: ctx, DesignPkgs: designPkgs})
}

func (f *FakeDesignPackagesPresenter) PresentError(err error) {
	f.PresentErrorCalls = append(f.PresentErrorCalls, err)
}

// FakeDesignMutationOutcomePresenter is a fake implementation of usecases.DesignMutationOutcomePresenter
type FakeDesignMutationOutcomePresenter struct {
	ConveySuccessCalls             []bool
	ConveySuccessWithResourceCalls []ConveySuccessWithResourceCall
	PresentErrorCalls              []error
}

type ConveySuccessWithResourceCall struct {
	Design usecases.Design
	Status usecases.Status
}

func NewFakeDesignMutationOutcomePresenter() *FakeDesignMutationOutcomePresenter {
	return &FakeDesignMutationOutcomePresenter{
		ConveySuccessCalls:             make([]bool, 0),
		ConveySuccessWithResourceCalls: make([]ConveySuccessWithResourceCall, 0),
		PresentErrorCalls:              make([]error, 0),
	}
}

func (f *FakeDesignMutationOutcomePresenter) ConveySuccess() {
	f.ConveySuccessCalls = append(f.ConveySuccessCalls, true)
}

func (f *FakeDesignMutationOutcomePresenter) ConveySuccessWithResource(design usecases.Design, status usecases.Status) {
	f.ConveySuccessWithResourceCalls = append(f.ConveySuccessWithResourceCalls, ConveySuccessWithResourceCall{
		Design: design, Status: status,
	})
}

func (f *FakeDesignMutationOutcomePresenter) PresentError(err error) {
	f.PresentErrorCalls = append(f.PresentErrorCalls, err)
}

// Backward compatibility function for existing tests
func NewMockFailingResponseWriter() *FakeResponseWriter {
	return NewFakeResponseWriter()
}

// Helper functions for fake verification

// AssertPresentErrorCalled checks if PresentError was called with the expected error
func AssertPresentErrorCalled(t *testing.T, fake interface{}, expectedError error) {
	t.Helper()
	switch f := fake.(type) {
	case *FakeDesignsPresenter:
		assert.Contains(t, f.PresentErrorCalls, expectedError, "Expected PresentError to be called with error: %v", expectedError)
	case *FakeProjectIdPresenter:
		assert.Contains(t, f.PresentErrorCalls, expectedError, "Expected PresentError to be called with error: %v", expectedError)
	case *FakeDesignPackagesPresenter:
		assert.Contains(t, f.PresentErrorCalls, expectedError, "Expected PresentError to be called with error: %v", expectedError)
	case *FakeDesignMutationOutcomePresenter:
		assert.Contains(t, f.PresentErrorCalls, expectedError, "Expected PresentError to be called with error: %v", expectedError)
	default:
		t.Errorf("Unsupported fake type: %T", fake)
	}
}

// AssertPresentErrorNotCalled checks if PresentError was not called
func AssertPresentErrorNotCalled(t *testing.T, fake interface{}) {
	t.Helper()
	switch f := fake.(type) {
	case *FakeDesignsPresenter:
		assert.Empty(t, f.PresentErrorCalls, "Expected PresentError not to be called")
	case *FakeProjectIdPresenter:
		assert.Empty(t, f.PresentErrorCalls, "Expected PresentError not to be called")
	case *FakeDesignPackagesPresenter:
		assert.Empty(t, f.PresentErrorCalls, "Expected PresentError not to be called")
	case *FakeDesignMutationOutcomePresenter:
		assert.Empty(t, f.PresentErrorCalls, "Expected PresentError not to be called")
	default:
		t.Errorf("Unsupported fake type: %T", fake)
	}
}

// Test table helpers

// ErrorTestCase represents a test case for error handling
type ErrorTestCase struct {
	Name           string
	Error          error
	ExpectedStatus int
	ExpectedBody   string
}

// SuccessTestCase represents a test case for success scenarios
type SuccessTestCase struct {
	Name           string
	ProjectID      *entities.ProjectId
	DesignID       *uuid.UUID
	ExpectedStatus int
	ExpectedBody   string
}

// BoundaryTestCase represents a test case for boundary conditions
type BoundaryTestCase struct {
	Name        string
	Input       interface{}
	ExpectedOut interface{}
	ShouldPanic bool
}
