package presenters

import (
	"bytes"
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"net/http"
	"strings"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type EventPresenter struct {
	w       http.ResponseWriter
	flusher http.Flusher
	logger  *slog.Logger
}

func NewEventPresenter(logger *slog.Logger, w http.ResponseWriter) (ep EventPresenter) {
	if usecases.IsNil(w) {
		panic("w cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	ep = EventPresenter{logger: logger, w: w}
	var ok bool
	if ep.flusher, ok = w.(http.Flusher); !ok {
		http.Error(w, "SSE not supported", http.StatusInternalServerError)
		return
	}
	w.Header().Set("Access-Control-Expose-Headers", "Content-Type")
	w.<PERSON><PERSON>().Set("Content-Type", "text/event-stream; charset=utf-8")
	w.<PERSON><PERSON>().Set("Cache-Control", "no-store")
	w.Header().Set("Connection", "keep-alive")
	w.Header().Set("Access-Control-Allow-Origin", "*")
	w.Header().Set("X-Accel-Buffering", "no")
	return
}

func (ep EventPresenter) SendEvent(ctx context.Context, eventName string, data any) (err error) {
	buff := bytes.Buffer{}
	encoder := json.NewEncoder(&buff)
	err = encoder.Encode(data)
	if err != nil {
		ep.logger.ErrorContext(ctx, "Could not encode data as JSON",
			slog.String("error", err.Error()))
		http.Error(ep.w, err.Error(), http.StatusInternalServerError)
		return err
	}
	sb := strings.Builder{}
	if eventName != "" {
		sb.WriteString(fmt.Sprintf("event: %s\n", eventName))
	}
	sb.WriteString(fmt.Sprintf("data: %v\n", buff.String()))
	if _, err = fmt.Fprintln(ep.w, sb.String()); err != nil {
		ep.logger.ErrorContext(ctx, "Could not write data to response",
			slog.String("error", err.Error()))
		http.Error(ep.w, err.Error(), http.StatusInternalServerError)
		return err
	}
	ep.flusher.Flush()
	return nil
}

func (ep EventPresenter) Close(ctx context.Context) {
	sb := strings.Builder{}
	sb.WriteString(fmt.Sprintln("event: close"))
	sb.WriteString(fmt.Sprintln("data:"))
	sb.WriteString(fmt.Sprintln("data:"))
	if _, err := fmt.Fprintln(ep.w, sb.String()); err != nil {
		ep.logger.ErrorContext(ctx, "Could not send close event",
			slog.String("error", err.Error()))
	}
	ep.flusher.Flush()
}

type DesignStreamer struct {
	EventPresenter
}

func NewDesignStreamer(logger *slog.Logger, w http.ResponseWriter) *DesignStreamer {
	return &DesignStreamer{EventPresenter: NewEventPresenter(logger, w)}
}

func (ds *DesignStreamer) SendEvent(ctx context.Context, eventName string, data any) (err error) {
	switch data := data.(type) {
	case usecases.Design:
		d := adapters.FromUsecaseDesign(data)
		return ds.EventPresenter.SendEvent(ctx, "design", d)
	default:
		return ds.EventPresenter.SendEvent(ctx, eventName, data)
	}
}
