package presenters

import (
	"context"
	"encoding/json"
	"fmt"
	"log/slog"
	"net/http"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type DesignPackagePresenter struct {
	DataPresenter
	useLegacyIds bool
}

func NewDesignPackagesPresenter(logger *slog.Logger, w http.ResponseWriter, useLegacyIds bool) *DesignPackagePresenter {
	return &DesignPackagePresenter{DataPresenter: NewDataPresenter(logger, w), useLegacyIds: useLegacyIds}
}

func (tp *DesignPackagePresenter) PresentDesignPackage(ctx context.Context, designPkg usecases.DesignPackage) {
	output := adapters.FromUsecaseDesignPackage(designPkg, tp.useLegacyIds)
	wrappedOutput := map[string]any{
		"data": []adapters.DesignPackage{output},
	}
	tp.PresentData(ctx, wrappedOutput)
}

func (tp *DesignPackagePresenter) PresentDesignPackages(ctx context.Context, designPkgs []usecases.DesignPackage) {
	output := make([]adapters.DesignPackage, len(designPkgs))
	for i, t := range designPkgs {
		output[i] = adapters.FromUsecaseDesignPackage(t, tp.useLegacyIds)
	}
	wrappedOutput := map[string]any{
		"data": output,
	}
	tp.PresentData(ctx, wrappedOutput)
}

// DesignPackageCreationOutcomePresenter handles the presentation of design package creation/mutation outcomes
type DesignPackageCreationOutcomePresenter struct {
	OutcomePresenter
}

func NewDesignPackageCreationOutcomePresenter(logger *slog.Logger, w http.ResponseWriter) *DesignPackageCreationOutcomePresenter {
	return &DesignPackageCreationOutcomePresenter{OutcomePresenter: NewOutcomePresenter(logger, w)}
}

func (p *DesignPackageCreationOutcomePresenter) ConveySuccessWithNewResource(designPkg usecases.DesignPackage) {
	p.w.Header().Set("Access-Control-Allow-Origin", "*")
	var zeroUUID uuid.UUID
	if designPkg.ID == uuid.Nil || designPkg.ID == zeroUUID {
		http.Error(p.w, "Missing/invalid ID in created design package", http.StatusInternalServerError)
		return
	}
	p.w.Header().Set("Location", fmt.Sprintf("/templates/%s", designPkg.ID))
	p.w.Header().Set("Content-Type", "application/json")
	p.w.WriteHeader(http.StatusCreated)

	output := adapters.FromUsecaseDesignPackage(designPkg, false)
	wrappedOutput := map[string]any{
		"data": output,
	}
	response, err := json.MarshalIndent(wrappedOutput, "", "  ")
	if err != nil {
		http.Error(p.w, err.Error(), http.StatusInternalServerError)
		return
	}
	if _, err := p.w.Write(response); err != nil {
		p.logger.Error("Failed to write response", slog.String("error message", err.Error()))
	}
}
