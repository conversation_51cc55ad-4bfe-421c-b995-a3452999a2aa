package presenters_test

import (
	"context"
	"encoding/json"
	"log/slog"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/presenters"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

func TestNewDesignPackagesPresenter(t *testing.T) {
	logger := slog.Default()
	w := httptest.NewRecorder()

	presenter := presenters.NewDesignPackagesPresenter(logger, w, false)
	assert.NotNil(t, presenter)
}

func TestDesignPackagesPresenter_PresentDesignPackage(t *testing.T) {
	ctx := context.Background()
	logger := slog.Default()
	w := httptest.NewRecorder()
	presenter := presenters.NewDesignPackagesPresenter(logger, w, false)

	testDesignPkg := usecases.DesignPackage{
		ID:   uuid.New(),
		Name: "Test Design Package",
		Tagged: usecases.Tagged{
			ColorScheme: usecases.Neutral,
			Style:       usecases.Modern,
		},
		Description:     "A test design package",
		Inspiration:     "Test inspiration",
		Atmosphere:      []string{"cozy", "modern"},
		ColorPalette:    []string{"blue", "gold"},
		MaterialPalette: []string{"wood", "metal"},
	}

	presenter.PresentDesignPackage(ctx, testDesignPkg)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.Equal(t, "application/json", w.Header().Get("Content-Type"))
	assert.Equal(t, "*", w.Header().Get("Access-Control-Allow-Origin"))

	var responseWrapper map[string][]any
	err := json.Unmarshal(w.Body.Bytes(), &responseWrapper)
	require.NoError(t, err)

	// Extract the design package from the data field
	designPkgData, ok := responseWrapper["data"][0].(map[string]any)
	require.True(t, ok, "Expected 'data' field to contain design package object")

	assert.Equal(t, testDesignPkg.ID.String(), designPkgData["id"])
	assert.Equal(t, testDesignPkg.Name, designPkgData["name"])
	assert.NotContains(t, designPkgData, "wallTile")
}

func TestDesignPackagesPresenter_PresentDesignPackages(t *testing.T) {
	ctx := context.Background()
	logger := slog.Default()
	w := httptest.NewRecorder()
	presenter := presenters.NewDesignPackagesPresenter(logger, w, false)

	testDesignPkgs := []usecases.DesignPackage{
		{
			ID:   uuid.New(),
			Name: "Design Package 1",
			Tagged: usecases.Tagged{
				ColorScheme: usecases.Neutral,
				Style:       usecases.Modern,
			},
		},
		{
			ID:   uuid.New(),
			Name: "Design Package 2",
			Tagged: usecases.Tagged{
				ColorScheme: usecases.Bold,
				Style:       usecases.Traditional,
			},
		},
	}

	presenter.PresentDesignPackages(ctx, testDesignPkgs)

	assert.Equal(t, http.StatusOK, w.Code)
	assert.Equal(t, "application/json", w.Header().Get("Content-Type"))

	var responseWrapper map[string]any
	err := json.Unmarshal(w.Body.Bytes(), &responseWrapper)
	require.NoError(t, err)

	// Extract the design packages array from the data field
	designPkgsData, ok := responseWrapper["data"].([]any)
	require.True(t, ok, "Expected 'data' field to contain design packages array")

	assert.Len(t, designPkgsData, 2)

	// Check first design package
	designPkg1, ok := designPkgsData[0].(map[string]any)
	require.True(t, ok)
	assert.Equal(t, testDesignPkgs[0].Name, designPkg1["name"])
	assert.Equal(t, testDesignPkgs[0].ID.String(), designPkg1["materials"].(map[string]any)["id"])

	// Check second design package
	designPkg2, ok := designPkgsData[1].(map[string]any)
	require.True(t, ok)
	assert.Equal(t, testDesignPkgs[1].Name, designPkg2["name"])
	assert.Equal(t, testDesignPkgs[1].ID.String(), designPkg2["materials"].(map[string]any)["id"])
}

func TestNewDesignPackageCreationOutcomePresenter(t *testing.T) {
	logger := slog.Default()
	w := httptest.NewRecorder()

	presenter := presenters.NewDesignPackageCreationOutcomePresenter(logger, w)
	assert.NotNil(t, presenter)
}

func TestDesignPackageCreationOutcomePresenter_ConveySuccessWithNewResource(t *testing.T) {
	logger := slog.Default()
	w := httptest.NewRecorder()
	presenter := presenters.NewDesignPackageCreationOutcomePresenter(logger, w)

	testDesignPkg := usecases.DesignPackage{
		ID:   uuid.New(),
		Name: "Test Design Package",
		Tagged: usecases.Tagged{
			ColorScheme: usecases.Neutral,
			Style:       usecases.Modern,
		},
		Description:     "A test design package",
		Inspiration:     "Test inspiration",
		Atmosphere:      []string{"cozy", "modern"},
		ColorPalette:    []string{"#FFFFFF", "#000000"},
		MaterialPalette: []string{"wood", "metal"},
	}

	presenter.ConveySuccessWithNewResource(testDesignPkg)

	assert.Equal(t, http.StatusCreated, w.Code)
	assert.Equal(t, "application/json", w.Header().Get("Content-Type"))
	assert.Equal(t, "*", w.Header().Get("Access-Control-Allow-Origin"))
	assert.Contains(t, w.Header().Get("Location"), testDesignPkg.ID.String())

	// Verify response body contains the design package wrapped in data field
	var responseWrapper map[string]any
	err := json.Unmarshal(w.Body.Bytes(), &responseWrapper)
	require.NoError(t, err)

	// Extract the design package from the data field
	designPkgData, ok := responseWrapper["data"].(map[string]any)
	require.True(t, ok, "Expected 'data' field to contain design package object")

	assert.Equal(t, testDesignPkg.ID.String(), designPkgData["id"])
	assert.Equal(t, testDesignPkg.Name, designPkgData["name"])
}

func TestDesignPackageCreationOutcomePresenter_PresentError(t *testing.T) {
	logger := slog.Default()

	testCases := []struct {
		name           string
		error          error
		expectedStatus int
		expectedBody   string
	}{
		{
			name:           "invalid payload error",
			error:          usecases.ErrInvalidPayload,
			expectedStatus: http.StatusBadRequest,
			expectedBody:   "invalid payload",
		},
		{
			name:           "not found error",
			error:          usecases.ErrNotFound,
			expectedStatus: http.StatusNotFound,
			expectedBody:   "not found",
		},
		{
			name:           "conflict error",
			error:          usecases.ErrConflict,
			expectedStatus: http.StatusConflict,
			expectedBody:   "with the same ID already exists",
		},
		{
			name:           "generic error",
			error:          assert.AnError,
			expectedStatus: http.StatusInternalServerError,
			expectedBody:   assert.AnError.Error(),
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			w := httptest.NewRecorder()
			presenter := presenters.NewDesignPackageCreationOutcomePresenter(logger, w)

			presenter.PresentError(tc.error)

			assert.Equal(t, tc.expectedStatus, w.Code)
			assert.Equal(t, "*", w.Header().Get("Access-Control-Allow-Origin"))
			assert.Contains(t, w.Body.String(), tc.expectedBody)
		})
	}
}

func TestDesignPackageCreationOutcomePresenter_ConveySuccessWithNewResource_InvalidDesignPackage(t *testing.T) {
	logger := slog.Default()
	w := httptest.NewRecorder()
	presenter := presenters.NewDesignPackageCreationOutcomePresenter(logger, w)

	// Design package with nil/zero UUID
	invalidDesignPkg := usecases.DesignPackage{
		ID:   uuid.Nil,
		Name: "Invalid Design Package",
	}

	presenter.ConveySuccessWithNewResource(invalidDesignPkg)

	assert.Equal(t, http.StatusInternalServerError, w.Code)
	assert.Contains(t, w.Body.String(), "Missing/invalid ID in created design package")
}
