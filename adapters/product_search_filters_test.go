package adapters_test

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// Helper functions for common test patterns

// assertPointerValue checks that a pointer is not nil and has the expected value
func assertPointerValue[T comparable](t *testing.T, ptr *T, expected T) {
	t.Helper()
	require.NotNil(t, ptr)
	assert.Equal(t, expected, *ptr)
}

// assertUIntRange checks min and max values of a UIntRange
func assertUIntRange(t *testing.T, r usecases.UIntRange, expectedMin uint, expectedMax *uint) {
	t.Helper()
	assert.Equal(t, expectedMin, r.Min)
	if expectedMax != nil {
		assertPointerValue(t, r.Max, *expectedMax)
	} else {
		assert.Nil(t, r.Max)
	}
}

// assertFloatRange checks min and max values of a FloatRange
func assertFloatRange(t *testing.T, r usecases.FloatRange, expectedMin float64, expectedMax *float64) {
	t.Helper()
	assert.Equal(t, expectedMin, r.Min)
	if expectedMax != nil {
		assertPointerValue(t, r.Max, *expectedMax)
	} else {
		assert.Nil(t, r.Max)
	}
}

func TestFilterParamsToUsecase(t *testing.T) {
	tests := []struct {
		name     string
		input    adapters.FilterParams
		validate func(t *testing.T, result usecases.ProductSearchFilters)
	}{
		{
			name:  "minimal conversion",
			input: adapters.FilterParams{Category: usecases.CategoryVanity},
			validate: func(t *testing.T, result usecases.ProductSearchFilters) {
				assert.Equal(t, usecases.CategoryVanity, result.Category)
				assertUIntRange(t, result.PriceRange, 0, nil)
				assert.Nil(t, result.MaxLeadTime)
				assert.Empty(t, result.Collections)
			},
		},
		{
			name: "price and lead time",
			input: adapters.FilterParams{
				Category: usecases.CategoryFaucet, PriceGTE: ptr(uint(1000)), PriceLTE: ptr(uint(5000)), LeadTimeLTE: ptr(uint(30)),
			},
			validate: func(t *testing.T, result usecases.ProductSearchFilters) {
				assertUIntRange(t, result.PriceRange, 1000, ptr(uint(5000)))
				assertPointerValue(t, result.MaxLeadTime, uint(30))
			},
		},
		{
			name:  "collections and faucet spacing",
			input: adapters.FilterParams{Category: usecases.CategoryFaucet, Collection: ptr("Modern"), FaucetHoleSpacingCompatibility: ptr(adapters.SingleHole)},
			validate: func(t *testing.T, result usecases.ProductSearchFilters) {
				require.Len(t, result.Collections, 1)
				assert.Equal(t, "Modern", result.Collections[0])
				assertPointerValue(t, result.FaucetHoleSpacingCompatibility, usecases.SingleHole)
			},
		},
		{
			name: "lighting filters",
			input: adapters.FilterParams{
				Category: usecases.CategoryLighting, LightingTypes: []usecases.LightingType{usecases.Sconce},
				NumberOfLightsGTE: ptr(uint(2)), NumberOfLightsLTE: ptr(uint(6)), LightingLengthLTE: ptr(float64(48.0)),
			},
			validate: func(t *testing.T, result usecases.ProductSearchFilters) {
				assert.Equal(t, []usecases.LightingType{usecases.Sconce}, result.LightingTypes)
				assertUIntRange(t, result.LightingNumBulbs, 2, ptr(uint(6)))
				assertFloatRange(t, result.Length, 0, ptr(float64(48.0)))
			},
		},
		{
			name: "mirror filters",
			input: adapters.FilterParams{
				Category: usecases.CategoryMirror, MirrorType: ptr(adapters.MirrorTypeMedicineCabinet), MirrorHasLED: ptr(true),
				MirrorHeightGTE: ptr(float64(24.0)), MirrorHeightLTE: ptr(float64(36.0)), MirrorWidthGTE: ptr(float64(18.0)), MirrorWidthLTE: ptr(float64(30.0)),
			},
			validate: func(t *testing.T, result usecases.ProductSearchFilters) {
				assertPointerValue(t, result.MirrorIsMedicineCabinet, true)
				assertPointerValue(t, result.MirrorIsLighted, true)
				assertFloatRange(t, result.Height, 24.0, ptr(float64(36.0)))
				assertFloatRange(t, result.Length, 18.0, ptr(float64(30.0)))
			},
		},
		{
			name: "toilet and vanity types",
			input: adapters.FilterParams{
				Category: usecases.CategoryToilet, ToiletType: ptr(adapters.ToiletTypeWallHung), HasBidet: ptr(true),
			},
			validate: func(t *testing.T, result usecases.ProductSearchFilters) {
				assertPointerValue(t, result.ToiletMountingPosition, usecases.ToiletMountingPositionWall)
				assertPointerValue(t, result.ToiletMustHaveBidet, true)
			},
		},
		{
			name: "edge cases",
			input: adapters.FilterParams{
				Category: usecases.CategoryFaucet, FaucetHoleSpacingCompatibility: ptr(adapters.FaucetHoleSpacing("INVALID")),
			},
			validate: func(t *testing.T, result usecases.ProductSearchFilters) {
				assert.Nil(t, result.FaucetHoleSpacingCompatibility)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := tt.input.ToUsecase()
			tt.validate(t, result)
		})
	}
}

func TestFiltersFromUsecase(t *testing.T) {
	tests := []struct {
		name     string
		input    usecases.ProductSearchFilters
		validate func(t *testing.T, result adapters.FilterParams)
	}{
		{
			name:  "minimal conversion",
			input: usecases.ProductSearchFilters{Category: usecases.CategoryVanity},
			validate: func(t *testing.T, result adapters.FilterParams) {
				assert.Equal(t, usecases.CategoryVanity, result.Category)
				assert.Nil(t, result.PriceGTE)
				assert.Nil(t, result.Collection)
			},
		},
		{
			name: "price range",
			input: usecases.ProductSearchFilters{
				Category:   usecases.CategoryFaucet,
				PriceRange: usecases.UIntRange{Min: 1000, Max: ptr(uint(5000))},
			},
			validate: func(t *testing.T, result adapters.FilterParams) {
				assertPointerValue(t, result.PriceGTE, uint(1000))
				assertPointerValue(t, result.PriceLTE, uint(5000))
			},
		},
		{
			name: "zero min price",
			input: usecases.ProductSearchFilters{
				Category:   usecases.CategoryFaucet,
				PriceRange: usecases.UIntRange{Min: 0, Max: ptr(uint(5000))},
			},
			validate: func(t *testing.T, result adapters.FilterParams) {
				assert.Nil(t, result.PriceGTE)
				assertPointerValue(t, result.PriceLTE, uint(5000))
			},
		},
		{
			name:  "collections",
			input: usecases.ProductSearchFilters{Category: usecases.CategoryMirror, Collections: []string{"Modern", "Traditional"}},
			validate: func(t *testing.T, result adapters.FilterParams) {
				assertPointerValue(t, result.Collection, "Modern") // Only first is used
			},
		},
		{
			name:  "empty collections",
			input: usecases.ProductSearchFilters{Category: usecases.CategoryMirror, Collections: []string{}},
			validate: func(t *testing.T, result adapters.FilterParams) {
				assert.Nil(t, result.Collection)
			},
		},
		{
			name: "mirror medicine cabinet",
			input: usecases.ProductSearchFilters{
				Category: usecases.CategoryMirror, MirrorIsMedicineCabinet: ptr(true), MirrorIsLighted: ptr(false),
				Height: usecases.FloatRange{Min: 24.0, Max: ptr(float64(36.0))},
			},
			validate: func(t *testing.T, result adapters.FilterParams) {
				assertPointerValue(t, result.MirrorType, adapters.MirrorTypeMedicineCabinet)
				assertPointerValue(t, result.MirrorHasLED, false)
				assertPointerValue(t, result.MirrorHeightGTE, float64(24.0))
				assertPointerValue(t, result.MirrorHeightLTE, float64(36.0))
			},
		},
		{
			name:  "regular mirror",
			input: usecases.ProductSearchFilters{Category: usecases.CategoryMirror, MirrorIsMedicineCabinet: ptr(false)},
			validate: func(t *testing.T, result adapters.FilterParams) {
				assertPointerValue(t, result.MirrorType, adapters.MirrorTypeMirror)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := adapters.FiltersFromUsecase(tt.input)
			tt.validate(t, result)
		})
	}
}

func TestRoundTripConversions(t *testing.T) {
	tests := []struct {
		name  string
		input adapters.FilterParams
	}{
		{
			name: "basic fields",
			input: adapters.FilterParams{
				Category: usecases.CategoryVanity, PriceGTE: ptr(uint(1000)), Colors: []usecases.ColorGroup{usecases.White},
			},
		},
		{
			name:  "mirror type",
			input: adapters.FilterParams{Category: usecases.CategoryMirror, MirrorType: ptr(adapters.MirrorTypeMedicineCabinet)},
		},
		{
			name:  "toilet type",
			input: adapters.FilterParams{Category: usecases.CategoryToilet, ToiletType: ptr(adapters.ToiletTypeWallHung)},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			usecaseFilters := tt.input.ToUsecase()
			roundTripped := adapters.FiltersFromUsecase(usecaseFilters)
			assert.Equal(t, tt.input.Category, roundTripped.Category)

			// Check that key fields round-trip correctly
			if tt.input.PriceGTE != nil {
				assertPointerValue(t, roundTripped.PriceGTE, *tt.input.PriceGTE)
			}
			if tt.input.MirrorType != nil {
				assertPointerValue(t, roundTripped.MirrorType, *tt.input.MirrorType)
			}
			if tt.input.ToiletType != nil {
				assertPointerValue(t, roundTripped.ToiletType, *tt.input.ToiletType)
			}
		})
	}
}

func TestEdgeCases(t *testing.T) {
	t.Run("nil values", func(t *testing.T) {
		filterParams := adapters.FilterParams{Category: usecases.CategoryFaucet}
		result := filterParams.ToUsecase()
		assert.Equal(t, usecases.CategoryFaucet, result.Category)
		assertUIntRange(t, result.PriceRange, 0, nil)
		assert.Nil(t, result.MaxLeadTime)
	})

	t.Run("empty slices", func(t *testing.T) {
		result := adapters.FiltersFromUsecase(usecases.ProductSearchFilters{
			Category: usecases.CategoryWallpaper, Collections: []string{}, LightingTypes: []usecases.LightingType{},
		})
		assert.Equal(t, usecases.CategoryWallpaper, result.Category)
		assert.Nil(t, result.Collection)
		assert.Empty(t, result.LightingTypes)
	})

	t.Run("multiple values use first", func(t *testing.T) {
		result := adapters.FiltersFromUsecase(usecases.ProductSearchFilters{
			Category: usecases.CategoryMirror, Collections: []string{"First", "Second"},
		})
		assertPointerValue(t, result.Collection, "First")
	})
}
