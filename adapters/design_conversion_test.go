package adapters_test

import (
	"database/sql"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

var defaultFloorTilePattern = usecases.HorizontalStacked

func TestDesignRoundTripConversions(t *testing.T) {
	tests := []struct {
		name          string
		design        adapters.Design
		expectedDiff  func(*adapters.Design)
		checkDefaults func(*testing.T, usecases.Design)
	}{
		{
			name:   "minimal",
			design: adapters.Design{ID: uuid.NewString()},
			expectedDiff: func(d *adapters.Design) {
				d.WallTilePlacement = ptr(usecases.NoWallTile)
				d.WallpaperPlacement = ptr(usecases.VanityWall)
				d.FloorTilePattern = ptr(defaultFloorTilePattern)
			},
		},
		{
			name: "trivial",
			design: adapters.Design{
				ID: uuid.NewString(), Tags: 0, LastUpdatedDateTime: ptr("2025-06-13T00:59:59Z"),
				WallpaperPlacement: ptr(usecases.VanityWall), WallTilePlacement: ptr(usecases.NoWallTile),
				FloorTilePattern: ptr(defaultFloorTilePattern),
			},
		},
		{
			name: "typical",
			design: adapters.Design{
				ID: uuid.NewString(), Tags: 36, LastUpdatedDateTime: ptr("2025-06-13T00:59:59Z"),
				Title: ptr("Test Design"), FloorTile: uuidStrPtr(), Toilet: uuidStrPtr(), Vanity: uuidStrPtr(),
				WallpaperPlacement: ptr(usecases.VanityWall), WallTilePlacement: ptr(usecases.HalfWall),
				IsNichesVisible: ptr(true),
			},
			expectedDiff: func(d *adapters.Design) {
				d.ColorScheme = ptr(usecases.Neutral)
				d.Style = ptr(usecases.Modern)
				d.FloorTilePattern = ptr(defaultFloorTilePattern)
			},
			checkDefaults: func(t *testing.T, uc usecases.Design) {
				assert.Equal(t, usecases.Neutral, *uc.ColorScheme)
				assert.Equal(t, usecases.Modern, *uc.Style)
			},
		},
		{
			name: "maximal",
			design: adapters.Design{
				ID: uuid.NewString(), Tags: 40, Title: ptr("Test Design"), FloorTile: uuidStrPtr(),
				Toilet: uuidStrPtr(), Vanity: uuidStrPtr(), SKUCount: ptr(int32(10)), TotalPrice: ptr(int32(100000)),
			},
			expectedDiff: func(d *adapters.Design) {
				d.ColorScheme = ptr(usecases.Neutral)
				d.Style = ptr(usecases.Transitional)
				d.FloorTilePattern = ptr(defaultFloorTilePattern)
				d.WallpaperPlacement = ptr(usecases.VanityWall)
				d.WallTilePlacement = ptr(usecases.NoWallTile)
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			if tt.checkDefaults != nil {
				uc, err := tt.design.ToUsecaseDesign("PROJ-1234")
				require.NoError(t, err)
				tt.checkDefaults(t, uc)
			}
			testRoundTripConversion(t, tt.design, tt.expectedDiff)
		})
	}
}

func TestTagConversions(t *testing.T) {
	tests := []struct {
		name           string
		design         *usecases.Design
		tags           *int64
		expectedStatus *usecases.DesignStatus
		expectedColor  *usecases.ColorScheme
		expectedStyle  *usecases.Style
	}{
		{"empty", &usecases.Design{}, ptr(int64(0)), ptr(usecases.DesignStatus("")), nil, nil},
		{"fave only", &usecases.Design{Status: usecases.Fave}, ptr(int64(1073741824)), ptr(usecases.Fave), nil, nil},
		{"archived only", &usecases.Design{Status: usecases.Archived}, ptr(int64(536870912)), ptr(usecases.Archived), nil, nil},
		{"neutral only", &usecases.Design{DesignOptions: usecases.DesignOptions{ColorScheme: ptr(usecases.Neutral)}}, ptr(int64(32)), ptr(usecases.DesignStatus("")), ptr(usecases.Neutral), nil},
		{"bold only", &usecases.Design{DesignOptions: usecases.DesignOptions{ColorScheme: ptr(usecases.Bold)}}, ptr(int64(16)), ptr(usecases.DesignStatus("")), ptr(usecases.Bold), nil},
		{"modern only", &usecases.Design{DesignOptions: usecases.DesignOptions{Style: ptr(usecases.Modern)}}, ptr(int64(4)), ptr(usecases.DesignStatus("")), nil, ptr(usecases.Modern)},
		{"fave+bold+modern", &usecases.Design{Status: usecases.Fave, DesignOptions: usecases.DesignOptions{ColorScheme: ptr(usecases.Bold), Style: ptr(usecases.Modern)}}, ptr(int64(1073741824 + 16 + 4)), ptr(usecases.Fave), ptr(usecases.Bold), ptr(usecases.Modern)},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Test design to tags conversion
			if tt.design != nil && tt.tags != nil {
				apiDesign := adapters.FromUsecaseDesign(*tt.design)
				assert.Equal(t, *tt.tags, apiDesign.Tags)
			}

			// Test tags to design conversion
			if tt.tags != nil {
				apiDesign := adapters.Design{ID: uuid.NewString(), Tags: *tt.tags}
				ucDesign, err := apiDesign.ToUsecaseDesign("PROJ-1234")
				require.NoError(t, err)

				if tt.expectedStatus != nil {
					assert.Equal(t, *tt.expectedStatus, ucDesign.Status)
				}
				if tt.expectedColor != nil {
					require.NotNil(t, ucDesign.ColorScheme)
					assert.Equal(t, *tt.expectedColor, *ucDesign.ColorScheme)
				} else {
					assert.Nil(t, ucDesign.ColorScheme)
				}
				if tt.expectedStyle != nil {
					require.NotNil(t, ucDesign.Style)
					assert.Equal(t, *tt.expectedStyle, *ucDesign.Style)
				} else {
					assert.Nil(t, ucDesign.Style)
				}
			}
		})
	}
}

func TestUUIDConversions(t *testing.T) {
	testUUID := uuid.New()

	// UUID to string conversion
	uc := usecases.Design{ID: uuid.New(), ProjectID: "PROJ-1234", DesignOptions: usecases.DesignOptions{FixedProductSelections: usecases.FixedProductSelections{FloorTile: &testUUID}}}
	api := adapters.FromUsecaseDesign(uc)
	assert.Equal(t, testUUID.String(), *api.FloorTile)

	// Nil UUID
	uc.FloorTile = nil
	api = adapters.FromUsecaseDesign(uc)
	assert.Nil(t, api.FloorTile)

	// String to UUID conversion
	api = adapters.Design{ID: uuid.NewString(), FloorTile: ptr(testUUID.String())}
	uc, err := api.ToUsecaseDesign("PROJ-1234")
	require.NoError(t, err)
	assert.Equal(t, testUUID, *uc.FloorTile)

	// Nil and empty string
	for _, floorTile := range []*string{nil, ptr("")} {
		api.FloorTile = floorTile
		uc, err = api.ToUsecaseDesign("PROJ-1234")
		require.NoError(t, err)
		assert.Nil(t, uc.FloorTile)
	}

	// Invalid UUID
	api.FloorTile = ptr("invalid")
	_, err = api.ToUsecaseDesign("PROJ-1234")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid UUID for FloorTile")
}

func TestSQLNullConversions(t *testing.T) {
	// String conversions
	uc := usecases.Design{ID: uuid.New(), ProjectID: "PROJ-1234", DesignOptions: usecases.DesignOptions{Title: sql.NullString{String: "Test", Valid: true}}}
	api := adapters.FromUsecaseDesign(uc)
	assert.Equal(t, "Test", *api.Title)

	uc.Title = sql.NullString{Valid: false}
	api = adapters.FromUsecaseDesign(uc)
	assert.Nil(t, api.Title)

	// Reverse conversion
	api = adapters.Design{ID: uuid.NewString(), Title: ptr("Test")}
	uc, err := api.ToUsecaseDesign("PROJ-1234")
	require.NoError(t, err)
	assert.True(t, uc.Title.Valid)
	assert.Equal(t, "Test", uc.Title.String)

	api.Title = nil
	uc, err = api.ToUsecaseDesign("PROJ-1234")
	require.NoError(t, err)
	assert.False(t, uc.Title.Valid)

	// Int32 conversions
	uc = usecases.Design{ID: uuid.New(), ProjectID: "PROJ-1234", DesignOptions: usecases.DesignOptions{NumSKUs: sql.NullInt32{Int32: 42, Valid: true}}}
	api = adapters.FromUsecaseDesign(uc)
	assert.Equal(t, int32(42), *api.SKUCount)

	uc.NumSKUs.Valid = false
	api = adapters.FromUsecaseDesign(uc)
	assert.Nil(t, api.SKUCount)

	// Reverse conversion
	api = adapters.Design{ID: uuid.NewString(), SKUCount: ptr(int32(42))}
	uc, err = api.ToUsecaseDesign("PROJ-1234")
	require.NoError(t, err)
	assert.True(t, uc.NumSKUs.Valid)
	assert.Equal(t, int32(42), uc.NumSKUs.Int32)

	api.SKUCount = nil
	uc, err = api.ToUsecaseDesign("PROJ-1234")
	require.NoError(t, err)
	assert.False(t, uc.NumSKUs.Valid)
}

func TestEnumConversions(t *testing.T) {
	// Non-zero enum values
	uc := usecases.Design{ID: uuid.New(), ProjectID: "PROJ-1234", WallpaperPlacement: usecases.VanityWall, WallTilePlacement: usecases.HalfWall}
	api := adapters.FromUsecaseDesign(uc)
	assert.Equal(t, usecases.VanityWall, *api.WallpaperPlacement)
	assert.Equal(t, usecases.HalfWall, *api.WallTilePlacement)

	// Zero enum values
	uc.WallpaperPlacement, uc.WallTilePlacement = "", ""
	api = adapters.FromUsecaseDesign(uc)
	assert.Nil(t, api.WallpaperPlacement)
	assert.Nil(t, api.WallTilePlacement)

	// Pointer to enum conversion
	api = adapters.Design{ID: uuid.NewString(), WallpaperPlacement: ptr(usecases.VanityWall), WallTilePlacement: ptr(usecases.HalfWall)}
	uc, err := api.ToUsecaseDesign("PROJ-1234")
	require.NoError(t, err)
	assert.Equal(t, usecases.VanityWall, uc.WallpaperPlacement)
	assert.Equal(t, usecases.HalfWall, uc.WallTilePlacement)

	// Nil pointer defaults
	api = adapters.Design{ID: uuid.NewString(), WallpaperPlacement: nil, WallTilePlacement: nil}
	uc, err = api.ToUsecaseDesign("PROJ-1234")
	require.NoError(t, err)
	assert.Equal(t, usecases.VanityWall, uc.WallpaperPlacement)
	assert.Equal(t, usecases.NoWallTile, uc.WallTilePlacement)

	// Wall tile placement with wall tile set
	api.WallTile = uuidStrPtr()
	uc, err = api.ToUsecaseDesign("PROJ-1234")
	require.NoError(t, err)
	assert.Equal(t, usecases.VanityHalfWall, uc.WallTilePlacement)
}

func TestTimeConversions(t *testing.T) {
	testTime := time.Date(2025, 6, 13, 0, 59, 59, 0, time.UTC)

	// Time to string
	uc := usecases.Design{ID: uuid.New(), ProjectID: "PROJ-1234", LastUpdated: testTime}
	api := adapters.FromUsecaseDesign(uc)
	assert.Equal(t, "2025-06-13T00:59:59Z", *api.LastUpdatedDateTime)

	// Zero time
	uc.LastUpdated = time.Time{}
	api = adapters.FromUsecaseDesign(uc)
	assert.Nil(t, api.LastUpdatedDateTime)

	// String to time
	api = adapters.Design{ID: uuid.NewString(), LastUpdatedDateTime: ptr("2025-06-13T00:59:59Z")}
	uc, err := api.ToUsecaseDesign("PROJ-1234")
	require.NoError(t, err)
	assert.Equal(t, testTime, uc.LastUpdated)

	// Nil string
	api.LastUpdatedDateTime = nil
	uc, err = api.ToUsecaseDesign("PROJ-1234")
	require.NoError(t, err)
	assert.True(t, uc.LastUpdated.IsZero())

	// Invalid time
	api.LastUpdatedDateTime = ptr("invalid")
	_, err = api.ToUsecaseDesign("PROJ-1234")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid last updated date time")
}

func TestDesignIDHandling(t *testing.T) {
	// Valid UUID
	validUUID := uuid.NewString()
	api := adapters.Design{ID: validUUID}
	uc, err := api.ToUsecaseDesign("PROJ-1234")
	require.NoError(t, err)
	assert.Equal(t, validUUID, uc.ID.String())
	assert.NotEqual(t, usecases.Preview, uc.Status)

	// Invalid IDs
	for _, id := range []string{"", "A", "ABC", "invalid-uuid-string"} {
		api.ID = id
		_, err = api.ToUsecaseDesign("PROJ-1234")
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid design ID")
	}
}

func TestToUsecaseDesignDiff(t *testing.T) {
	t.Run("valid design diff conversion", func(t *testing.T) {
		validUUID := uuid.NewString()
		title := "Test Title"
		vanityWall := usecases.VanityWall

		apiDesign := adapters.Design{
			ID:                 validUUID,
			Tags:               1073741824 + 32 + 4, // Fave + Neutral + Modern
			Title:              &title,
			WallpaperPlacement: &vanityWall,
		}

		designDiff, err := apiDesign.ToUsecaseDesignDiff()
		require.NoError(t, err)

		assert.Equal(t, validUUID, designDiff.ID.String())
		require.NotNil(t, designDiff.Status)
		assert.Equal(t, usecases.Fave, *designDiff.Status)
		require.NotNil(t, designDiff.ColorScheme)
		assert.Equal(t, usecases.Neutral, *designDiff.ColorScheme)
		require.NotNil(t, designDiff.Style)
		assert.Equal(t, usecases.Modern, *designDiff.Style)
		require.NotNil(t, designDiff.WallpaperPlacement)
		assert.Equal(t, usecases.VanityWall, *designDiff.WallpaperPlacement)
		assert.True(t, designDiff.Title.Valid)
		assert.Equal(t, "Test Title", designDiff.Title.String)
	})

	t.Run("design diff with invalid UUID", func(t *testing.T) {
		apiDesign := adapters.Design{
			ID: "invalid-uuid",
		}

		_, err := apiDesign.ToUsecaseDesignDiff()
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "invalid design ID")
	})

	t.Run("design diff with no tags", func(t *testing.T) {
		validUUID := uuid.NewString()

		apiDesign := adapters.Design{
			ID:   validUUID,
			Tags: 0,
		}

		designDiff, err := apiDesign.ToUsecaseDesignDiff()
		require.NoError(t, err)

		assert.Equal(t, validUUID, designDiff.ID.String())
		assert.Nil(t, designDiff.Status)
		assert.Nil(t, designDiff.ColorScheme)
		assert.Nil(t, designDiff.Style)
	})
}

func TestBooleanConversions(t *testing.T) {
	// Boolean to pointer
	uc := usecases.Design{ID: uuid.New(), ProjectID: "PROJ-1234", TubDoorVisible: true, NichesVisible: true}
	api := adapters.FromUsecaseDesign(uc)
	assert.True(t, *api.IsTubDoorVisible)
	assert.True(t, *api.IsNichesVisible)

	// Pointer to boolean
	api = adapters.Design{ID: uuid.NewString(), IsShowerGlassVisible: ptr(true), IsTubDoorVisible: ptr(false), IsNichesVisible: ptr(true)}
	uc, err := api.ToUsecaseDesign("PROJ-1234")
	require.NoError(t, err)
	assert.True(t, uc.ShowerGlassVisible)
	assert.False(t, uc.TubDoorVisible)
	assert.True(t, uc.NichesVisible)

	// Nil pointer defaults to false
	api = adapters.Design{ID: uuid.NewString(), IsShowerGlassVisible: nil, IsTubDoorVisible: nil, IsNichesVisible: nil}
	uc, err = api.ToUsecaseDesign("PROJ-1234")
	require.NoError(t, err)
	assert.False(t, uc.ShowerGlassVisible)
	assert.False(t, uc.TubDoorVisible)
	assert.False(t, uc.NichesVisible)
}

func TestTilePatternConversions(t *testing.T) {
	patterns := []usecases.TilePattern{usecases.HorizontalStacked, usecases.VerticalStacked, usecases.HalfOffset, usecases.ThirdOffset, usecases.Herringbone}

	for _, pattern := range patterns {
		uc := usecases.Design{ID: uuid.New(), ProjectID: "PROJ-1234", DesignOptions: usecases.DesignOptions{FloorTilePattern: &pattern}}
		api := adapters.FromUsecaseDesign(uc)
		assert.Equal(t, pattern, *api.FloorTilePattern)

		// Round trip
		ucRoundTrip, err := api.ToUsecaseDesign("PROJ-1234")
		require.NoError(t, err)
		assert.Equal(t, pattern, *ucRoundTrip.FloorTilePattern)
	}

	// Nil pattern gets default
	uc := usecases.Design{ID: uuid.New(), ProjectID: "PROJ-1234", DesignOptions: usecases.DesignOptions{FloorTilePattern: nil}}
	api := adapters.FromUsecaseDesign(uc)
	assert.Nil(t, api.FloorTilePattern)

	ucRoundTrip, err := api.ToUsecaseDesign("PROJ-1234")
	require.NoError(t, err)
	assert.Equal(t, usecases.HorizontalStacked, *ucRoundTrip.FloorTilePattern)
}

func TestErrorCases(t *testing.T) {
	// Invalid UUIDs
	api := adapters.Design{ID: uuid.NewString(), FloorTile: ptr("invalid"), Toilet: ptr("also-invalid")}
	_, err := api.ToUsecaseDesign("PROJ-1234")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid UUID for")

	// Mixed valid/invalid
	api.FloorTile = uuidStrPtr()
	_, err = api.ToUsecaseDesign("PROJ-1234")
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "invalid UUID for Toilet")

	// Enum combinations round-trip
	wallpaperPlacements := []usecases.WallpaperPlacement{usecases.NoWallpaper, usecases.AllWalls, usecases.VanityWall}
	wallTilePlacements := []usecases.WallTilePlacement{usecases.NoWallTile, usecases.FullWall, usecases.HalfWall}

	for _, wp := range wallpaperPlacements {
		for _, wt := range wallTilePlacements {
			uc := usecases.Design{ID: uuid.New(), ProjectID: "PROJ-1234", WallpaperPlacement: wp, WallTilePlacement: wt}
			api := adapters.FromUsecaseDesign(uc)
			ucRoundTrip, err := api.ToUsecaseDesign("PROJ-1234")
			require.NoError(t, err)
			assert.Equal(t, wp, ucRoundTrip.WallpaperPlacement)
			assert.Equal(t, wt, ucRoundTrip.WallTilePlacement)
		}
	}
}

// Test helpers and utilities
func ptr[T any](v T) *T   { return &v }
func uuidStrPtr() *string { s := uuid.NewString(); return &s }

// Helper for round-trip conversion tests
func testRoundTripConversion(t *testing.T, design adapters.Design, expectedDiffs func(*adapters.Design)) {
	t.Helper()
	ucDesign, err := design.ToUsecaseDesign("PROJ-1234")
	require.NoError(t, err)
	roundTripped := adapters.FromUsecaseDesign(ucDesign)

	// Apply expected differences
	if expectedDiffs != nil {
		expectedDiffs(&design)
	}

	assert.Equal(t, design, roundTripped)
}
