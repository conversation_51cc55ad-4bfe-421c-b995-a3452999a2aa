package adapters

import (
	"encoding/binary"
	"encoding/json"

	"github.com/cespare/xxhash/v2"
	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

func (rl *RoomLayout) ToEntity() entities.RoomLayout {
	// Marshal the entire RoomLayout structure to get the complete raw data
	rawData, err := json.Marshal(rl)
	if err != nil {
		return entities.RoomLayout{}
	}
	roomLayoutHash := xxhash.Sum64(rawData)

	entity := entities.RoomLayout{
		Hash:    roomLayoutHash,
		RawData: rawData,
	}

	if rl.Measurements != nil && rl.Measurements.CeilingArea != nil {
		entity.AreaSqFt = *rl.Measurements.CeilingArea
	}

	// Extract floor IDs from the structured data
	floorIds := make([]uuid.UUID, 0, len(rl.Floors))
	for _, floor := range rl.Floors {
		if id, err := uuid.Parse(floor.Identifier); err == nil {
			floorIds = append(floorIds, id)
		}
	}
	entity.FloorIds = floorIds

	// Extract toilet IDs from the structured data
	toiletIds := make([]uuid.UUID, 0, len(rl.Toilets))
	for _, toilet := range rl.Toilets {
		if id, err := uuid.Parse(toilet.Identifier); err == nil {
			toiletIds = append(toiletIds, id)
		}
	}
	entity.ToiletIds = toiletIds

	// Extract wall information
	walls := make([]entities.Wall, 0, len(rl.Walls))
	for _, wall := range rl.Walls {
		if wallId, err := uuid.Parse(wall.Identifier); err == nil {
			entityWall := entities.Wall{
				LayoutId: wallId,
				NicheIds: []uuid.UUID{}, // Initialize empty, could be populated from niches with matching parentIdentifier
			}

			// Find niches that belong to this wall
			for _, niche := range rl.Niches {
				if niche.ParentIdentifier != nil && *niche.ParentIdentifier == wall.Identifier {
					if nicheId, err := uuid.Parse(niche.Identifier); err == nil {
						entityWall.NicheIds = append(entityWall.NicheIds, nicheId)
					}
				}
			}

			walls = append(walls, entityWall)
		}
	}
	entity.Walls = walls

	// Extract vanity information
	vanities := make([]entities.Vanity, 0, len(rl.Vanities))
	for _, vanity := range rl.Vanities {
		if vanityId, err := uuid.Parse(vanity.Identifier); err == nil {
			entityVanity := entities.Vanity{
				LayoutId: vanityId,
			}
			// Set MaxLength from measurements if available
			if rl.Measurements != nil && rl.Measurements.VanityLength != nil {
				entityVanity.MaxLength = rl.Measurements.VanityLength
			}
			vanities = append(vanities, entityVanity)
		}
	}
	entity.Vanities = vanities

	// Extract wet area information (combining showers, tubs, etc.)
	wetAreas := make([]entities.WetArea, 0)

	// Group shower systems by shower area
	showerSystemsByArea := make(map[string][]uuid.UUID)
	for _, system := range rl.ShowerSystems {
		if systemId, err := uuid.Parse(system.Identifier); err == nil {
			areaId := system.ShowerAreaIdentifier
			showerSystemsByArea[areaId] = append(showerSystemsByArea[areaId], systemId)
		}
	}

	// Create wet areas from shower areas
	for _, shower := range rl.Areas.Showers {
		if showerId, err := uuid.Parse(shower.Identifier); err == nil {
			systemsForArea := showerSystemsByArea[shower.Identifier]

			wetArea := entities.WetArea{
				LayoutId:           showerId,
				GlassType:          entities.NoShowerEnclosure, // Default, could be determined from other data
				AlcoveTubs:         []entities.AlcoveTub{},
				FreestandingTubIds: []uuid.UUID{},
			}
			// Set MaxTubLength from measurements if available
			if rl.Measurements != nil && rl.Measurements.TubLength != nil {
				wetArea.MaxTubLength = rl.Measurements.TubLength
			}

			// Set MaxShowerGlassLength if possible
			if rl.Measurements != nil && rl.Measurements.ShowerOpenSides != nil &&
				*rl.Measurements.ShowerOpenSides == 1 && rl.Measurements.ShowerLongestOpenSideLength != nil {
				wetArea.MaxShowerGlassLength = rl.Measurements.ShowerLongestOpenSideLength
			}

			// Check if this shower has a parent tub (alcove tub scenario)
			if shower.ParentIdentifier != nil {
				for _, tub := range rl.Tubs {
					if tub.Identifier == *shower.ParentIdentifier {
						if tubId, err := uuid.Parse(tub.Identifier); err == nil {
							alcoveTub := entities.AlcoveTub{
								LayoutId: tubId,
								DoorType: entities.NoShowerEnclosure, // Default
							}
							// Find shower system for this tub/shower combo
							if len(systemsForArea) > 0 {
								alcoveTub.ShowerId = &systemsForArea[0]
								// Remove the assigned system from the list to avoid duplication
								systemsForArea = systemsForArea[1:]
							}
							wetArea.AlcoveTubs = append(wetArea.AlcoveTubs, alcoveTub)
							break // A shower can only have one parent tub
						}
					}
				}
			}

			// Assign any remaining (i.e., non-alcove) shower systems to the main list
			wetArea.ShowerIds = systemsForArea

			wetAreas = append(wetAreas, wetArea)
		}
	}

	// Add standalone tubs as wet areas
	for _, tub := range rl.Tubs {
		// Check if this tub is not already part of a shower area
		isPartOfShower := false
		for _, shower := range rl.Areas.Showers {
			if shower.ParentIdentifier != nil && *shower.ParentIdentifier == tub.Identifier {
				isPartOfShower = true
				break
			}
		}

		if !isPartOfShower {
			if tubId, err := uuid.Parse(tub.Identifier); err == nil {
				wetArea := entities.WetArea{
					LayoutId:           tubId,
					GlassType:          entities.NoShowerEnclosure,
					ShowerIds:          []uuid.UUID{},
					AlcoveTubs:         []entities.AlcoveTub{},
					FreestandingTubIds: []uuid.UUID{tubId},
				}
				// Set MaxTubLength from measurements if available
				if rl.Measurements != nil && rl.Measurements.TubLength != nil {
					wetArea.MaxTubLength = rl.Measurements.TubLength
				}
				wetAreas = append(wetAreas, wetArea)
			}
		}
	}

	entity.WetAreas = wetAreas

	return entity
}

func FromRoomLayoutEntity(entity entities.RoomLayout) RoomLayout {
	var roomLayout RoomLayout

	// First try to unmarshal the complete structure from RawData
	if len(entity.RawData) > 0 {
		err := json.Unmarshal(entity.RawData, &roomLayout)
		if err == nil {
			return roomLayout
		}
	}

	// Fallback: create a minimal structure from entity fields
	roomLayout = RoomLayout{
		Areas:         Areas{Showers: []Shower{}},
		Ceilings:      []Ceiling{},
		Doors:         []Door{},
		Floors:        []Floor{},
		Lights:        []Light{},
		LinenCabinets: []BaseElement{},
		Mirrors:       []BaseElement{},
		Niches:        []Niche{},
		Openings:      []BaseElement{},
		RobeHooks:     []BaseElement{},
		Shelves:       []BaseElement{},
		ShowerSystems: []ShowerSystem{},
		Sinks:         []BaseElement{},
		Toilets:       []Toilet{},
		TowelBars:     []BaseElement{},
		TowelRings:    []BaseElement{},
		TpHolders:     []BaseElement{},
		TubFillers:    []BaseElement{},
		Tubs:          []Tub{},
		Vanities:      []Vanity{},
		Walls:         []Wall{},
		Windows:       []BaseElement{},
		Measurements:  &Measurements{CeilingArea: &entity.AreaSqFt},
	}

	// Populate basic structure from entity fields if RawData parsing failed
	for _, floorId := range entity.FloorIds {
		roomLayout.Floors = append(roomLayout.Floors, Floor{
			ShapedElement: ShapedElement{
				BaseElement: BaseElement{
					Identifier: floorId.String(),
				},
			},
		})
	}

	for _, toiletId := range entity.ToiletIds {
		roomLayout.Toilets = append(roomLayout.Toilets, Toilet{
			ScalableElement: ScalableElement{
				BaseElement: BaseElement{
					Identifier: toiletId.String(),
				},
			},
		})
	}

	for _, wall := range entity.Walls {
		roomLayout.Walls = append(roomLayout.Walls, Wall{
			ShapedElement: ShapedElement{
				BaseElement: BaseElement{
					Identifier: wall.LayoutId.String(),
				},
			},
		})

		// Add niches for this wall
		for _, nicheId := range wall.NicheIds {
			wallIdStr := wall.LayoutId.String()
			roomLayout.Niches = append(roomLayout.Niches, Niche{
				ScalableElement: ScalableElement{
					BaseElement: BaseElement{
						Identifier:       nicheId.String(),
						ParentIdentifier: &wallIdStr,
					},
				},
			})
		}
	}

	for _, vanity := range entity.Vanities {
		roomLayout.Vanities = append(roomLayout.Vanities, Vanity{
			ScalableElement: ScalableElement{
				BaseElement: BaseElement{
					Identifier: vanity.LayoutId.String(),
				},
			},
		})
	}

	// Convert wet areas back to showers, tubs, and shower systems
	for _, wetArea := range entity.WetAreas {
		// Add shower area
		shower := Shower{
			BaseElement: BaseElement{
				Identifier: wetArea.LayoutId.String(),
			},
		}

		// Handle alcove tubs
		for _, alcoveTub := range wetArea.AlcoveTubs {
			// Add the tub
			tub := Tub{
				ScalableElement: ScalableElement{
					BaseElement: BaseElement{
						Identifier: alcoveTub.LayoutId.String(),
					},
				},
				Type: "Alcove",
			}
			roomLayout.Tubs = append(roomLayout.Tubs, tub)

			// Set shower parent to this tub
			tubIdStr := alcoveTub.LayoutId.String()
			shower.ParentIdentifier = &tubIdStr

			// Add shower system if present
			if alcoveTub.ShowerId != nil {
				showerSystem := ShowerSystem{
					ScalableElement: ScalableElement{
						BaseElement: BaseElement{
							Identifier: alcoveTub.ShowerId.String(),
						},
					},
					ShowerAreaIdentifier: wetArea.LayoutId.String(),
					Type:                 "TubAndShowerFaucet",
				}
				roomLayout.ShowerSystems = append(roomLayout.ShowerSystems, showerSystem)
			}
		}

		// Add freestanding tubs
		for _, tubId := range wetArea.FreestandingTubIds {
			tub := Tub{
				ScalableElement: ScalableElement{
					BaseElement: BaseElement{
						Identifier: tubId.String(),
					},
				},
				Type: "Freestanding",
			}
			roomLayout.Tubs = append(roomLayout.Tubs, tub)
		}

		// Add shower systems
		for _, systemId := range wetArea.ShowerIds {
			showerSystem := ShowerSystem{
				ScalableElement: ScalableElement{
					BaseElement: BaseElement{
						Identifier: systemId.String(),
					},
				},
				ShowerAreaIdentifier: wetArea.LayoutId.String(),
			}
			roomLayout.ShowerSystems = append(roomLayout.ShowerSystems, showerSystem)
		}

		// Only add shower if it has content or is standalone
		if len(wetArea.AlcoveTubs) > 0 || len(wetArea.ShowerIds) > 0 {
			roomLayout.Areas.Showers = append(roomLayout.Areas.Showers, shower)
		}
	}

	return roomLayout
}

func Uint64ToBytes(i uint64) []byte {
	var b [8]byte
	binary.LittleEndian.PutUint64(b[:], i)
	return b[:]
}
