package controllers_test

import (
	"context"
	"log/slog"
	"testing"
	"time"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// Test presenters
type TestCreationPresenter struct {
	success   bool
	err       error
	rendition entities.Rendition
}

func (p *TestCreationPresenter) ConveySuccessWithResource(r entities.Rendition) {
	p.success = true
	p.rendition = r
}
func (p *TestCreationPresenter) PresentError(err error) { p.err = err }

type TestPresenter struct {
	success bool
	err     error
}

func (p *TestPresenter) ConveySuccess()         { p.success = true }
func (p *TestPresenter) PresentError(err error) { p.err = err }

type FakeGenAI struct{ ShouldFail bool }

func (f *FakeGenAI) GenerateDesignTitleAndDescription(ctx context.Context, productDescriptions map[string]string) (string, string, error) {
	if f.ShouldFail {
		return "", "", assert.AnError
	}
	return "Fake Title", "Fake Description", nil
}

func setup(_ *testing.T) (*controllers.RenditionWriteController, *gateways.FakeRelDb, *FakeGenAI) {
	db := gateways.NewFakeRelDb()
	genAI := &FakeGenAI{}
	controller := controllers.NewRenditionWriteController(slog.Default(),
		usecases.NewRenditionCreator(db, slog.Default()),
		usecases.NewRenditionSaver(db),
		usecases.NewRenditionUpdater(db, genAI, db, gateways.NewFakeCatalog(), slog.Default()),
		usecases.NewRenditionDeleter(db))
	return controller, db, genAI
}

func genRendition() adapters.Rendition {
	url := "https://example.com/rendition.jpg"
	return adapters.Rendition{Id: uuid.New(), CreatedAt: time.Now(), UpdatedAt: time.Now(), Status: adapters.RenditionPending, URL: &url}
}

func createDesign(t *testing.T, db *gateways.FakeRelDb) uuid.UUID {
	id := uuid.New()
	_, err := db.UpsertDesign(context.Background(), usecases.Design{ID: id, ProjectID: "TEST", Status: usecases.Preview})
	require.NoError(t, err)
	return id
}

func TestNewRenditionWriteController(t *testing.T) {
	db := gateways.NewFakeRelDb()
	genAI := &FakeGenAI{}
	catalog := gateways.NewFakeCatalog()
	logger := slog.Default()

	// Valid creation
	controller := controllers.NewRenditionWriteController(logger,
		usecases.NewRenditionCreator(db, logger),
		usecases.NewRenditionSaver(db),
		usecases.NewRenditionUpdater(db, genAI, db, catalog, logger),
		usecases.NewRenditionDeleter(db))
	assert.NotNil(t, controller)

	// Nil dependencies should panic
	maker := usecases.NewRenditionCreator(db, logger)
	saver := usecases.NewRenditionSaver(db)
	updater := usecases.NewRenditionUpdater(db, genAI, db, catalog, logger)
	deleter := usecases.NewRenditionDeleter(db)

	assert.Panics(t, func() { controllers.NewRenditionWriteController(logger, nil, saver, updater, deleter) })
	assert.Panics(t, func() { controllers.NewRenditionWriteController(logger, maker, nil, updater, deleter) })
	assert.Panics(t, func() { controllers.NewRenditionWriteController(logger, maker, saver, nil, deleter) })
	assert.Panics(t, func() { controllers.NewRenditionWriteController(logger, maker, saver, updater, nil) })

	// Nil logger should work
	controller = controllers.NewRenditionWriteController(nil, maker, saver, updater, deleter)
	assert.NotNil(t, controller)
}

func TestRenditionWriteController_AddRendition(t *testing.T) {
	ctx := context.Background()
	controller, db, _ := setup(t)
	designId := createDesign(t, db)

	// Valid rendition
	rendition := genRendition()
	presenter := &TestCreationPresenter{}
	controller.AddRendition(ctx, designId, rendition, presenter)
	assert.True(t, presenter.success)
	assert.Nil(t, presenter.err)
	assert.NotEqual(t, uuid.UUID{}, presenter.rendition.Id)
	assert.Equal(t, entities.RenditionPending, presenter.rendition.Status)

	// Without URL
	rendition.URL = nil
	presenter = &TestCreationPresenter{}
	controller.AddRendition(ctx, designId, rendition, presenter)
	assert.True(t, presenter.success)
	assert.Nil(t, presenter.rendition.URL)

	// Invalid URL
	invalidURL := "://invalid"
	rendition.URL = &invalidURL
	presenter = &TestCreationPresenter{}
	controller.AddRendition(ctx, designId, rendition, presenter)
	assert.False(t, presenter.success)
	assert.Equal(t, usecases.ErrInvalidPayload, presenter.err)

	// All statuses
	statuses := []adapters.RenditionStatus{adapters.RenditionPending, adapters.RenditionStarted, adapters.RenditionCompleted}
	for _, status := range statuses {
		rendition := genRendition()
		rendition.Status = status
		presenter := &TestCreationPresenter{}
		controller.AddRendition(ctx, designId, rendition, presenter)
		assert.True(t, presenter.success)
		expected := entities.RenditionStatus(adapters.CapitalizeFirstLetter(string(status)))
		assert.Equal(t, expected, presenter.rendition.Status)
	}
}

func TestRenditionWriteController_SaveRendition(t *testing.T) {
	ctx := context.Background()
	controller, db, _ := setup(t)
	designId := createDesign(t, db)

	// Valid save
	rendition := genRendition()
	presenter := &TestPresenter{}
	controller.SaveRendition(ctx, designId, rendition, presenter)
	assert.True(t, presenter.success)
	assert.Nil(t, presenter.err)

	// Without URL
	rendition.URL = nil
	presenter = &TestPresenter{}
	controller.SaveRendition(ctx, designId, rendition, presenter)
	assert.True(t, presenter.success)

	// Invalid URL
	invalidURL := "://invalid"
	rendition.URL = &invalidURL
	presenter = &TestPresenter{}
	controller.SaveRendition(ctx, designId, rendition, presenter)
	assert.False(t, presenter.success)
	assert.Equal(t, usecases.ErrInvalidPayload, presenter.err)
}

func TestRenditionWriteController_UpdateRendition(t *testing.T) {
	ctx := context.Background()
	controller, db, _ := setup(t)
	designId := createDesign(t, db)

	// Create rendition to update
	createPresenter := &TestCreationPresenter{}
	controller.AddRendition(ctx, designId, genRendition(), createPresenter)
	require.True(t, createPresenter.success)
	renditionId := createPresenter.rendition.Id

	// Valid update
	updateURL := "https://example.com/updated.jpg"
	update := adapters.Rendition{Id: renditionId, Status: adapters.RenditionStarted, URL: &updateURL}
	presenter := &TestPresenter{}
	controller.UpdateRendition(ctx, update, presenter)
	assert.True(t, presenter.success)

	// Invalid URL
	invalidURL := "://invalid"
	update.URL = &invalidURL
	presenter = &TestPresenter{}
	controller.UpdateRendition(ctx, update, presenter)
	assert.False(t, presenter.success)
	assert.Equal(t, usecases.ErrInvalidPayload, presenter.err)

	// Completed without URL
	update = adapters.Rendition{Id: renditionId, Status: adapters.RenditionCompleted}
	presenter = &TestPresenter{}
	controller.UpdateRendition(ctx, update, presenter)
	assert.False(t, presenter.success)
	assert.Equal(t, usecases.ErrInvalidPayload, presenter.err)

	// Zero UUID
	update = adapters.Rendition{Id: uuid.UUID{}, Status: adapters.RenditionStarted, URL: &updateURL}
	presenter = &TestPresenter{}
	controller.UpdateRendition(ctx, update, presenter)
	assert.False(t, presenter.success)
	assert.Equal(t, usecases.ErrInvalidPayload, presenter.err)
}

func TestRenditionWriteController_DeleteRendition(t *testing.T) {
	ctx := context.Background()
	controller, db, _ := setup(t)
	designId := createDesign(t, db)

	// Create rendition to delete
	createPresenter := &TestCreationPresenter{}
	controller.AddRendition(ctx, designId, genRendition(), createPresenter)
	require.True(t, createPresenter.success)
	renditionId := createPresenter.rendition.Id

	// Successful delete
	presenter := &TestPresenter{}
	controller.DeleteRendition(ctx, renditionId, presenter)
	assert.True(t, presenter.success)

	// Non-existent rendition
	presenter = &TestPresenter{}
	controller.DeleteRendition(ctx, uuid.New(), presenter)
	assert.False(t, presenter.success)
	assert.Equal(t, usecases.ErrNotFound, presenter.err)

	// Zero UUID
	presenter = &TestPresenter{}
	controller.DeleteRendition(ctx, uuid.UUID{}, presenter)
	assert.False(t, presenter.success)
	assert.Equal(t, usecases.ErrInvalidPayload, presenter.err)

	// Multiple renditions
	createPresenter1 := &TestCreationPresenter{}
	createPresenter2 := &TestCreationPresenter{}
	controller.AddRendition(ctx, designId, genRendition(), createPresenter1)
	controller.AddRendition(ctx, designId, genRendition(), createPresenter2)
	require.True(t, createPresenter1.success && createPresenter2.success)

	// Delete one, verify other remains
	presenter = &TestPresenter{}
	controller.DeleteRendition(ctx, createPresenter1.rendition.Id, presenter)
	assert.True(t, presenter.success)

	renditions, err := db.RenditionsForDesign(ctx, designId)
	require.NoError(t, err)
	assert.Len(t, renditions, 1)
	assert.Equal(t, createPresenter2.rendition.Id, renditions[0].Id)
}
