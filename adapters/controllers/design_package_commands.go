package controllers

import (
	"context"
	"log/slog"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type DesignPackageWriteController struct {
	creator *usecases.DesignPackageCreator
	logger  *slog.Logger
}

func NewDesignPackageWriteController(creator *usecases.DesignPackageCreator, logger *slog.Logger) *DesignPackageWriteController {
	if usecases.IsNil(creator) {
		panic("creator cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &DesignPackageWriteController{creator: creator, logger: logger}
}

func (c *DesignPackageWriteController) SaveDesignPackage(ctx context.Context, designPkgID uuid.UUID, designPkg adapters.DesignPackage, presenter usecases.DesignPackageCreationOutcomePresenter) {
	if usecases.IsNil(presenter) {
		panic("presenter cannot be nil")
	}

	legacyId := designPkg.ID
	if designPkg.ID != designPkgID.String() {
		designPkg.ID = designPkgID.String()
	}

	usecaseDesignPkg, err := adapters.ToUsecaseDesignPackage(designPkg)
	if err != nil {
		c.logger.ErrorContext(ctx, "Error converting design package",
			slog.String("error", err.Error()), slog.String("designPkgId", designPkgID.String()))
		presenter.PresentError(err)
		return
	}

	c.creator.CreateDesignPackage(ctx, presenter, usecaseDesignPkg, legacyId)
}
