package controllers_test

import (
	"context"
	"log/slog"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/presenters"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

func setupDesignPackage(t *testing.T) (*controllers.DesignPackageWriteController, *gateways.FakeRelDb) {
	t.Helper()
	r := gateways.NewFakeRelDb()
	logger := slog.Default()
	creator := usecases.NewDesignPackageCreator(r, logger)
	ctlr := controllers.NewDesignPackageWriteController(creator, logger)
	return ctlr, r
}

func genDesignPackage() adapters.DesignPackage {
	return adapters.DesignPackage{
		Name: "Test Design Package",
		Tagged: usecases.Tagged{
			ColorScheme: usecases.Neutral,
			Style:       usecases.Modern,
		},
		Description:     "A test design package",
		Inspiration:     "Test inspiration",
		Atmosphere:      "cozy, modern",
		ColorPalette:    "#FFFFFF, #000000",
		MaterialPalette: "wood, metal",
		Materials: adapters.Materials{
			VanityDict: map[string]uuid.UUID{
				"24": uuid.New(),
				"36": uuid.New(),
			},
			FaucetDict: map[string]uuid.UUID{
				"24": uuid.New(),
				"36": uuid.New(),
			},
		},
	}
}

func TestNewDesignPackageWriteController(t *testing.T) {
	logger := slog.Default()

	t.Run("should create controller with valid creator", func(t *testing.T) {
		r := gateways.NewFakeRelDb()
		creator := usecases.NewDesignPackageCreator(r, logger)
		controller := controllers.NewDesignPackageWriteController(creator, logger)
		assert.NotNil(t, controller)
	})

	t.Run("should panic with nil creator", func(t *testing.T) {
		assert.Panics(t, func() {
			controllers.NewDesignPackageWriteController(nil, logger)
		})
	})
}

func TestSavingNewDesignPackage(t *testing.T) {
	ctlr, store := setupDesignPackage(t)
	designPkgID := uuid.New()
	testDesignPkg := genDesignPackage()
	testDesignPkg.ID = designPkgID.String()

	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignPackageCreationOutcomePresenter(nil, recorder)

	ctlr.SaveDesignPackage(context.Background(), designPkgID, testDesignPkg, presenter)

	require.Equal(t, http.StatusCreated, recorder.Code)

	// Verify design package was saved to store
	savedDesignPkg, err := store.ReadDesignPackage(context.Background(), designPkgID)
	require.NoError(t, err)

	// Verify the saved design package has the correct properties
	assert.Equal(t, designPkgID, savedDesignPkg.ID)
	assert.Equal(t, "Test Design Package", savedDesignPkg.Name)
	assert.Equal(t, usecases.Neutral, savedDesignPkg.ColorScheme)
	assert.Equal(t, usecases.Modern, savedDesignPkg.Style)
	assert.Equal(t, "A test design package", savedDesignPkg.Description)
	assert.Equal(t, "Test inspiration", savedDesignPkg.Inspiration)
}

func TestDesignPackageIdAlignment(t *testing.T) {
	ctlr, store := setupDesignPackage(t)
	designPkgID := uuid.New()
	differentId := uuid.New()

	testDesignPkg := genDesignPackage()
	testDesignPkg.ID = differentId.String() // Design package has different ID than URL parameter

	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignPackageCreationOutcomePresenter(nil, recorder)

	ctlr.SaveDesignPackage(context.Background(), designPkgID, testDesignPkg, presenter)

	require.Equal(t, http.StatusCreated, recorder.Code)

	// Verify design package was saved with the URL designPkgID, not the design package's original ID
	savedDesignPkg, err := store.ReadDesignPackage(context.Background(), designPkgID)
	require.NoError(t, err)
	assert.Equal(t, designPkgID, savedDesignPkg.ID)

	// Verify the original design package ID is not found
	_, err = store.ReadDesignPackage(context.Background(), differentId)
	require.ErrorIs(t, err, usecases.ErrNotFound)
}

func TestDesignPackageConversionError(t *testing.T) {
	ctlr, store := setupDesignPackage(t)
	designPkgID := uuid.New()

	// Create invalid design package (mismatched vanity/faucet dict lengths)
	invalidDesignPkg := genDesignPackage()
	invalidDesignPkg.Materials.VanityDict = map[string]uuid.UUID{"24": uuid.New()}
	invalidDesignPkg.Materials.FaucetDict = map[string]uuid.UUID{"24": uuid.New(), "36": uuid.New()}

	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignPackageCreationOutcomePresenter(nil, recorder)

	ctlr.SaveDesignPackage(context.Background(), designPkgID, invalidDesignPkg, presenter)

	require.Equal(t, http.StatusBadRequest, recorder.Code)

	// Verify design package was not saved to store
	_, err := store.ReadDesignPackage(context.Background(), designPkgID)
	require.ErrorIs(t, err, usecases.ErrNotFound)
}

func TestNilPresenterPanic(t *testing.T) {
	ctlr, _ := setupDesignPackage(t)
	designPkgID := uuid.New()
	testDesignPkg := genDesignPackage()

	assert.Panics(t, func() {
		ctlr.SaveDesignPackage(context.Background(), designPkgID, testDesignPkg, nil)
	})
}
