package controllers

import (
	"context"

	"github.com/google/uuid"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type DesignPackageRetrievalController struct {
	retriever *usecases.DesignPackageRetriever
	generator *usecases.DesignGenerator
}

func NewDesignPackageRetrievalController(retriever *usecases.DesignPackageRetriever, generator *usecases.DesignGenerator) *DesignPackageRetrievalController {
	if usecases.IsNil(retriever) {
		panic("retriever cannot be nil")
	}
	if usecases.IsNil(generator) {
		panic("generator cannot be nil")
	}
	return &DesignPackageRetrievalController{retriever: retriever, generator: generator}
}

func (c *DesignPackageRetrievalController) FetchDesignPackage(ctx context.Context, designPkgID uuid.UUID, presenter usecases.DesignPackagesPresenter) {
	c.retriever.RetrieveDesignPackage(ctx, presenter, designPkgID)
}

func (c *DesignPackageRetrievalController) FetchDesignPackageByLegacyId(ctx context.Context, legacyId string, presenter usecases.DesignPackagesPresenter) {
	c.retriever.RetrieveDesignPackageByLegacyId(ctx, presenter, legacyId)
}

func (c *DesignPackageRetrievalController) FetchAllDesignPackages(ctx context.Context, presenter usecases.DesignPackagesPresenter) {
	c.retriever.RetrieveAllDesignPackages(ctx, presenter)
}

func (c *DesignPackageRetrievalController) GenerateDesignsFromDesignPackages(ctx context.Context,
	projectId entities.ProjectId, designPkgIDs []uuid.UUID, presenter usecases.DesignsPresenter) {
	c.generator.GenerateDesignsForProjectFromDesignPackages(ctx, projectId, designPkgIDs, presenter)
}
