package controllers_test

import (
	"context"
	"net/http"
	"net/http/httptest"
	"os"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/controllers"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/presenters"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// FakeMonolith is a fake implementation of the monolith interface for testing
type FakeMonolith struct {
	LayoutToReturn           entities.RoomLayout
	ShouldFail               bool
	ErrorToReturn            error
	GetLayoutForProjectCalls []entities.ProjectId
}

func (f *FakeMonolith) UpdateCurrentDesignIdForProject(ctx context.Context, projectId entities.ProjectId, designId uuid.UUID) error {
	return nil // Not needed for this test
}

func (f *FakeMonolith) GetLayoutForProject(ctx context.Context, projectId entities.ProjectId) (entities.RoomLayout, error) {
	f.GetLayoutForProjectCalls = append(f.GetLayoutForProjectCalls, projectId)
	if f.ShouldFail {
		return entities.RoomLayout{}, f.ErrorToReturn
	}
	return f.LayoutToReturn, nil
}

func NewFakeMonolith() *FakeMonolith {
	return &FakeMonolith{
		GetLayoutForProjectCalls: make([]entities.ProjectId, 0),
	}
}

func setupDesignPackageRetrieval(t *testing.T) (*controllers.DesignPackageRetrievalController, *gateways.FakeRelDb, *FakeMonolith) {
	t.Helper()
	const defaultRoomLayoutFilename = "default_room_layout.json"
	defaultRoomLayout, err := os.ReadFile(defaultRoomLayoutFilename)
	require.NoError(t, err)
	r := gateways.NewFakeRelDb()
	rooms := gateways.NewRooms(defaultRoomLayout)
	retriever := usecases.NewDesignPackageRetriever(r)
	monolith := NewFakeMonolith()
	ai := gateways.NewFakeLLM()
	productSearch := gateways.NewFakeProductSearch()
	catalog := gateways.NewFakeCatalog()
	generator := usecases.NewDesignGenerator(r, monolith, rooms, productSearch, catalog, ai, nil)
	ctlr := controllers.NewDesignPackageRetrievalController(retriever, generator)
	return ctlr, r, monolith
}

func TestNewDesignPackageRetrievalController(t *testing.T) {
	t.Run("should create controller with valid dependencies", func(t *testing.T) {
		controller, _, _ := setupDesignPackageRetrieval(t)
		assert.NotNil(t, controller)
	})

	t.Run("should panic with nil retriever", func(t *testing.T) {
		_, r, monolith := setupDesignPackageRetrieval(t)
		rooms := gateways.NewRooms([]byte(`{}`))
		ai := gateways.NewFakeLLM()
		productSearch := gateways.NewFakeProductSearch()
		catalog := gateways.NewFakeCatalog()
		generator := usecases.NewDesignGenerator(r, monolith, rooms, productSearch, catalog, ai, nil)
		assert.Panics(t, func() {
			controllers.NewDesignPackageRetrievalController(nil, generator)
		})
	})

	t.Run("should panic with nil generator", func(t *testing.T) {
		_, r, _ := setupDesignPackageRetrieval(t)
		retriever := usecases.NewDesignPackageRetriever(r)
		assert.Panics(t, func() {
			controllers.NewDesignPackageRetrievalController(retriever, nil)
		})
	})
}

func TestFetchingDesignPackage(t *testing.T) {
	ctlr, store, _ := setupDesignPackageRetrieval(t)

	// First create a design package to fetch
	designPkgID := uuid.New()
	testDesignPkg := usecases.DesignPackage{
		ID:   designPkgID,
		Name: "Test Design Package",
		Tagged: usecases.Tagged{
			ColorScheme: usecases.Neutral,
			Style:       usecases.Modern,
		},
		Description: "A test design package",
	}

	// Insert design package directly into store
	_, err := store.InsertDesignPackage(context.Background(), testDesignPkg, "")
	require.NoError(t, err)

	// Now fetch it through the controller
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignPackagesPresenter(nil, recorder, true)

	ctlr.FetchDesignPackage(context.Background(), designPkgID, presenter)

	require.Equal(t, http.StatusOK, recorder.Code)
	assert.Contains(t, recorder.Body.String(), "Test Design Package")
}

func TestFetchingAllDesignPackages(t *testing.T) {
	ctlr, store, _ := setupDesignPackageRetrieval(t)

	// Create multiple design packages
	designPkg1 := usecases.DesignPackage{
		ID:   uuid.New(),
		Name: "Design Package 1",
		Tagged: usecases.Tagged{
			ColorScheme: usecases.Neutral,
			Style:       usecases.Modern,
		},
	}
	designPkg2 := usecases.DesignPackage{
		ID:   uuid.New(),
		Name: "Design Package 2",
		Tagged: usecases.Tagged{
			ColorScheme: usecases.Bold,
			Style:       usecases.Traditional,
		},
	}

	// Insert design packages into store
	_, err := store.InsertDesignPackage(context.Background(), designPkg1, "")
	require.NoError(t, err)
	_, err = store.InsertDesignPackage(context.Background(), designPkg2, "")
	require.NoError(t, err)

	// Fetch all design packages
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignPackagesPresenter(nil, recorder, true)

	ctlr.FetchAllDesignPackages(context.Background(), presenter)

	require.Equal(t, http.StatusOK, recorder.Code)
	assert.Contains(t, recorder.Body.String(), "Design Package 1")
	assert.Contains(t, recorder.Body.String(), "Design Package 2")
}

func TestFetchingDesignPackageByLegacyId(t *testing.T) {
	ctlr, store, _ := setupDesignPackageRetrieval(t)

	// Create a design package with a legacy ID
	designPkgID := uuid.New()
	legacyId := "42"
	testDesignPkg := usecases.DesignPackage{
		ID:       designPkgID,
		LegacyId: &legacyId,
		Name:     "Legacy Design Package",
		Tagged: usecases.Tagged{
			ColorScheme: usecases.Neutral,
			Style:       usecases.Modern,
		},
		Description: "A design package with legacy ID",
	}

	// Insert design package directly into store
	_, err := store.InsertDesignPackage(context.Background(), testDesignPkg, legacyId)
	require.NoError(t, err)

	// Now fetch it by legacy ID through the controller
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignPackagesPresenter(nil, recorder, true)

	ctlr.FetchDesignPackageByLegacyId(context.Background(), legacyId, presenter)

	require.Equal(t, http.StatusOK, recorder.Code)
	assert.Contains(t, recorder.Body.String(), "Legacy Design Package")
}

func TestGenerateDesignsFromDesignPackages(t *testing.T) {
	ctlr, store, monolith := setupDesignPackageRetrieval(t)
	ctx := context.Background()
	projectId := entities.NewProjectId("PRJ-123")
	designPkgID := uuid.New()

	// Setup a designPkg in the store
	designPkg := usecases.DesignPackage{
		ID:   designPkgID,
		Name: "Test Design Package",
		Tagged: usecases.Tagged{
			ColorScheme: usecases.Neutral,
			Style:       usecases.Modern,
		},
	}
	_, err := store.InsertDesignPackage(ctx, designPkg, "")
	require.NoError(t, err)

	// Setup fake monolith to return a layout
	monolith.LayoutToReturn = entities.RoomLayout{Id: uuid.New()}

	// Setup a presenter
	recorder := httptest.NewRecorder()
	presenter := presenters.NewDesignsPresenter(nil, recorder)

	// Call the method
	ctlr.GenerateDesignsFromDesignPackages(ctx, projectId, []uuid.UUID{designPkgID}, presenter)

	// Assertions
	require.Equal(t, http.StatusOK, recorder.Code)
	assert.Contains(t, recorder.Body.String(), `"title":"Test Design Package"`)
	assert.Len(t, monolith.GetLayoutForProjectCalls, 1, "expected GetLayoutForProject to be called")
	assert.Equal(t, projectId, monolith.GetLayoutForProjectCalls[0])
}
