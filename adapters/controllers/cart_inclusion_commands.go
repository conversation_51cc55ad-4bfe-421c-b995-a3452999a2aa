package controllers

import (
	"context"
	"log/slog"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type CartInclusionWriteController struct {
	replacer *usecases.CartInclusionReplacer
	deleter  *usecases.CartInclusionDeleter
	merger   *usecases.CartInclusionMerger
	logger   *slog.Logger
}

func NewCartInclusionWriteController(
	replacer *usecases.CartInclusionReplacer,
	deleter *usecases.CartInclusionDeleter,
	merger *usecases.CartInclusionMerger,
	logger *slog.Logger) *CartInclusionWriteController {

	if usecases.IsNil(replacer) {
		panic("replacer cannot be nil")
	}
	if usecases.IsNil(deleter) {
		panic("deleter cannot be nil")
	}
	if usecases.IsNil(merger) {
		panic("merger cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &CartInclusionWriteController{
		replacer: replacer,
		deleter:  deleter,
		merger:   merger,
		logger:   logger,
	}
}

// validateRequest validates that the presenter is not nil and the design ID is valid
func (c *CartInclusionWriteController) validateRequest(ctx context.Context, designId uuid.UUID, presenter usecases.OutcomePresenter) bool {
	if usecases.IsNil(presenter) {
		panic("presenter cannot be nil")
	}

	var zeroUUID uuid.UUID
	if designId == zeroUUID || designId == uuid.Nil {
		c.logger.ErrorContext(ctx, "Invalid design ID provided",
			slog.String("designId", designId.String()))
		presenter.PresentError(usecases.ErrInvalidPayload)
		return false
	}
	return true
}

// validateRequestWithProduct validates presenter, design ID, and product ID
func (c *CartInclusionWriteController) validateRequestWithProduct(ctx context.Context, designId uuid.UUID, productId uuid.UUID, presenter usecases.OutcomePresenter) bool {
	return c.validateRequest(ctx, designId, presenter) &&
		c.validateProductID(ctx, productId, presenter)
}

// validateProductID validates that the product ID is not zero or nil
func (c *CartInclusionWriteController) validateProductID(ctx context.Context, productId uuid.UUID, presenter usecases.OutcomePresenter) bool {
	var zeroUUID uuid.UUID
	if productId == zeroUUID || productId == uuid.Nil {
		c.logger.ErrorContext(ctx, "Invalid product ID provided",
			slog.String("productId", productId.String()))
		presenter.PresentError(usecases.ErrInvalidPayload)
		return false
	}
	return true
}

// ReplaceCartInclusions performs a bulk replacement of cart inclusions for a design
func (c *CartInclusionWriteController) ReplaceCartInclusions(ctx context.Context,
	designId uuid.UUID, inclusions adapters.CartInclusions, presenter usecases.OutcomePresenter) {

	if !c.validateRequest(ctx, designId, presenter) {
		return
	}

	c.logger.DebugContext(ctx, "Replacing cart inclusions...",
		slog.String("designId", designId.String()),
		slog.Int("inclusionCount", len(inclusions)))

	c.replacer.ReplaceCartInclusions(ctx, presenter, designId, inclusions)
}

// DeleteCartInclusion removes a specific cart inclusion
func (c *CartInclusionWriteController) DeleteCartInclusion(ctx context.Context,
	designId uuid.UUID, productId uuid.UUID, location usecases.Location, presenter usecases.OutcomePresenter) {

	if !c.validateRequestWithProduct(ctx, designId, productId, presenter) {
		return
	}

	c.logger.DebugContext(ctx, "Deleting cart inclusion...",
		slog.String("designId", designId.String()),
		slog.String("productId", productId.String()),
		slog.String("location", string(location)))

	c.deleter.DeleteCartInclusion(ctx, presenter, designId, productId, location)
}

// ClearCartInclusions removes all cart inclusions for a design
func (c *CartInclusionWriteController) ClearCartInclusions(ctx context.Context,
	designId uuid.UUID, presenter usecases.OutcomePresenter) {

	if !c.validateRequest(ctx, designId, presenter) {
		return
	}

	c.logger.DebugContext(ctx, "Clearing cart inclusions...",
		slog.String("designId", designId.String()))

	c.deleter.ClearCartInclusions(ctx, presenter, designId)
}

// MergeCartInclusions handles partial updates by merging new inclusions with existing ones
func (c *CartInclusionWriteController) MergeCartInclusions(ctx context.Context,
	designId uuid.UUID, inclusions adapters.CartInclusions, presenter usecases.OutcomePresenter) {

	if !c.validateRequest(ctx, designId, presenter) {
		return
	}

	c.logger.DebugContext(ctx, "Merging cart inclusions...",
		slog.String("designId", designId.String()),
		slog.Int("inclusionCount", len(inclusions)))

	c.merger.MergeCartInclusions(ctx, presenter, designId, inclusions)
}
