package controllers

import (
	"context"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type DesignRetrievalController struct {
	retriever          *usecases.DesignRetriever
	projectIdRetriever *usecases.ProjectIdRetriever
	projectSynthesizer *usecases.ProjectSynthesizer
	generator          *usecases.DesignGenerator
}

func NewDesignRetrievalController(retriever *usecases.DesignRetriever,
	projectIdRetriever *usecases.ProjectIdRetriever, projectSynthesizer *usecases.ProjectSynthesizer,
	generator *usecases.DesignGenerator) *DesignRetrievalController {
	return &DesignRetrievalController{
		retriever:          retriever,
		projectIdRetriever: projectIdRetriever,
		projectSynthesizer: projectSynthesizer,
		generator:          generator,
	}
}

func (c *DesignRetrievalController) FetchDesign(ctx context.Context,
	designId uuid.UUID, presenter usecases.DesignsPresenter) {
	c.retriever.RetrieveDesign(ctx, presenter, designId)
}

func (h *DesignRetrievalController) FetchAllDesignsForProject(ctx context.Context,
	projectId entities.ProjectId, presenter usecases.DesignsPresenter, useLegacyIds bool) {
	h.retriever.RetrieveAllDesignsForProject(ctx, presenter, projectId, useLegacyIds)
}

func (h *DesignRetrievalController) FetchDesignsForMultipleProjects(ctx context.Context,
	projectIds []entities.ProjectId, presenter usecases.DesignsPresenter) {
	h.retriever.RetrieveDesignsForMultipleProjects(ctx, presenter, projectIds)
}

func (h *DesignRetrievalController) GetProjectIdForDesign(ctx context.Context,
	designId uuid.UUID, presenter usecases.ProjectIdPresenter) {
	h.projectIdRetriever.RetrieveProjectIdForDesign(ctx, presenter, designId)
}

func (h *DesignRetrievalController) SynthesizeProjectFromDesign(ctx context.Context,
	designId uuid.UUID, presenter usecases.ProjectPresenter) {
	h.projectSynthesizer.SynthesizeProjectFromDesign(ctx, presenter, designId)
}

func (h *DesignRetrievalController) GenerateDesignForProjectViaAI(ctx context.Context,
	projectId entities.ProjectId, userInput string, presenter usecases.EventPresenter) {
	h.generator.GenerateDesignForProjectViaAI(ctx, projectId, userInput, presenter)
}

type PresetRetrievalController struct {
	retriever *usecases.PresetRetriever
}

func NewPresetRetrievalController(retriever *usecases.PresetRetriever) *PresetRetrievalController {
	return &PresetRetrievalController{retriever: retriever}
}

func (c *PresetRetrievalController) FetchPresetByLegacyId(ctx context.Context,
	designPkgID string, presenter usecases.PresetPresenter) {
	c.retriever.FindPresetByLegacyId(ctx, presenter, designPkgID)
}
