package gateways

import (
	"context"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// FakeCatalog is a fake implementation of the catalog interface for testing
type FakeCatalog struct {
	products map[uuid.UUID]usecases.RenderableProduct
	errors   map[string]map[uuid.UUID]error
}

func NewFakeCatalog() *FakeCatalog {
	return &FakeCatalog{
		products: make(map[uuid.UUID]usecases.RenderableProduct),
		errors:   make(map[string]map[uuid.UUID]error),
	}
}

func (f *FakeCatalog) ProductInfo(ctx context.Context, category string, productId uuid.UUID) (usecases.RenderableProduct, error) {
	if categoryErrors, exists := f.errors[category]; exists {
		if err, hasError := categoryErrors[productId]; hasError {
			return usecases.ProductInfo{}, err
		}
	}

	if product, found := f.products[productId]; found {
		return product, nil
	}

	return usecases.ProductInfo{}, usecases.ErrNotFound
}
func (f *FakeCatalog) ProductDescriptionsForDesign(ctx context.Context, design usecases.Design) (map[string]string, error) {
	idToProduct := design.CategoriesByProductUUID()
	if len(idToProduct) == 0 {
		return make(map[string]string), usecases.ErrNotFound
	}

	var productIds []uuid.UUID
	for id := range idToProduct {
		productIds = append(productIds, id)
	}

	productDescriptions, err := f.ProductDescriptions(ctx, productIds)
	if err != nil {
		return nil, err
	}

	result := make(map[string]string)
	for id, description := range productDescriptions {
		category, ok := idToProduct[id]
		if !ok {
			continue
		}
		result[category] = description
	}
	return result, nil
}
func (f *FakeCatalog) ProductDescriptions(ctx context.Context, productUUIDs []uuid.UUID) (map[uuid.UUID]string, error) {
	results := make(map[uuid.UUID]string)
	if len(productUUIDs) == 0 {
		return results, nil
	}

	for _, id := range productUUIDs {
		if product, exists := f.products[id]; exists {
			results[id] = product.GetDescription()
		}
	}

	return results, nil
}

func (f *FakeCatalog) AddProduct(category string, productId uuid.UUID, product usecases.RenderableProduct) {
	f.products[productId] = product
}

func (f *FakeCatalog) AddError(category string, productId uuid.UUID, err error) {
	if f.errors[category] == nil {
		f.errors[category] = make(map[uuid.UUID]error)
	}
	f.errors[category][productId] = err
}
