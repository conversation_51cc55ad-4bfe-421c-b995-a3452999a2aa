package gateways

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"log/slog"
	"net/url"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

func (r *RelationalDb) InsertRendition(ctx context.Context, designId uuid.UUID, rendition entities.Rendition) (uuid.UUID, error) {
	var zeroUUID uuid.UUID
	if rendition.Id == uuid.Nil || rendition.Id == zeroUUID {
		rendition.Id = uuid.New()
		r.logger.InfoContext(ctx, "Generated new ID for rendition", slog.String("renditionID", rendition.Id.String()))
	}

	if (rendition.URL == nil || rendition.URL.String() == "") && rendition.Status == entities.RenditionCompleted {
		r.logger.ErrorContext(ctx, "Rendition URL cannot be empty for completed renditions",
			slog.String("renditionID", rendition.Id.String()), slog.String("designID", designId.String()))
		return zeroUUID, usecases.ErrInvalidPayload
	}
	rendition.Status = entities.RenditionStatus(capitalizeFirstLetter(rendition.Status))
	r.logger.InfoContext(ctx, "Inserting rendition", slog.String("designID", designId.String()),
		slog.String("renditionID", rendition.Id.String()), slog.String("status", string(rendition.Status)))

	tx, err := r.db.Begin(ctx)
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to begin transaction", slog.String("error", err.Error()))
		return zeroUUID, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer func() {
		if err != nil {
			if err := tx.Rollback(ctx); err != nil {
				r.logger.ErrorContext(ctx, "Failed to rollback transaction", slog.String("error", err.Error()))
			}
		}
	}()

	const renditionQuery = `
		INSERT INTO public.renditions (id, room_design_id, status, url)
		VALUES (@id, @room_design_id, @status, @url)
		ON CONFLICT (id) DO UPDATE SET
			room_design_id = EXCLUDED.room_design_id,
			status = EXCLUDED.status,
			url = EXCLUDED.url
		WHERE public.renditions.updated_at < EXCLUDED.updated_at`
	_, err = tx.Exec(ctx, renditionQuery, pgx.NamedArgs{
		"id":             rendition.Id,
		"room_design_id": designId,
		"status":         rendition.Status,
		"url":            rendition.URL,
	})
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to insert into renditions", slog.String("error", err.Error()),
			slog.String("renditionID", rendition.Id.String()), slog.String("designID", designId.String()))
		return zeroUUID, fmt.Errorf("failed to insert %s into renditions for design %s: %w",
			rendition.Id, designId, err)
	}

	if err = tx.Commit(ctx); err != nil {
		r.logger.ErrorContext(ctx, "Failed to commit transaction", slog.String("error", err.Error()),
			slog.String("renditionID", rendition.Id.String()), slog.String("designID", designId.String()))
		return zeroUUID, fmt.Errorf("failed to commit transaction: %w", err)
	}

	return rendition.Id, nil
}

func (r *RelationalDb) Renditions(ctx context.Context, ids []uuid.UUID) ([]entities.Rendition, error) {
	query := `
		SELECT r.id, r.created_at, r.updated_at, r.status, url, r.room_design_id,
			title, description, sku_count, total_price_cents
		FROM public.renditions r
			LEFT JOIN design.room_designs rd ON r.room_design_id = rd.id
			LEFT JOIN design.retail_info ri ON rd.id = ri.room_design_id
		WHERE r.id = ANY($1)`

	r.logger.DebugContext(ctx, "Looking up renditions by ID...",
		slog.Int("count", len(ids)), slog.String("ids", fmt.Sprintf("%v", ids)))
	rows, err := r.db.Query(ctx, query, ids)
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to query renditions by id",
			slog.String("error", err.Error()), slog.String("ids", fmt.Sprintf("%v", ids)))
		return nil, usecases.ErrNotFound
	}
	defer rows.Close()
	return r.scanRenditions(rows)
}

func (r *RelationalDb) PanoramicImagesForDesignPackages(ctx context.Context, designPkgIDs []uuid.UUID) (map[uuid.UUID]url.URL, error) {
	r.logger.DebugContext(ctx, "Looking up prerendered panoranic imagery by design package Ids...",
		slog.Int("count", len(designPkgIDs)), slog.String("ids", fmt.Sprintf("%v", designPkgIDs)))
	designPkgPanoImagesURLs := make(map[uuid.UUID]url.URL)
	query := `
		SELECT ll.design_pkg_id, r.url
		FROM public.renditions r JOIN public.legacy_lookup ll ON r.id = ll.rendition_id
		WHERE ll.design_pkg_id = ANY($1) AND r.url IS NOT NULL`
	rows, err := r.db.Query(ctx, query, designPkgIDs)
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to query panoramic images by design_pkg_id",
			slog.String("error", err.Error()), slog.String("ids", fmt.Sprintf("%v", designPkgIDs)))
		return designPkgPanoImagesURLs, usecases.ErrNotFound
	}
	defer rows.Close()
	for rows.Next() {
		var designPkgID uuid.UUID
		// TODO: try using a plain string.
		var urlString sql.NullString
		err := rows.Scan(&designPkgID, &urlString)
		if err != nil {
			r.logger.ErrorContext(context.Background(), "unable to parse design package UUID or image URL",
				slog.String("error", err.Error()))
			continue
		}
		if urlString.Valid && urlString.String != "" {
			url, err := url.Parse(urlString.String)
			if err != nil {
				r.logger.ErrorContext(context.Background(), "failed to parse pano image URL for design package",
					slog.String("error", err.Error()), slog.String("url", urlString.String), slog.String("designPkgId", designPkgID.String()))
				continue
			}
			designPkgPanoImagesURLs[designPkgID] = *url
		}
	}
	if err := rows.Err(); err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			r.logger.InfoContext(context.Background(), "No panoramic images found")
			return designPkgPanoImagesURLs, usecases.ErrNotFound
		} else {
			r.logger.ErrorContext(context.Background(), "error while iterating over rows",
				slog.String("error", err.Error()))
			return designPkgPanoImagesURLs, fmt.Errorf("error while iterating over rows: %w", err)
		}
	}
	return designPkgPanoImagesURLs, nil
}

// RenditionsForDesign retrieves all renditions associated with a given design ID.
func (r *RelationalDb) RenditionsForDesign(ctx context.Context, designId uuid.UUID) ([]entities.Rendition, error) {
	query := `
		SELECT r.id, r.created_at, r.updated_at, r.status, url, r.room_design_id,
			title, description, sku_count, total_price_cents
		FROM public.renditions r
			LEFT JOIN design.room_designs rd ON r.room_design_id = rd.id
			LEFT JOIN design.retail_info ri ON rd.id = ri.room_design_id
		WHERE r.room_design_id = $1
		ORDER BY updated_at DESC`

	r.logger.DebugContext(ctx, "Looking up renditions for design", "designID", designId)
	rows, err := r.db.Query(ctx, query, designId)
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to query renditions by design id",
			slog.String("error", err.Error()), slog.String("designID", designId.String()))
		if errors.Is(err, pgx.ErrNoRows) {
			return nil, usecases.ErrNotFound
		}
		return nil, fmt.Errorf("failed to query renditions by design id: %w", err)
	}
	defer rows.Close()
	return r.scanRenditions(rows)
}

func (r *RelationalDb) scanRenditions(rows pgx.Rows) ([]entities.Rendition, error) {
	var renditions []entities.Rendition
	for rows.Next() {
		var rendition entities.Rendition
		var urlString sql.NullString
		var totalPriceCents sql.NullInt32
		err := rows.Scan(
			&rendition.Id,
			&rendition.CreatedAt,
			&rendition.UpdatedAt,
			&rendition.Status,
			&urlString,
			&rendition.DesignId,
			&rendition.Title,
			&rendition.Description,
			&rendition.SkuCount,
			&totalPriceCents,
		)
		if err != nil {
			r.logger.ErrorContext(context.Background(), "failed to scan rendition row",
				slog.String("error", err.Error()))
			continue
		}
		if totalPriceCents.Valid {
			// TODO: if we are just surfacing cents anyay then this can be simplified.
			rendition.TotalPrice = sql.NullInt32{Int32: totalPriceCents.Int32, Valid: true}
		}

		// Parse URL if it exists
		if urlString.Valid && urlString.String != "" {
			if rendition.URL, err = url.Parse(urlString.String); err != nil {
				r.logger.ErrorContext(context.Background(), "failed to parse rendition URL",
					slog.String("error", err.Error()), slog.String("url", urlString.String))
				continue
			}
		}
		renditions = append(renditions, rendition)
	}

	if err := rows.Err(); err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			r.logger.InfoContext(context.Background(), "No renditions found")
			return renditions, usecases.ErrNotFound
		} else {
			r.logger.ErrorContext(context.Background(), "error during rows iteration",
				slog.String("error", err.Error()))
			return nil, fmt.Errorf("error during rendition rows iteration: %w", err)
		}
	}
	return renditions, nil
}

func (r *RelationalDb) MarkAllCompletedRenditionsOutdatedForProject(ctx context.Context, projectId entities.ProjectId) error {
	_, err := r.db.Exec(ctx, `UPDATE public.renditions SET status = 'Outdated'
		WHERE room_design_id IN (SELECT id FROM design.room_designs WHERE project_id = $1)
			AND status = 'Completed'`, projectId)
	if err != nil {
		return fmt.Errorf("failed to mark renditions as outdated for project %s: %w", projectId, err)
	}
	return nil
}

// UpdateRendition updates the status and URL of an existing rendition in the database.
// The updated_at timestamp is automatically updated by the database trigger.
func (r *RelationalDb) UpdateRendition(ctx context.Context, rendition entities.RenditionDiff) error {
	if rendition.Status == "" {
		r.logger.ErrorContext(ctx, "Rendition status was empty", slog.String("renditionId", rendition.Id.String()))
		return usecases.ErrInvalidPayload
	}
	rendition.Status = entities.RenditionStatus(capitalizeFirstLetter(rendition.Status))
	updateQuery := `UPDATE public.renditions SET status = @status`
	args := pgx.NamedArgs{
		"id":     rendition.Id,
		"status": rendition.Status,
	}
	if rendition.Status == entities.RenditionCompleted {
		if rendition.URL == nil || rendition.URL.String() == "" {
			r.logger.ErrorContext(ctx, "Rendition URL was empty with Completed status",
				slog.String("renditionId", rendition.Id.String()))
			return usecases.ErrInvalidPayload
		}
		updateQuery += ", url = @url"
		args["url"] = rendition.URL.String()
	}
	updateQuery += " WHERE id = @id"

	result, err := r.db.Exec(ctx, updateQuery, args)
	if err != nil {
		return fmt.Errorf("failed to update rendition: %w", err)
	}
	rowsAffected := result.RowsAffected()
	if rowsAffected == 0 {
		r.logger.ErrorContext(ctx, "No rows were updated in DB when trying to update rendition",
			slog.String("renditionId", rendition.Id.String()))
		return usecases.ErrNotFound
	}
	return nil
}

func (r *RelationalDb) DeleteRendition(ctx context.Context, id uuid.UUID) error {
	query := `DELETE FROM public.renditions WHERE id = $1`
	cmdTag, err := r.db.Exec(ctx, query, id)
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to execute delete command",
			slog.String("error", err.Error()), slog.String("renditionId", id.String()))
		return fmt.Errorf("failed to execute delete command: %w", err)
	}
	if cmdTag.RowsAffected() == 0 {
		r.logger.ErrorContext(ctx, "No rows were updated in DB when trying to delete rendition",
			slog.String("renditionId", id.String()))
		return usecases.ErrNotFound
	}
	return nil
}

func (r *RelationalDb) MarkRenditionsOutdatedForDesign(ctx context.Context, designId uuid.UUID) error {
	result, err := r.db.Exec(ctx, `UPDATE public.renditions SET status = 'Outdated' WHERE room_design_id = $1`, designId)
	if err != nil {
		return fmt.Errorf("failed to mark renditions as outdated for design %s: %w", designId, err)
	} else {
		rowsAffected := result.RowsAffected()
		r.logger.DebugContext(ctx, "Marked renditions as outdated for design",
			slog.String("designID", designId.String()), slog.Int("count", int(rowsAffected)))
		if rowsAffected == 0 {
			r.logger.ErrorContext(ctx, "No renditions found to update for design", slog.String("designID", designId.String()))
		}
	}
	return nil
}
