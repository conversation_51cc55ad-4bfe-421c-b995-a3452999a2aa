package gateways

// TODO: investigate splitting this type up.

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"log/slog"
	"net/url"
	"time"
	"unicode"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"
	"github.com/jackc/pgx/v5/pgxpool"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// Provides access to the Postgres DB used by the new Design service.
// The current schema of this DB is defined in `frameworks/db/schema.sql`.
type RelationalDb struct {
	db     *pgxpool.Pool
	logger *slog.Logger
}

func NewRelationalDb(db *pgxpool.Pool, logger *slog.Logger) *RelationalDb {
	if db == nil {
		panic("db cannot be nil")
	}
	if logger == nil {
		logger = slog.Default()
	}
	return &RelationalDb{db: db, logger: logger}
}

func (r *RelationalDb) Close() {
	r.db.Close()
}

// TODO: nothing seems to use this so maybe we can delete it.
func (r *RelationalDb) IdsOfProjectsWithDesigns(ctx context.Context) ([]entities.ProjectId, error) {
	projectIds := make([]entities.ProjectId, 0)
	rows, err := r.db.Query(ctx, "SELECT DISTINCT project_id FROM design.room_designs")
	if err != nil {
		return nil, fmt.Errorf("query failed: %w", err)
	}
	defer rows.Close()
	for rows.Next() {
		var projectId entities.ProjectId
		if err := rows.Scan(&projectId); err != nil {
			return nil, fmt.Errorf("scan failed: %w", err)
		}
		projectIds = append(projectIds, projectId)
	}
	return projectIds, nil
}

func (r *RelationalDb) InsertPreset(ctx context.Context, preset usecases.Preset) error {
	if preset.Id == "" {
		return fmt.Errorf("preset ID cannot be empty")
	}

	tx, err := r.db.Begin(ctx)
	if err != nil {
		return fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer func() {
		if err != nil {
			if err := tx.Rollback(ctx); err != nil {
				r.logger.ErrorContext(ctx, "Failed to rollback transaction", slog.String("error", err.Error()))
			}
		}
	}()

	// 1. Insert the design data (room_designs, default_products, render_prefs, retail_info)
	var zeroUUID uuid.UUID
	design := preset.Design
	if design.ID == zeroUUID {
		design.ID = uuid.New()
		r.logger.InfoContext(ctx, "Generated new design ID for preset", slog.String("designID", design.ID.String()))
	}

	roomDesignQuery := `
		INSERT INTO design.room_designs (
			id, project_id, status, style, color_scheme, title, description
		) VALUES (
			@id, @project_id, @status, @style, @color_scheme, @title, @description
		)`
	_, err = tx.Exec(ctx, roomDesignQuery, pgx.NamedArgs{
		"id":           design.ID,
		"project_id":   design.ProjectID,
		"status":       design.Status,
		"style":        design.Style,
		"color_scheme": design.ColorScheme,
		"title":        design.Title,
		"description":  design.Description,
	})
	if err != nil {
		return fmt.Errorf("failed to insert into room_designs: %w", err)
	}

	productsQuery := `
		INSERT INTO design.default_products (
			room_design_id, floor_tile, floor_tile_pattern, toilet, vanity, faucet, mirror,
			lighting, paint, shelving, wall_tile_placement, wall_tile, wall_tile_pattern,
			wallpaper_placement, wallpaper, shower_system, shower_floor_tile,
			shower_floor_tile_pattern, shower_wall_tile, shower_wall_tile_pattern,
			shower_short_wall_tile, shower_glass, niche_tile, tub, tub_filler, tub_door
		) VALUES (
			@room_design_id, @floor_tile, @floor_tile_pattern, @toilet, @vanity, @faucet, @mirror,
			@lighting, @paint, @shelving, @wall_tile_placement, @wall_tile, @wall_tile_pattern,
			@wallpaper_placement, @wallpaper, @shower_system, @shower_floor_tile,
			@shower_floor_tile_pattern, @shower_wall_tile, @shower_wall_tile_pattern,
			@shower_short_wall_tile, @shower_glass, @niche_tile, @tub, @tub_filler, @tub_door
		)`
	_, err = tx.Exec(ctx, productsQuery, pgx.NamedArgs{
		"room_design_id":            design.ID,
		"floor_tile":                design.FloorTile,
		"floor_tile_pattern":        design.FloorTilePattern,
		"toilet":                    design.Toilet,
		"vanity":                    design.Vanity,
		"faucet":                    design.Faucet,
		"mirror":                    design.Mirror,
		"lighting":                  design.Lighting,
		"paint":                     design.Paint,
		"shelving":                  design.Shelving,
		"wall_tile_placement":       design.WallTilePlacement,
		"wall_tile":                 design.WallTile,
		"wall_tile_pattern":         design.WallTilePattern,
		"wallpaper_placement":       design.WallpaperPlacement,
		"wallpaper":                 design.Wallpaper,
		"shower_system":             design.ShowerSystem,
		"shower_floor_tile":         design.ShowerFloorTile,
		"shower_floor_tile_pattern": design.ShowerFloorTilePattern,
		"shower_wall_tile":          design.ShowerWallTile,
		"shower_wall_tile_pattern":  design.ShowerWallTilePattern,
		"shower_short_wall_tile":    design.ShowerShortWallTile,
		"shower_glass":              design.ShowerGlass,
		"niche_tile":                design.NicheTile,
		"tub":                       design.Tub,
		"tub_filler":                design.TubFiller,
		"tub_door":                  design.TubDoor,
	})
	if err != nil {
		return fmt.Errorf("failed to insert into default_products: %w", err)
	}

	prefsQuery := `
		INSERT INTO design.render_prefs (
			room_design_id, shower_glass_visible, tub_door_visible, niches_visible
		) VALUES (
			@room_design_id, @shower_glass_visible, @tub_door_visible, @niches_visible
		)`
	_, err = tx.Exec(ctx, prefsQuery, pgx.NamedArgs{
		"room_design_id":       design.ID,
		"shower_glass_visible": design.ShowerGlassVisible,
		"tub_door_visible":     design.TubDoorVisible,
		"niches_visible":       design.NichesVisible,
	})
	if err != nil {
		return fmt.Errorf("failed to insert into render_prefs: %w", err)
	}

	retailInfoQuery := `
		INSERT INTO design.retail_info (
			room_design_id, total_price_cents, lead_time_days, sku_count
		)
		VALUES (@room_design_id, @total_price_cents, @lead_time_days, @sku_count)`
	_, err = tx.Exec(ctx, retailInfoQuery, pgx.NamedArgs{
		"room_design_id":    design.ID,
		"total_price_cents": design.TotalPriceInCents,
		"lead_time_days":    design.LeadTimeDays,
		"sku_count":         design.NumSKUs,
	})
	if err != nil {
		r.logger.Error("Failed to upsert into retail_info", "designID", design.ID, "err", err.Error(),
			"price", design.TotalPriceInCents, "leadTime", design.LeadTimeDays, "skuCount", design.NumSKUs)
		return fmt.Errorf("failed to upsert into retail_info: %w", err)
	}

	// 2. Insert the rendition
	rendition := preset.Rendition
	if rendition.Id == uuid.Nil || rendition.Id == zeroUUID {
		rendition.Id = uuid.New()
		r.logger.InfoContext(ctx, "Generated new rendition ID for preset", slog.String("renditionID", rendition.Id.String()))
	}
	renditionURL := rendition.URL.String()
	if renditionURL == "" && rendition.Status == entities.RenditionCompleted {
		return fmt.Errorf("rendition URL cannot be empty for completed renditions")
	}
	r.logger.Info("Inserting rendition", "designID", design.ID, "renditionID", rendition.Id,
		"status", rendition.Status, "url", renditionURL)

	const renditionQuery = `
		INSERT INTO public.renditions (id, room_design_id, status, url)
		VALUES (@id, @room_design_id, @status, @url)
		ON CONFLICT (id) DO UPDATE SET
			room_design_id = EXCLUDED.room_design_id,
			status = EXCLUDED.status,
			url = EXCLUDED.url
		WHERE public.renditions.updated_at < EXCLUDED.updated_at`

	_, err = tx.Exec(ctx, renditionQuery, pgx.NamedArgs{
		"id":             rendition.Id,
		"room_design_id": design.ID,
		"status":         capitalizeFirstLetter(rendition.Status),
		"url":            renditionURL,
	})
	if err != nil {
		return fmt.Errorf("failed to insert %s into renditions: %w", rendition.Id, err)
	}

	// 3. Update legacy_lookup table to establish the mapping
	legacyLookupQuery := `
		UPDATE public.legacy_lookup SET
		rendition_id = @rendition_id
		WHERE id = @id`
	_, err = tx.Exec(ctx, legacyLookupQuery, pgx.NamedArgs{
		"id":           preset.Id,
		"rendition_id": rendition.Id,
	})
	if err != nil {
		return fmt.Errorf("failed to update legacy_lookup: %w", err)
	}

	if err = tx.Commit(ctx); err != nil {
		return fmt.Errorf("failed to commit transaction: %w", err)
	}
	return nil
}

func capitalizeFirstLetter(rs entities.RenditionStatus) string {
	runes := []rune(rs)
	runes[0] = unicode.ToTitle(runes[0])
	return string(runes)
}

func (r *RelationalDb) LegacyIdMapping(ctx context.Context) (map[uuid.UUID]string, error) {
	const query = `SELECT legacy_lookup.id, room_design_id
	FROM public.legacy_lookup JOIN public.renditions ON legacy_lookup.rendition_id = renditions.id`
	rows, err := r.db.Query(ctx, query)
	if err != nil {
		r.logger.ErrorContext(ctx, "failed to query legacy_lookup", "err", err.Error())
		return nil, fmt.Errorf("failed to query legacy_lookup: %w", err)
	}
	defer rows.Close()

	mapping := make(map[uuid.UUID]string)
	for rows.Next() {
		var legacyId string
		var designId uuid.UUID
		err := rows.Scan(&legacyId, &designId)
		if err != nil {
			r.logger.ErrorContext(ctx, "failed to scan legacy_lookup row", "err", err.Error())
			continue
		}
		mapping[designId] = legacyId
	}

	if len(mapping) == 0 {
		r.logger.ErrorContext(ctx, "No legacy IDs found")
		return nil, usecases.ErrNotFound
	}
	return mapping, nil
}

func (r *RelationalDb) FindPresetByLegacyId(ctx context.Context, designPkgID string) (usecases.Preset, error) {
	// Query to join legacy_lookup -> renditions -> room_designs and get all design data
	query := fmt.Sprintf(`
		SELECT
			ll.id as legacy_id,
			ll.design_pkg_id,
			r.id as rendition_id, r.created_at as rendition_created_at, r.updated_at as rendition_updated_at,
			r.status as rendition_status, r.url as rendition_url,
			%s
		FROM public.legacy_lookup ll
		INNER JOIN public.renditions r ON ll.rendition_id = r.id
		INNER JOIN design.room_designs rd ON r.room_design_id = rd.id
		INNER JOIN design.default_products dp ON rd.id = dp.room_design_id
		INNER JOIN design.render_prefs rp ON rd.id = rp.room_design_id
		LEFT JOIN design.retail_info ri ON rd.id = ri.room_design_id
		WHERE ll.id = $1`,
		designFields)

	row := r.db.QueryRow(ctx, query, designPkgID)

	// Scan the legacy lookup and rendition data first
	var legacyId string
	var designPkgUUID uuid.UUID
	var renditionId uuid.UUID
	var renditionCreatedAt, renditionUpdatedAt time.Time
	var renditionStatus string
	var renditionURL sql.NullString

	// Then scan the design data using the same pattern as scanDesign
	var rd RoomDesignModel
	var dp DefaultProductsModel
	var rp RenderPrefsModel
	var ri RetailInfoModel

	err := row.Scan(
		&legacyId, &designPkgUUID,
		&renditionId, &renditionCreatedAt, &renditionUpdatedAt, &renditionStatus, &renditionURL,
		// Design fields (same order as in scanDesign)
		&rd.ID, &rd.ProjectID, &rd.CreatedAt, &rd.UpdatedAt,
		&rd.Status, &rd.Style, &rd.ColorScheme, &rd.Title, &rd.Description,
		&dp.FloorTile, &dp.FloorTilePattern, &dp.Toilet, &dp.Vanity, &dp.Faucet,
		&dp.Mirror, &dp.Lighting, &dp.Paint, &dp.Shelving,
		&dp.WallTilePlacement, &dp.WallTile, &dp.WallTilePattern,
		&dp.WallpaperPlacement, &dp.Wallpaper,
		&dp.ShowerSystem, &dp.ShowerFloorTile, &dp.ShowerFloorTilePattern,
		&dp.ShowerWallTile, &dp.ShowerWallTilePattern, &dp.ShowerShortWallTile,
		&dp.ShowerGlass, &dp.NicheTile, &dp.Tub, &dp.TubFiller, &dp.TubDoor,
		&rp.ShowerGlassVisible, &rp.TubDoorVisible, &rp.NichesVisible,
		&ri.TotalPriceCents, &ri.LeadTimeDays, &ri.SkuCount,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return usecases.Preset{}, fmt.Errorf("no preset found with legacy ID %s", designPkgID)
		}
		return usecases.Preset{}, fmt.Errorf("failed to scan preset row: %w", err)
	}

	design := newDesignFromModels(&rd, &dp, &rp, &ri)

	// Construct the Rendition
	var renditionURLParsed *url.URL
	if renditionURL.Valid && renditionURL.String != "" {
		if renditionURLParsed, err = url.Parse(renditionURL.String); err != nil {
			return usecases.Preset{}, fmt.Errorf("failed to parse rendition URL: %w", err)
		}
	}

	rendition := entities.Rendition{
		Id:        renditionId,
		CreatedAt: renditionCreatedAt,
		UpdatedAt: renditionUpdatedAt,
		Status:    entities.RenditionStatus(renditionStatus),
		URL:       renditionURLParsed,
	}

	preset := usecases.Preset{
		Id:          legacyId,
		DesignPkgId: designPkgUUID,
		Design:      design,
		Rendition:   rendition,
		RoomLayout:  entities.RoomLayout{},
	}

	return preset, nil
}
