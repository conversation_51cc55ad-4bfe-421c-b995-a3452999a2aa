//go:build integration

package gateways_test

import (
	"context"
	"database/sql"
	"net/url"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// Test helpers and utilities
type testSetup struct {
	ctx context.Context
	db  *gateways.RelationalDb
}

func setupRenditionTest(t *testing.T) *testSetup {
	t.Helper()
	ctx := context.Background()
	db := gateways.NewRelationalDb(testPool, nil)

	// Clean database
	_, err := testPool.Exec(ctx, "TRUNCATE TABLE design.room_designs CASCADE;")
	require.NoError(t, err, "Failed to truncate tables before test")

	return &testSetup{ctx: ctx, db: db}
}

func createTestDesignForRendition(t *testing.T, setup *testSetup, projectID entities.ProjectId, title string) uuid.UUID {
	t.Helper()
	design := usecases.Design{
		ProjectID:          projectID,
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.NoWallpaper,
		WallTilePlacement:  usecases.HalfWall,
		DesignOptions: usecases.DesignOptions{
			Title:       sql.NullString{String: title, Valid: true},
			Description: sql.NullString{String: "Test design for " + title, Valid: true},
			FixedProductSelections: usecases.FixedProductSelections{
				FloorTile: ptr(uuid.New()),
				Toilet:    ptr(uuid.New()),
			},
		},
	}

	designID, err := setup.db.UpsertDesign(setup.ctx, design)
	require.NoError(t, err, "Failed to create test design")
	return designID
}

func createTestRendition(status entities.RenditionStatus, urlPath string) entities.Rendition {
	testURL, _ := url.Parse("https://example.com" + urlPath)
	return entities.Rendition{
		Id:     uuid.New(),
		Status: status,
		URL:    testURL,
	}
}

func assertRenditionStatus(t *testing.T, setup *testSetup, renditionID uuid.UUID, expectedStatus entities.RenditionStatus) {
	t.Helper()
	renditions, err := setup.db.Renditions(setup.ctx, []uuid.UUID{renditionID})
	require.NoError(t, err)
	require.Len(t, renditions, 1)
	assert.Equal(t, expectedStatus, renditions[0].Status)
}

func TestRelationalDb_Integration_Renditions_CRUD(t *testing.T) {
	t.Run("create and read rendition", func(t *testing.T) {
		setup := setupRenditionTest(t)
		designID := createTestDesignForRendition(t, setup, "PRJ-RENDITION-TEST", "CRUD Test Design")
		testRendition := createTestRendition(entities.RenditionPending, "/test-image.webp")

		createdID, err := setup.db.InsertRendition(setup.ctx, designID, testRendition)
		require.NoError(t, err)
		assert.Equal(t, testRendition.Id, createdID)

		// Test read by ID
		renditions, err := setup.db.Renditions(setup.ctx, []uuid.UUID{createdID})
		require.NoError(t, err)
		require.Len(t, renditions, 1)

		rendition := renditions[0]
		assert.Equal(t, createdID, rendition.Id)
		assert.Equal(t, testRendition.Status, rendition.Status)
		assert.NotZero(t, rendition.CreatedAt)
		assert.Equal(t, rendition.CreatedAt, rendition.UpdatedAt)

		// Test read by design ID
		designRenditions, err := setup.db.RenditionsForDesign(setup.ctx, designID)
		require.NoError(t, err)
		require.Len(t, designRenditions, 1)
		assert.Equal(t, rendition, designRenditions[0])
	})

	t.Run("update rendition", func(t *testing.T) {
		setup := setupRenditionTest(t)
		designID := createTestDesignForRendition(t, setup, "PRJ-UPDATE-TEST", "Update Test Design")
		testRendition := createTestRendition(entities.RenditionPending, "/update-test.webp")
		renditionID, err := setup.db.InsertRendition(setup.ctx, designID, testRendition)
		require.NoError(t, err)

		// Update to completed with new URL
		newURL, _ := url.Parse("https://example.com/updated-image.webp")
		updateDiff := entities.RenditionDiff{
			Id:     renditionID,
			Status: entities.RenditionCompleted,
			URL:    newURL,
		}

		err = setup.db.UpdateRendition(setup.ctx, updateDiff)
		require.NoError(t, err)

		// Verify update
		renditions, err := setup.db.Renditions(setup.ctx, []uuid.UUID{renditionID})
		require.NoError(t, err)
		require.Len(t, renditions, 1)

		updated := renditions[0]
		assert.Equal(t, entities.RenditionCompleted, updated.Status)
		assert.Equal(t, newURL.String(), updated.URL.String())
		assert.Greater(t, updated.UpdatedAt, updated.CreatedAt)
	})

	t.Run("multiple renditions and delete", func(t *testing.T) {
		setup := setupRenditionTest(t)
		designID := createTestDesignForRendition(t, setup, "PRJ-MULTI-TEST", "Multi Test Design")

		// Create two renditions
		rendition1 := createTestRendition(entities.RenditionCompleted, "/multi1.webp")
		rendition2 := createTestRendition(entities.RenditionStarted, "/multi2.webp")

		id1, err := setup.db.InsertRendition(setup.ctx, designID, rendition1)
		require.NoError(t, err)
		id2, err := setup.db.InsertRendition(setup.ctx, designID, rendition2)
		require.NoError(t, err)

		// Verify both exist
		renditions, err := setup.db.Renditions(setup.ctx, []uuid.UUID{id1, id2})
		require.NoError(t, err)
		assert.Len(t, renditions, 2)

		// Delete one
		err = setup.db.DeleteRendition(setup.ctx, id1)
		require.NoError(t, err)

		// Verify deletion
		remaining, err := setup.db.Renditions(setup.ctx, []uuid.UUID{id1})
		require.NoError(t, err)
		assert.Len(t, remaining, 0)

		// Verify other still exists
		remaining, err = setup.db.RenditionsForDesign(setup.ctx, designID)
		require.NoError(t, err)
		assert.Len(t, remaining, 1)
		assert.Equal(t, id2, remaining[0].Id)
	})

	t.Run("delete non-existent rendition", func(t *testing.T) {
		setup := setupRenditionTest(t)
		err := setup.db.DeleteRendition(setup.ctx, uuid.New())
		require.Error(t, err)
		assert.Contains(t, err.Error(), "target not found in storage")
	})
}

func TestRelationalDb_Integration_Renditions_ValidationAndEdgeCases(t *testing.T) {
	setup := setupRenditionTest(t)
	designID := createTestDesignForRendition(t, setup, "PRJ-VALIDATION-TEST", "Validation Test Design")

	t.Run("validation errors", func(t *testing.T) {
		testCases := []struct {
			name        string
			rendition   entities.Rendition
			shouldError bool
			errorText   string
		}{
			{
				name: "completed rendition without URL",
				rendition: entities.Rendition{
					Id:     uuid.New(),
					Status: entities.RenditionCompleted,
					URL:    &url.URL{}, // Empty URL
				},
				shouldError: true,
				errorText:   "invalid payload in request",
			},
			{
				name: "pending rendition with URL",
				rendition: entities.Rendition{
					Id:     uuid.New(),
					Status: entities.RenditionPending,
					URL:    &url.URL{Scheme: "https", Host: "example.com", Path: "/valid.webp"},
				},
				shouldError: false,
			},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				_, err := setup.db.InsertRendition(setup.ctx, designID, tc.rendition)
				if tc.shouldError {
					require.Error(t, err)
					assert.Contains(t, err.Error(), tc.errorText)
				} else {
					require.NoError(t, err)
				}
			})
		}
	})

	t.Run("update validation", func(t *testing.T) {
		// Create a pending rendition
		testRendition := createTestRendition(entities.RenditionPending, "/update-validation.webp")
		renditionID, err := setup.db.InsertRendition(setup.ctx, designID, testRendition)
		require.NoError(t, err)

		testCases := []struct {
			name   string
			update entities.RenditionDiff
			error  string
		}{
			{
				name:   "completed without URL",
				update: entities.RenditionDiff{Id: renditionID, Status: entities.RenditionCompleted, URL: nil},
				error:  "invalid payload in request",
			},
			{
				name:   "empty status",
				update: entities.RenditionDiff{Id: renditionID, Status: "", URL: nil},
				error:  "invalid payload in request",
			},
			{
				name:   "non-existent rendition",
				update: entities.RenditionDiff{Id: uuid.New(), Status: entities.RenditionArchived},
				error:  "target not found in storage",
			},
		}

		for _, tc := range testCases {
			t.Run(tc.name, func(t *testing.T) {
				err := setup.db.UpdateRendition(setup.ctx, tc.update)
				require.Error(t, err)
				assert.Contains(t, err.Error(), tc.error)
			})
		}
	})

	t.Run("edge cases", func(t *testing.T) {
		// Nil UUID should generate new UUID
		renditionWithNilID := createTestRendition(entities.RenditionStarted, "/nil-id.webp")
		renditionWithNilID.Id = uuid.Nil

		generatedID, err := setup.db.InsertRendition(setup.ctx, designID, renditionWithNilID)
		require.NoError(t, err)
		assert.NotEqual(t, uuid.Nil, generatedID)

		// Empty list should not error
		emptyRenditions, err := setup.db.Renditions(setup.ctx, []uuid.UUID{})
		require.NoError(t, err)
		assert.Len(t, emptyRenditions, 0)

		// Non-existent design should return empty
		nonExistentRenditions, err := setup.db.RenditionsForDesign(setup.ctx, uuid.New())
		require.NoError(t, err)
		assert.Len(t, nonExistentRenditions, 0)
	})

	t.Run("all status types", func(t *testing.T) {
		statuses := []entities.RenditionStatus{
			entities.RenditionPending, entities.RenditionStarted, entities.RenditionCompleted,
			entities.RenditionOutdated, entities.RenditionArchived,
		}

		var renditionIDs []uuid.UUID
		for _, status := range statuses {
			rendition := createTestRendition(status, "/status-"+string(status)+".webp")
			id, err := setup.db.InsertRendition(setup.ctx, designID, rendition)
			require.NoError(t, err)
			renditionIDs = append(renditionIDs, id)
		}

		// Verify all were created with correct statuses
		renditions, err := setup.db.Renditions(setup.ctx, renditionIDs)
		require.NoError(t, err)
		assert.Len(t, renditions, len(statuses))

		statusMap := make(map[uuid.UUID]entities.RenditionStatus)
		for _, r := range renditions {
			statusMap[r.Id] = r.Status
		}

		for i, expectedStatus := range statuses {
			assert.Equal(t, expectedStatus, statusMap[renditionIDs[i]])
		}
	})
}

func TestRelationalDb_Integration_MarkRenditionsOutdatedForDesign(t *testing.T) {
	setup := setupRenditionTest(t)

	// Create two designs to test isolation
	designID1 := createTestDesignForRendition(t, setup, "PRJ-OUTDATED-1", "Design 1")
	designID2 := createTestDesignForRendition(t, setup, "PRJ-OUTDATED-2", "Design 2")

	t.Run("mark multiple renditions outdated", func(t *testing.T) {
		// Create renditions with different statuses for design 1
		rendition1 := createTestRendition(entities.RenditionCompleted, "/r1.jpg")
		rendition2 := createTestRendition(entities.RenditionPending, "/r2.jpg")
		rendition3 := createTestRendition(entities.RenditionStarted, "/r3.jpg")

		id1, err := setup.db.InsertRendition(setup.ctx, designID1, rendition1)
		require.NoError(t, err)
		id2, err := setup.db.InsertRendition(setup.ctx, designID1, rendition2)
		require.NoError(t, err)
		id3, err := setup.db.InsertRendition(setup.ctx, designID1, rendition3)
		require.NoError(t, err)

		// Create control rendition for design 2
		controlRendition := createTestRendition(entities.RenditionCompleted, "/control.jpg")
		controlID, err := setup.db.InsertRendition(setup.ctx, designID2, controlRendition)
		require.NoError(t, err)

		// Mark design 1 renditions outdated
		err = setup.db.MarkRenditionsOutdatedForDesign(setup.ctx, designID1)
		require.NoError(t, err)

		// Verify design 1 renditions are outdated
		assertRenditionStatus(t, setup, id1, entities.RenditionOutdated)
		assertRenditionStatus(t, setup, id2, entities.RenditionOutdated)
		assertRenditionStatus(t, setup, id3, entities.RenditionOutdated)

		// Verify design 2 rendition unchanged
		assertRenditionStatus(t, setup, controlID, entities.RenditionCompleted)
	})

	t.Run("edge cases", func(t *testing.T) {
		// Empty design should not error
		emptyDesignID := createTestDesignForRendition(t, setup, "PRJ-EMPTY", "Empty Design")
		err := setup.db.MarkRenditionsOutdatedForDesign(setup.ctx, emptyDesignID)
		require.NoError(t, err)

		// Non-existent design should not error
		err = setup.db.MarkRenditionsOutdatedForDesign(setup.ctx, uuid.New())
		require.NoError(t, err)

		// Already outdated renditions should remain outdated
		outdatedRendition := createTestRendition(entities.RenditionOutdated, "/already-outdated.jpg")
		_, err = setup.db.InsertRendition(setup.ctx, designID2, outdatedRendition)
		require.NoError(t, err)

		err = setup.db.MarkRenditionsOutdatedForDesign(setup.ctx, designID2)
		require.NoError(t, err)

		// Verify all renditions for design 2 are outdated
		renditions, err := setup.db.RenditionsForDesign(setup.ctx, designID2)
		require.NoError(t, err)
		for _, r := range renditions {
			assert.Equal(t, entities.RenditionOutdated, r.Status)
		}
	})
}

func TestRelationalDb_Integration_MarkAllCompletedRenditionsOutdatedForProject(t *testing.T) {
	setup := setupRenditionTest(t)

	// Create designs in two projects
	projectID1 := entities.NewProjectId("PRJ-COMPLETED-1")
	projectID2 := entities.NewProjectId("PRJ-COMPLETED-2")

	designID1 := createTestDesignForRendition(t, setup, projectID1, "Project 1 Design 1")
	designID2 := createTestDesignForRendition(t, setup, projectID1, "Project 1 Design 2")
	designID3 := createTestDesignForRendition(t, setup, projectID2, "Project 2 Design 1")

	t.Run("mark completed renditions outdated for project", func(t *testing.T) {
		// Create renditions with various statuses
		// Project 1 renditions
		p1d1r1 := createTestRendition(entities.RenditionCompleted, "/p1d1r1.jpg") // Should be outdated
		p1d1r2 := createTestRendition(entities.RenditionPending, "/p1d1r2.jpg")   // Should remain pending
		p1d2r1 := createTestRendition(entities.RenditionCompleted, "/p1d2r1.jpg") // Should be outdated
		p1d2r2 := createTestRendition(entities.RenditionOutdated, "/p1d2r2.jpg")  // Should remain outdated

		// Project 2 renditions (control group)
		p2d1r1 := createTestRendition(entities.RenditionCompleted, "/p2d1r1.jpg") // Should remain completed
		p2d1r2 := createTestRendition(entities.RenditionPending, "/p2d1r2.jpg")   // Should remain pending

		// Insert all renditions
		p1d1r1ID, err := setup.db.InsertRendition(setup.ctx, designID1, p1d1r1)
		require.NoError(t, err)
		p1d1r2ID, err := setup.db.InsertRendition(setup.ctx, designID1, p1d1r2)
		require.NoError(t, err)
		p1d2r1ID, err := setup.db.InsertRendition(setup.ctx, designID2, p1d2r1)
		require.NoError(t, err)
		p1d2r2ID, err := setup.db.InsertRendition(setup.ctx, designID2, p1d2r2)
		require.NoError(t, err)
		p2d1r1ID, err := setup.db.InsertRendition(setup.ctx, designID3, p2d1r1)
		require.NoError(t, err)
		p2d1r2ID, err := setup.db.InsertRendition(setup.ctx, designID3, p2d1r2)
		require.NoError(t, err)

		// Mark all completed renditions outdated for Project 1
		err = setup.db.MarkAllCompletedRenditionsOutdatedForProject(setup.ctx, projectID1)
		require.NoError(t, err)

		// Verify only completed renditions in Project 1 are outdated
		assertRenditionStatus(t, setup, p1d1r1ID, entities.RenditionOutdated) // Was completed
		assertRenditionStatus(t, setup, p1d1r2ID, entities.RenditionPending)  // Was pending
		assertRenditionStatus(t, setup, p1d2r1ID, entities.RenditionOutdated) // Was completed
		assertRenditionStatus(t, setup, p1d2r2ID, entities.RenditionOutdated) // Was already outdated

		// Verify Project 2 renditions are unaffected
		assertRenditionStatus(t, setup, p2d1r1ID, entities.RenditionCompleted) // Should remain completed
		assertRenditionStatus(t, setup, p2d1r2ID, entities.RenditionPending)   // Should remain pending
	})

	t.Run("edge cases", func(t *testing.T) {
		// Empty project should not error
		emptyProjectID := entities.NewProjectId("PRJ-EMPTY")
		err := setup.db.MarkAllCompletedRenditionsOutdatedForProject(setup.ctx, emptyProjectID)
		require.NoError(t, err)

		// Project with designs but no renditions should not error
		projectWithNoRenditions := entities.NewProjectId("PRJ-NO-RENDITIONS")
		_ = createTestDesignForRendition(t, setup, projectWithNoRenditions, "Design with no renditions")
		err = setup.db.MarkAllCompletedRenditionsOutdatedForProject(setup.ctx, projectWithNoRenditions)
		require.NoError(t, err)

		// Mark Project 2 completed renditions outdated independently
		err = setup.db.MarkAllCompletedRenditionsOutdatedForProject(setup.ctx, projectID2)
		require.NoError(t, err)

		// Verify Project 2 completed renditions are now outdated
		renditions, err := setup.db.RenditionsForDesign(setup.ctx, designID3)
		require.NoError(t, err)
		for _, r := range renditions {
			if r.Status == entities.RenditionCompleted {
				t.Errorf("Expected no completed renditions, but found one with ID %s", r.Id)
			}
		}
	})
}
