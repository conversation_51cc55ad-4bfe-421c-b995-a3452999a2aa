package gateways

import (
	"context"

	"github.com/openai/openai-go"
	"github.com/openai/openai-go/option"
	"github.com/openai/openai-go/responses"
)

func NewFakeLLM() *OpenAI {
	return &OpenAI{chatCompleter: NewFakeChatCompletionSvc(), responder: NewFakeResponder()}
}

type fakeChatCompletionSvc struct{}

func NewFakeChatCompletionSvc() *fakeChatCompletionSvc {
	return &fakeChatCompletionSvc{}
}

func (f *fakeChatCompletionSvc) New(ctx context.Context, body openai.ChatCompletionNewParams,
	opts ...option.RequestOption) (res *openai.ChatCompletion, err error) {
	result := openai.ChatCompletion{
		Choices: []openai.ChatCompletionChoice{
			{
				Message: openai.ChatCompletionMessage{
					Content: `{"title": "Fake Title", "description": "Fake Description"}`,
				},
			},
		},
	}
	return &result, nil
}

type fakeResponder struct{}

func NewFakeResponder() *fakeResponder {
	return &fakeResponder{}
}

func (f *fakeResponder) New(ctx context.Context, params responses.ResponseNewParams,
	opts ...option.RequestOption) (*responses.Response, error) {
	return &responses.Response{
		Output: []responses.ResponseOutputItemUnion{{
			Result: "Fake Response",
			Content: []responses.ResponseOutputMessageContentUnion{
				{
					Text: "Fake Response",
				},
			},
		}},
	}, nil
}
