SET statement_timeout = 0;
SET lock_timeout = 0;
SET idle_in_transaction_session_timeout = 0;
SET transaction_timeout = 0;
SET client_encoding = 'UTF8';
SET standard_conforming_strings = on;
SELECT pg_catalog.set_config('search_path', '', false);
SET check_function_bodies = false;
SET xmloption = content;
SET client_min_messages = warning;
SET row_security = off;

--
-- Name: design; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA design;


--
-- Name: design_pkg; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA design_pkg;


--
-- Name: layout; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA layout;


--
-- Name: template; Type: SCHEMA; Schema: -; Owner: -
--

CREATE SCHEMA template;


--
-- Name: location_type; Type: TYPE; Schema: design; Owner: -
--

CREATE TYPE design.location_type AS ENUM (
    'Floor',
    'ShowerFloor',
    'ShowerNiche',
    'ShowerWall',
    'Wall',
    'Unspecified'
);


--
-- Name: color_scheme_enum; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.color_scheme_enum AS ENUM (
    'Neutral',
    'Bold'
);


--
-- Name: http_img_url; Type: DOMAIN; Schema: public; Owner: -
--

CREATE DOMAIN public.http_img_url AS text
	CONSTRAINT http_img_url_check CHECK ((VALUE ~ '^https?://.*\.(webp|jpe?g|png)$'::text));


--
-- Name: posint; Type: DOMAIN; Schema: public; Owner: -
--

CREATE DOMAIN public.posint AS integer
	CONSTRAINT posint_check CHECK ((VALUE >= 0));


--
-- Name: rendition_status_enum; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.rendition_status_enum AS ENUM (
    'Pending',
    'Started',
    'Completed',
    'Outdated',
    'Archived'
);


--
-- Name: style_enum; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.style_enum AS ENUM (
    'Traditional',
    'Transitional',
    'Mid-century',
    'Modern'
);


--
-- Name: tile_pattern_enum; Type: TYPE; Schema: public; Owner: -
--

CREATE TYPE public.tile_pattern_enum AS ENUM (
    'Vertical',
    'Horizontal',
    'HalfOffset',
    'ThirdOffset',
    'Herringbone'
);


--
-- Name: maintain_updated_at_column(); Type: FUNCTION; Schema: public; Owner: -
--

CREATE FUNCTION public.maintain_updated_at_column() RETURNS trigger
    LANGUAGE plpgsql
    AS $$ BEGIN NEW.updated_at = NOW();
RETURN NEW;
END;
$$;


SET default_tablespace = '';

SET default_table_access_method = heap;

--
-- Name: cart_inclusions; Type: TABLE; Schema: design; Owner: -
--

CREATE TABLE design.cart_inclusions (
    room_design_id uuid NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    product_id uuid NOT NULL,
    location design.location_type DEFAULT 'Unspecified'::design.location_type NOT NULL,
    include boolean DEFAULT true NOT NULL,
    quantity_diff integer DEFAULT 0 NOT NULL
);


--
-- Name: TABLE cart_inclusions; Type: COMMENT; Schema: design; Owner: -
--

COMMENT ON TABLE design.cart_inclusions IS 'Records product inclusion preferences for room designs. Each row represents a user decision to
include/exclude a specific product at a particular location, with optional quantity adjustments.
This allows customization of the materials list without modifying the base design.';


--
-- Name: COLUMN cart_inclusions.room_design_id; Type: COMMENT; Schema: design; Owner: -
--

COMMENT ON COLUMN design.cart_inclusions.room_design_id IS 'References the room design this inclusion applies to';


--
-- Name: COLUMN cart_inclusions.product_id; Type: COMMENT; Schema: design; Owner: -
--

COMMENT ON COLUMN design.cart_inclusions.product_id IS 'Identifier of the product being included or excluded';


--
-- Name: COLUMN cart_inclusions.location; Type: COMMENT; Schema: design; Owner: -
--

COMMENT ON COLUMN design.cart_inclusions.location IS 'Physical location for product placement: FLOOR, SHOWER_FLOOR, SHOWER_NICHE, SHOWER_WALL, WALL, or UNSPECIFIED when location is unknown/irrelevant';


--
-- Name: COLUMN cart_inclusions.include; Type: COMMENT; Schema: design; Owner: -
--

COMMENT ON COLUMN design.cart_inclusions.include IS 'True to include product in cart, false to exclude from cart';


--
-- Name: COLUMN cart_inclusions.quantity_diff; Type: COMMENT; Schema: design; Owner: -
--

COMMENT ON COLUMN design.cart_inclusions.quantity_diff IS 'Quantity adjustment from base design quantity (e.g., -1, 0, +2)';


--
-- Name: default_products; Type: TABLE; Schema: design; Owner: -
--

CREATE TABLE design.default_products (
    room_design_id uuid NOT NULL,
    floor_tile uuid,
    floor_tile_pattern public.tile_pattern_enum,
    paint uuid,
    toilet uuid,
    vanity uuid,
    faucet uuid,
    mirror uuid,
    lighting uuid,
    shelving uuid,
    wall_tile_placement text NOT NULL,
    wall_tile uuid,
    wall_tile_pattern public.tile_pattern_enum,
    wallpaper_placement text NOT NULL,
    wallpaper uuid,
    shower_system uuid,
    shower_floor_tile uuid,
    shower_floor_tile_pattern public.tile_pattern_enum,
    shower_wall_tile uuid,
    shower_wall_tile_pattern public.tile_pattern_enum,
    shower_short_wall_tile uuid,
    shower_glass uuid,
    niche_tile uuid,
    tub uuid,
    tub_filler uuid,
    tub_door uuid
);


--
-- Name: render_prefs; Type: TABLE; Schema: design; Owner: -
--

CREATE TABLE design.render_prefs (
    room_design_id uuid NOT NULL,
    shower_glass_visible boolean DEFAULT false NOT NULL,
    tub_door_visible boolean DEFAULT false NOT NULL,
    niches_visible boolean DEFAULT false NOT NULL
);


--
-- Name: retail_info; Type: TABLE; Schema: design; Owner: -
--

CREATE TABLE design.retail_info (
    room_design_id uuid NOT NULL,
    total_price_cents public.posint,
    lead_time_days public.posint,
    sku_count public.posint
);


--
-- Name: room_designs; Type: TABLE; Schema: design; Owner: -
--

CREATE TABLE design.room_designs (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    project_id text,
    room_layout_id uuid,
    status text,
    title text,
    description text,
    color_scheme public.color_scheme_enum,
    style public.style_enum
);


--
-- Name: options; Type: TABLE; Schema: design_pkg; Owner: -
--

CREATE TABLE design_pkg.options (
    design_pkg_id uuid NOT NULL,
    alcove_tub uuid NOT NULL,
    freestanding_tub uuid NOT NULL,
    shower_glass_fixed uuid NOT NULL,
    shower_glass_sliding uuid NOT NULL,
    shower_system_full uuid NOT NULL,
    shower_system_shower uuid NOT NULL,
    tub_door_fixed uuid NOT NULL,
    tub_door_sliding uuid NOT NULL
);


--
-- Name: packages; Type: TABLE; Schema: design_pkg; Owner: -
--

CREATE TABLE design_pkg.packages (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    color_scheme public.color_scheme_enum NOT NULL,
    style public.style_enum NOT NULL,
    render_priority public.posint,
    name text NOT NULL,
    description text NOT NULL,
    image_url public.http_img_url NOT NULL,
    inspiration text NOT NULL,
    atmosphere text[] NOT NULL,
    color_palette text[] NOT NULL,
    material_palette text[] NOT NULL,
    highlighted_brand_urls public.http_img_url[]
);


--
-- Name: product_selections; Type: TABLE; Schema: design_pkg; Owner: -
--

CREATE TABLE design_pkg.product_selections (
    design_pkg_id uuid NOT NULL,
    floor_tile uuid NOT NULL,
    lighting uuid NOT NULL,
    mirror uuid NOT NULL,
    paint uuid NOT NULL,
    shelving uuid NOT NULL,
    toilet uuid NOT NULL,
    shower_floor_tile uuid NOT NULL,
    shower_wall_tile uuid NOT NULL,
    tub_filler uuid NOT NULL,
    wall_tile_placement text NOT NULL,
    wall_tile uuid,
    wallpaper_placement text NOT NULL,
    wallpaper uuid
);


--
-- Name: provenance; Type: TABLE; Schema: design_pkg; Owner: -
--

CREATE TABLE design_pkg.provenance (
    design_pkg_id uuid NOT NULL,
    lighting_brand text NOT NULL,
    plumbing_brand text NOT NULL,
    toilet_brand text NOT NULL,
    vanity_brand text NOT NULL,
    vanity_storage text NOT NULL
);


--
-- Name: legacy_lookup; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.legacy_lookup (
    id character varying(2) NOT NULL,
    design_pkg_id uuid NOT NULL,
    rendition_id uuid
);


--
-- Name: details; Type: VIEW; Schema: design_pkg; Owner: -
--

CREATE VIEW design_pkg.details AS
 SELECT dp.id,
    dp.created_at,
    dp.updated_at,
    dp.color_scheme,
    dp.style,
    dp.render_priority,
    dp.name,
    dp.description,
    dp.image_url,
    dp.inspiration,
    dp.atmosphere,
    dp.color_palette,
    dp.material_palette,
    dp.highlighted_brand_urls,
    ps.floor_tile,
    ps.lighting,
    ps.mirror,
    ps.paint,
    ps.shelving,
    ps.toilet,
    ps.shower_floor_tile,
    ps.shower_wall_tile,
    ps.tub_filler,
    ps.wall_tile_placement,
    ps.wall_tile,
    ps.wallpaper_placement,
    ps.wallpaper,
    o.alcove_tub,
    o.freestanding_tub,
    o.shower_glass_fixed,
    o.shower_glass_sliding,
    o.shower_system_full,
    o.shower_system_shower,
    o.tub_door_fixed,
    o.tub_door_sliding,
    p.lighting_brand,
    p.plumbing_brand,
    p.toilet_brand,
    p.vanity_brand,
    p.vanity_storage,
    ll.id AS legacy_id
   FROM ((((design_pkg.packages dp
     LEFT JOIN design_pkg.product_selections ps ON ((dp.id = ps.design_pkg_id)))
     LEFT JOIN design_pkg.options o ON ((dp.id = o.design_pkg_id)))
     LEFT JOIN design_pkg.provenance p ON ((dp.id = p.design_pkg_id)))
     LEFT JOIN public.legacy_lookup ll ON ((dp.id = ll.design_pkg_id)));


--
-- Name: VIEW details; Type: COMMENT; Schema: design_pkg; Owner: -
--

COMMENT ON VIEW design_pkg.details IS 'Comprehensive view that joins all tables related to design packages except vanity_scaling_options.
This view simplifies design package retrieval by consolidating complex joins into a single queryable view.
Note: vanity_scaling_options are still fetched separately due to their one-to-many relationship.';


--
-- Name: vanity_scaling_options; Type: TABLE; Schema: design_pkg; Owner: -
--

CREATE TABLE design_pkg.vanity_scaling_options (
    design_pkg_id uuid NOT NULL,
    min_vanity_length_inches public.posint NOT NULL,
    vanity_product_id uuid NOT NULL,
    faucet_product_id uuid NOT NULL
);


--
-- Name: room_layouts; Type: TABLE; Schema: layout; Owner: -
--

CREATE TABLE layout.room_layouts (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    raw_data jsonb NOT NULL,
    xxhash bytea NOT NULL,
    CONSTRAINT chk_room_layouts_raw_data CHECK ((jsonb_typeof(raw_data) = 'object'::text))
);


--
-- Name: renditions; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.renditions (
    id uuid DEFAULT gen_random_uuid() NOT NULL,
    created_at timestamp with time zone DEFAULT now() NOT NULL,
    updated_at timestamp with time zone DEFAULT now() NOT NULL,
    status public.rendition_status_enum NOT NULL,
    url public.http_img_url,
    room_design_id uuid NOT NULL,
    CONSTRAINT chk_rendition_url CHECK (((status <> 'Completed'::public.rendition_status_enum) OR (url IS NOT NULL)))
);


--
-- Name: schema_migrations; Type: TABLE; Schema: public; Owner: -
--

CREATE TABLE public.schema_migrations (
    version character varying NOT NULL
);


--
-- Name: template_details; Type: VIEW; Schema: template; Owner: -
--

CREATE VIEW template.template_details AS
 SELECT dp.id,
    dp.created_at,
    dp.updated_at,
    dp.color_scheme,
    dp.style,
    dp.render_priority,
    dp.name,
    dp.description,
    dp.image_url,
    dp.inspiration,
    dp.atmosphere,
    dp.color_palette,
    dp.material_palette,
    dp.highlighted_brand_urls,
    ps.floor_tile,
    ps.lighting,
    ps.mirror,
    ps.paint,
    ps.shelving,
    ps.toilet,
    ps.shower_floor_tile,
    ps.shower_wall_tile,
    ps.tub_filler,
    ps.wall_tile_placement,
    ps.wall_tile,
    ps.wallpaper_placement,
    ps.wallpaper,
    o.alcove_tub,
    o.freestanding_tub,
    o.shower_glass_fixed,
    o.shower_glass_sliding,
    o.shower_system_full,
    o.shower_system_shower,
    o.tub_door_fixed,
    o.tub_door_sliding,
    p.lighting_brand,
    p.plumbing_brand,
    p.toilet_brand,
    p.vanity_brand,
    p.vanity_storage,
    ll.id AS legacy_id
   FROM ((((design_pkg.packages dp
     LEFT JOIN design_pkg.product_selections ps ON ((dp.id = ps.design_pkg_id)))
     LEFT JOIN design_pkg.options o ON ((dp.id = o.design_pkg_id)))
     LEFT JOIN design_pkg.provenance p ON ((dp.id = p.design_pkg_id)))
     LEFT JOIN public.legacy_lookup ll ON ((dp.id = ll.design_pkg_id)));


--
-- Name: VIEW template_details; Type: COMMENT; Schema: template; Owner: -
--

COMMENT ON VIEW template.template_details IS 'Comprehensive view that joins all tables related to design packages except vanity_scaling_options.
This view simplifies design package retrieval by consolidating complex joins into a single queryable view.
Note: vanity_scaling_options are still fetched separately due to their one-to-many relationship.
Duplicated from design_pkg.details for backwards compatibility.';


--
-- Name: vanity_scaling_options; Type: VIEW; Schema: template; Owner: -
--

CREATE VIEW template.vanity_scaling_options AS
 SELECT design_pkg_id AS template_id,
    min_vanity_length_inches,
    vanity_product_id,
    faucet_product_id
   FROM design_pkg.vanity_scaling_options;


--
-- Name: cart_inclusions cart_inclusions_pkey; Type: CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.cart_inclusions
    ADD CONSTRAINT cart_inclusions_pkey PRIMARY KEY (room_design_id, product_id, location);


--
-- Name: default_products default_products_pkey; Type: CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.default_products
    ADD CONSTRAINT default_products_pkey PRIMARY KEY (room_design_id);


--
-- Name: render_prefs render_prefs_pkey; Type: CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.render_prefs
    ADD CONSTRAINT render_prefs_pkey PRIMARY KEY (room_design_id);


--
-- Name: retail_info retail_info_pkey; Type: CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.retail_info
    ADD CONSTRAINT retail_info_pkey PRIMARY KEY (room_design_id);


--
-- Name: room_designs room_designs_pkey; Type: CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.room_designs
    ADD CONSTRAINT room_designs_pkey PRIMARY KEY (id);


--
-- Name: options design_pkg_options_pkey; Type: CONSTRAINT; Schema: design_pkg; Owner: -
--

ALTER TABLE ONLY design_pkg.options
    ADD CONSTRAINT design_pkg_options_pkey PRIMARY KEY (design_pkg_id);


--
-- Name: packages design_pkg_packages_image_url_key; Type: CONSTRAINT; Schema: design_pkg; Owner: -
--

ALTER TABLE ONLY design_pkg.packages
    ADD CONSTRAINT design_pkg_packages_image_url_key UNIQUE (image_url);


--
-- Name: packages design_pkg_packages_name_key; Type: CONSTRAINT; Schema: design_pkg; Owner: -
--

ALTER TABLE ONLY design_pkg.packages
    ADD CONSTRAINT design_pkg_packages_name_key UNIQUE (name);


--
-- Name: packages design_pkg_packages_pkey; Type: CONSTRAINT; Schema: design_pkg; Owner: -
--

ALTER TABLE ONLY design_pkg.packages
    ADD CONSTRAINT design_pkg_packages_pkey PRIMARY KEY (id);


--
-- Name: product_selections design_pkg_product_selections_pkey; Type: CONSTRAINT; Schema: design_pkg; Owner: -
--

ALTER TABLE ONLY design_pkg.product_selections
    ADD CONSTRAINT design_pkg_product_selections_pkey PRIMARY KEY (design_pkg_id);


--
-- Name: provenance design_pkg_provenance_pkey; Type: CONSTRAINT; Schema: design_pkg; Owner: -
--

ALTER TABLE ONLY design_pkg.provenance
    ADD CONSTRAINT design_pkg_provenance_pkey PRIMARY KEY (design_pkg_id);


--
-- Name: vanity_scaling_options vanity_scaling_options_design_pkg_id_min_vanity_length_inches_k; Type: CONSTRAINT; Schema: design_pkg; Owner: -
--

ALTER TABLE ONLY design_pkg.vanity_scaling_options
    ADD CONSTRAINT vanity_scaling_options_design_pkg_id_min_vanity_length_inches_k UNIQUE (design_pkg_id, min_vanity_length_inches);


--
-- Name: room_layouts room_layouts_pkey; Type: CONSTRAINT; Schema: layout; Owner: -
--

ALTER TABLE ONLY layout.room_layouts
    ADD CONSTRAINT room_layouts_pkey PRIMARY KEY (id);


--
-- Name: legacy_lookup legacy_lookup_design_pkg_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.legacy_lookup
    ADD CONSTRAINT legacy_lookup_design_pkg_id_key UNIQUE (design_pkg_id);


--
-- Name: legacy_lookup legacy_lookup_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.legacy_lookup
    ADD CONSTRAINT legacy_lookup_pkey PRIMARY KEY (id);


--
-- Name: legacy_lookup legacy_lookup_rendition_id_key; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.legacy_lookup
    ADD CONSTRAINT legacy_lookup_rendition_id_key UNIQUE (rendition_id);


--
-- Name: renditions renditions_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.renditions
    ADD CONSTRAINT renditions_pkey PRIMARY KEY (id);


--
-- Name: schema_migrations schema_migrations_pkey; Type: CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.schema_migrations
    ADD CONSTRAINT schema_migrations_pkey PRIMARY KEY (version);


--
-- Name: room_designs_project_id_idx; Type: INDEX; Schema: design; Owner: -
--

CREATE INDEX room_designs_project_id_idx ON design.room_designs USING btree (project_id);


--
-- Name: room_designs_room_layout_id_idx; Type: INDEX; Schema: design; Owner: -
--

CREATE INDEX room_designs_room_layout_id_idx ON design.room_designs USING btree (room_layout_id);


--
-- Name: room_layouts_xxhash_idx; Type: INDEX; Schema: layout; Owner: -
--

CREATE INDEX room_layouts_xxhash_idx ON layout.room_layouts USING btree (xxhash);


--
-- Name: cart_inclusions refresh_cart_inclusions_updated_at; Type: TRIGGER; Schema: design; Owner: -
--

CREATE TRIGGER refresh_cart_inclusions_updated_at BEFORE UPDATE ON design.cart_inclusions FOR EACH ROW EXECUTE FUNCTION public.maintain_updated_at_column();


--
-- Name: room_designs refresh_designs_updated_at; Type: TRIGGER; Schema: design; Owner: -
--

CREATE TRIGGER refresh_designs_updated_at BEFORE UPDATE ON design.room_designs FOR EACH ROW EXECUTE FUNCTION public.maintain_updated_at_column();


--
-- Name: packages refresh_design_pkg_packages_updated_at; Type: TRIGGER; Schema: design_pkg; Owner: -
--

CREATE TRIGGER refresh_design_pkg_packages_updated_at BEFORE UPDATE ON design_pkg.packages FOR EACH ROW EXECUTE FUNCTION public.maintain_updated_at_column();


--
-- Name: room_layouts refresh_layouts_updated_at; Type: TRIGGER; Schema: layout; Owner: -
--

CREATE TRIGGER refresh_layouts_updated_at BEFORE UPDATE ON layout.room_layouts FOR EACH ROW EXECUTE FUNCTION public.maintain_updated_at_column();


--
-- Name: renditions refresh_renditions_updated_at; Type: TRIGGER; Schema: public; Owner: -
--

CREATE TRIGGER refresh_renditions_updated_at BEFORE UPDATE ON public.renditions FOR EACH ROW EXECUTE FUNCTION public.maintain_updated_at_column();


--
-- Name: cart_inclusions cart_inclusions_room_design_id_fkey; Type: FK CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.cart_inclusions
    ADD CONSTRAINT cart_inclusions_room_design_id_fkey FOREIGN KEY (room_design_id) REFERENCES design.room_designs(id) ON DELETE CASCADE;


--
-- Name: default_products default_products_room_design_id_fkey; Type: FK CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.default_products
    ADD CONSTRAINT default_products_room_design_id_fkey FOREIGN KEY (room_design_id) REFERENCES design.room_designs(id) ON DELETE CASCADE;


--
-- Name: render_prefs render_prefs_room_design_id_fkey; Type: FK CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.render_prefs
    ADD CONSTRAINT render_prefs_room_design_id_fkey FOREIGN KEY (room_design_id) REFERENCES design.room_designs(id) ON DELETE CASCADE;


--
-- Name: retail_info retail_info_room_design_id_fkey; Type: FK CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.retail_info
    ADD CONSTRAINT retail_info_room_design_id_fkey FOREIGN KEY (room_design_id) REFERENCES design.room_designs(id) ON DELETE CASCADE;


--
-- Name: room_designs room_designs_room_layout_id_fkey; Type: FK CONSTRAINT; Schema: design; Owner: -
--

ALTER TABLE ONLY design.room_designs
    ADD CONSTRAINT room_designs_room_layout_id_fkey FOREIGN KEY (room_layout_id) REFERENCES layout.room_layouts(id) ON DELETE CASCADE;


--
-- Name: options template_options_template_id_fkey; Type: FK CONSTRAINT; Schema: design_pkg; Owner: -
--

ALTER TABLE ONLY design_pkg.options
    ADD CONSTRAINT template_options_template_id_fkey FOREIGN KEY (design_pkg_id) REFERENCES design_pkg.packages(id) ON DELETE CASCADE;


--
-- Name: product_selections template_product_selections_template_id_fkey; Type: FK CONSTRAINT; Schema: design_pkg; Owner: -
--

ALTER TABLE ONLY design_pkg.product_selections
    ADD CONSTRAINT template_product_selections_template_id_fkey FOREIGN KEY (design_pkg_id) REFERENCES design_pkg.packages(id) ON DELETE CASCADE;


--
-- Name: provenance template_provenance_template_id_fkey; Type: FK CONSTRAINT; Schema: design_pkg; Owner: -
--

ALTER TABLE ONLY design_pkg.provenance
    ADD CONSTRAINT template_provenance_template_id_fkey FOREIGN KEY (design_pkg_id) REFERENCES design_pkg.packages(id) ON DELETE CASCADE;


--
-- Name: vanity_scaling_options vanity_scaling_options_template_id_fkey; Type: FK CONSTRAINT; Schema: design_pkg; Owner: -
--

ALTER TABLE ONLY design_pkg.vanity_scaling_options
    ADD CONSTRAINT vanity_scaling_options_template_id_fkey FOREIGN KEY (design_pkg_id) REFERENCES design_pkg.packages(id) ON DELETE CASCADE;


--
-- Name: legacy_lookup legacy_lookup_rendition_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.legacy_lookup
    ADD CONSTRAINT legacy_lookup_rendition_id_fkey FOREIGN KEY (rendition_id) REFERENCES public.renditions(id) ON DELETE CASCADE;


--
-- Name: legacy_lookup legacy_lookup_template_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.legacy_lookup
    ADD CONSTRAINT legacy_lookup_template_id_fkey FOREIGN KEY (design_pkg_id) REFERENCES design_pkg.packages(id) ON DELETE CASCADE;


--
-- Name: renditions renditions_room_design_id_fkey; Type: FK CONSTRAINT; Schema: public; Owner: -
--

ALTER TABLE ONLY public.renditions
    ADD CONSTRAINT renditions_room_design_id_fkey FOREIGN KEY (room_design_id) REFERENCES design.room_designs(id) ON DELETE CASCADE;


--
-- PostgreSQL database dump complete
--


--
-- Dbmate schema migrations
--

INSERT INTO public.schema_migrations (version) VALUES
    ('20250618235400'),
    ('20250618235402'),
    ('20250618235403'),
    ('20250618235404'),
    ('20250619143144'),
    ('20250630180001'),
    ('20250630195437'),
    ('20250716193645'),
    ('20250721000000'),
    ('20250722120000'),
    ('20250725011151');
