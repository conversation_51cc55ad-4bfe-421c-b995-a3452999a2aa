//go:build integration

package gateways_test

import (
	"context"
	"database/sql"
	"net/url"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// TestRelationalDb_Integration_InsertDesignPackage tests the InsertDesignPackage method
// against a live database to ensure it correctly creates all related records.
func TestRelationalDb_Integration_InsertDesignPackage(t *testing.T) {
	// Arrange
	ctx := context.Background()
	db := gateways.NewRelationalDb(testPool, nil)

	// Ensure the database is clean before this test runs.
	_, err := testPool.Exec(ctx, "TRUNCATE TABLE design_pkg.packages CASCADE;")
	require.NoError(t, err, "Failed to truncate packages table before test")

	legacyId := "00"
	testDesignPkg := usecases.DesignPackage{
		LegacyId: &legacyId,
		Tagged: usecases.Tagged{
			ColorScheme: usecases.Neutral,
			Style:       usecases.Modern,
		},
		Name:            "Test Design Package",
		Description:     "A test design package for integration testing",
		ImageURL:        url.URL{Scheme: "https", Host: "example.com", Path: "/test.jpg"},
		Inspiration:     "Modern minimalist design",
		Atmosphere:      []string{"calm", "serene", "modern"},
		ColorPalette:    []string{"white", "gray", "black"},
		MaterialPalette: []string{"marble", "wood", "metal"},
		HighlightedBrandUrls: []url.URL{
			{Scheme: "https", Host: "example.com", Path: "/highlight1.jpg"},
			{Scheme: "https", Host: "example.com", Path: "/highlight2.jpg"},
		},
		FixedProductSelections: usecases.FixedProductSelections{
			FloorTile:       ptr(uuid.New()),
			Lighting:        ptr(uuid.New()),
			Mirror:          ptr(uuid.New()),
			Paint:           ptr(uuid.New()),
			Shelving:        ptr(uuid.New()),
			Toilet:          ptr(uuid.New()),
			ShowerFloorTile: ptr(uuid.New()),
			ShowerWallTile:  ptr(uuid.New()),
			TubFiller:       ptr(uuid.New()),
			WallTile:        ptr(uuid.New()),
			Wallpaper:       ptr(uuid.New()),
		},
		ProductSelectionOptions: usecases.ProductSelectionOptions{
			AlcoveTub:          uuid.New(),
			FreestandingTub:    uuid.New(),
			ShowerGlassFixed:   uuid.New(),
			ShowerGlassSliding: uuid.New(),
			ShowerSystemCombo:  uuid.New(),
			ShowerSystemSolo:   uuid.New(),
			TubDoorFixed:       uuid.New(),
			TubDoorSliding:     uuid.New(),
		},
		DesignPackageProvenance: usecases.DesignPackageProvenance{
			LightingBrand: ptr("Test Lighting Brand"),
			PlumbingBrand: ptr("Test Plumbing Brand"),
			ToiletBrand:   ptr("Test Toilet Brand"),
			VanityBrand:   ptr("Test Vanity Brand"),
			VanityStorage: ptr("Test Storage"),
		},
		WallTilePlacement:  usecases.HalfWall,
		WallpaperPlacement: usecases.VanityWall,
		VanityScalingOptions: map[int]usecases.VanityScalingOption{
			36: {
				VanityProductID: uuid.New(),
				FaucetProductID: uuid.New(),
			},
			48: {
				VanityProductID: uuid.New(),
				FaucetProductID: uuid.New(),
			},
		},
	}

	// Act - Insert the design package
	designPkgID, err := db.InsertDesignPackage(ctx, testDesignPkg, "00")
	require.NoError(t, err, "InsertDesignPackage failed")
	require.NotEqual(t, uuid.Nil, designPkgID, "InsertDesignPackage should return a valid UUID")

	// Assert - Read the design package back to verify it was created correctly
	readDesignPkg, err := db.ReadDesignPackage(ctx, designPkgID)
	require.NoError(t, err, "ReadDesignPackage failed after insert")

	// Verify design package fields
	assert.Equal(t, designPkgID, readDesignPkg.ID)
	testDesignPkg.ID = readDesignPkg.ID
	assert.Equal(t, legacyId, *readDesignPkg.LegacyId)
	testDesignPkg.LegacyId = readDesignPkg.LegacyId
	assert.NotZero(t, readDesignPkg.UpdatedAt)
	testDesignPkg.UpdatedAt = readDesignPkg.UpdatedAt
	assert.Equal(t, testDesignPkg, readDesignPkg)

	// --- Additional Test: ReadAllDesignPackages consistency ---
	// Verify that ReadAllDesignPackages returns the same design package data as ReadDesignPackage
	allDesignPkgs, err := db.ReadAllDesignPackages(ctx)
	require.NoError(t, err, "ReadAllDesignPackages failed")
	require.NotEmpty(t, allDesignPkgs, "ReadAllDesignPackages should return at least one design package")

	// Find our design package in the results
	var foundDesignPkg *usecases.DesignPackage
	for i := range allDesignPkgs {
		if allDesignPkgs[i].ID == designPkgID {
			foundDesignPkg = &allDesignPkgs[i]
			break
		}
	}
	require.NotNil(t, foundDesignPkg, "Design package should be found in ReadAllDesignPackages results")

	// Verify that the design package from ReadAllDesignPackages matches the one from ReadDesignPackage
	assert.Equal(t, readDesignPkg, *foundDesignPkg)
}

// TestRelationalDb_Integration_DesignPackageVanityScalingOptions tests that vanity scaling options
// are correctly stored and retrieved from the database across different design package operations.
func TestRelationalDb_Integration_DesignPackageVanityScalingOptions(t *testing.T) {
	// Arrange
	ctx := context.Background()
	db := gateways.NewRelationalDb(testPool, nil)

	// Ensure the database is clean before this test runs.
	_, err := testPool.Exec(ctx, "TRUNCATE TABLE design_pkg.packages CASCADE;")
	require.NoError(t, err, "Failed to truncate packages table before test")

	// Create test design packages with vanity scaling options
	designPkg1 := usecases.DesignPackage{
		Tagged: usecases.Tagged{
			ColorScheme: usecases.Neutral,
			Style:       usecases.Modern,
		},
		Name:            "Design Package 1",
		Description:     "Design package with multiple vanity scaling options",
		ImageURL:        url.URL{Scheme: "https", Host: "example.com", Path: "/designPkg1.jpg"},
		Inspiration:     "Modern design with multiple vanity options",
		Atmosphere:      []string{"calm", "modern"},
		ColorPalette:    []string{"white", "gray"},
		MaterialPalette: []string{"marble", "wood"},
		HighlightedBrandUrls: []url.URL{
			{Scheme: "https", Host: "example.com", Path: "/highlight1.jpg"},
		},
		FixedProductSelections: usecases.FixedProductSelections{
			FloorTile:       ptr(uuid.New()),
			Lighting:        ptr(uuid.New()),
			Mirror:          ptr(uuid.New()),
			Paint:           ptr(uuid.New()),
			Shelving:        ptr(uuid.New()),
			Toilet:          ptr(uuid.New()),
			ShowerFloorTile: ptr(uuid.New()),
			ShowerWallTile:  ptr(uuid.New()),
			TubFiller:       ptr(uuid.New()),
			WallTile:        ptr(uuid.New()),
		},
		ProductSelectionOptions: usecases.ProductSelectionOptions{
			AlcoveTub:          uuid.New(),
			FreestandingTub:    uuid.New(),
			ShowerGlassFixed:   uuid.New(),
			ShowerGlassSliding: uuid.New(),
			ShowerSystemCombo:  uuid.New(),
			ShowerSystemSolo:   uuid.New(),
			TubDoorFixed:       uuid.New(),
			TubDoorSliding:     uuid.New(),
		},
		DesignPackageProvenance: usecases.DesignPackageProvenance{
			LightingBrand: ptr("Test Lighting Brand 1"),
			PlumbingBrand: ptr("Test Plumbing Brand 1"),
			ToiletBrand:   ptr("Test Toilet Brand 1"),
			VanityBrand:   ptr("Test Vanity Brand 1"),
			VanityStorage: ptr("Test Storage 1"),
		},
		WallTilePlacement:  usecases.HalfWall,
		WallpaperPlacement: usecases.NoWallpaper,
		VanityScalingOptions: map[int]usecases.VanityScalingOption{
			36: {
				VanityProductID: uuid.New(),
				FaucetProductID: uuid.New(),
			},
			48: {
				VanityProductID: uuid.New(),
				FaucetProductID: uuid.New(),
			},
		},
	}

	designPkg2 := usecases.DesignPackage{
		Tagged: usecases.Tagged{
			ColorScheme: usecases.Bold,
			Style:       usecases.Traditional,
		},
		Name:            "Design Package 2",
		Description:     "Design package with single vanity scaling option",
		ImageURL:        url.URL{Scheme: "https", Host: "example.com", Path: "/designPkg2.jpg"},
		Inspiration:     "Traditional design with single vanity option",
		Atmosphere:      []string{"warm", "traditional"},
		ColorPalette:    []string{"beige", "brown"},
		MaterialPalette: []string{"wood", "ceramic"},
		HighlightedBrandUrls: []url.URL{
			{Scheme: "https", Host: "example.com", Path: "/highlight2.jpg"},
		},
		FixedProductSelections: usecases.FixedProductSelections{
			FloorTile:       ptr(uuid.New()),
			Lighting:        ptr(uuid.New()),
			Mirror:          ptr(uuid.New()),
			Paint:           ptr(uuid.New()),
			Shelving:        ptr(uuid.New()),
			Toilet:          ptr(uuid.New()),
			ShowerFloorTile: ptr(uuid.New()),
			ShowerWallTile:  ptr(uuid.New()),
			TubFiller:       ptr(uuid.New()),
			WallTile:        ptr(uuid.New()),
		},
		ProductSelectionOptions: usecases.ProductSelectionOptions{
			AlcoveTub:          uuid.New(),
			FreestandingTub:    uuid.New(),
			ShowerGlassFixed:   uuid.New(),
			ShowerGlassSliding: uuid.New(),
			ShowerSystemCombo:  uuid.New(),
			ShowerSystemSolo:   uuid.New(),
			TubDoorFixed:       uuid.New(),
			TubDoorSliding:     uuid.New(),
		},
		DesignPackageProvenance: usecases.DesignPackageProvenance{
			LightingBrand: ptr("Test Lighting Brand 2"),
			PlumbingBrand: ptr("Test Plumbing Brand 2"),
			ToiletBrand:   ptr("Test Toilet Brand 2"),
			VanityBrand:   ptr("Test Vanity Brand 2"),
			VanityStorage: ptr("Test Storage 2"),
		},
		WallTilePlacement:  usecases.FullWall,
		WallpaperPlacement: usecases.VanityWall,
		VanityScalingOptions: map[int]usecases.VanityScalingOption{
			60: {
				VanityProductID: uuid.New(),
				FaucetProductID: uuid.New(),
			},
		},
	}

	designPkg3 := usecases.DesignPackage{
		Tagged: usecases.Tagged{
			ColorScheme: usecases.Neutral,
			Style:       usecases.Transitional,
		},
		Name:            "Design Package 3",
		Description:     "Design package with no vanity scaling options",
		ImageURL:        url.URL{Scheme: "https", Host: "example.com", Path: "/designPkg3.jpg"},
		Inspiration:     "Transitional design with no vanity options",
		Atmosphere:      []string{"balanced", "transitional"},
		ColorPalette:    []string{"cream", "taupe"},
		MaterialPalette: []string{"stone", "metal"},
		HighlightedBrandUrls: []url.URL{
			{Scheme: "https", Host: "example.com", Path: "/highlight3.jpg"},
		},
		FixedProductSelections: usecases.FixedProductSelections{
			FloorTile:       ptr(uuid.New()),
			Lighting:        ptr(uuid.New()),
			Mirror:          ptr(uuid.New()),
			Paint:           ptr(uuid.New()),
			Shelving:        ptr(uuid.New()),
			Toilet:          ptr(uuid.New()),
			ShowerFloorTile: ptr(uuid.New()),
			ShowerWallTile:  ptr(uuid.New()),
			TubFiller:       ptr(uuid.New()),
			WallTile:        ptr(uuid.New()),
		},
		ProductSelectionOptions: usecases.ProductSelectionOptions{
			AlcoveTub:          uuid.New(),
			FreestandingTub:    uuid.New(),
			ShowerGlassFixed:   uuid.New(),
			ShowerGlassSliding: uuid.New(),
			ShowerSystemCombo:  uuid.New(),
			ShowerSystemSolo:   uuid.New(),
			TubDoorFixed:       uuid.New(),
			TubDoorSliding:     uuid.New(),
		},
		DesignPackageProvenance: usecases.DesignPackageProvenance{
			LightingBrand: ptr("Test Lighting Brand 3"),
			PlumbingBrand: ptr("Test Plumbing Brand 3"),
			ToiletBrand:   ptr("Test Toilet Brand 3"),
			VanityBrand:   ptr("Test Vanity Brand 3"),
			VanityStorage: ptr("Test Storage 3"),
		},
		WallTilePlacement:    usecases.HalfWall,
		WallpaperPlacement:   usecases.AllWalls,
		VanityScalingOptions: map[int]usecases.VanityScalingOption{}, // Empty
	}

	// Insert design packages
	id1, err := db.InsertDesignPackage(ctx, designPkg1, "")
	require.NoError(t, err, "Failed to insert designPkg1")
	require.NotEqual(t, uuid.Nil, id1)

	id2, err := db.InsertDesignPackage(ctx, designPkg2, "")
	require.NoError(t, err, "Failed to insert designPkg2")
	require.NotEqual(t, uuid.Nil, id2)

	id3, err := db.InsertDesignPackage(ctx, designPkg3, "")
	require.NoError(t, err, "Failed to insert designPkg3")
	require.NotEqual(t, uuid.Nil, id3)

	// Test ReadAllDesignPackages - should populate vanity scaling options correctly
	allDesignPkgs, err := db.ReadAllDesignPackages(ctx)
	require.NoError(t, err, "ReadAllDesignPackages failed")
	require.Len(t, allDesignPkgs, 3, "Should return all 3 design packages")

	// Verify vanity scaling options are preserved
	designPkgMap := make(map[uuid.UUID]usecases.DesignPackage)
	for _, dp := range allDesignPkgs {
		designPkgMap[dp.ID] = dp
	}

	// Check designPkg1 vanity options
	dp1 := designPkgMap[id1]
	require.Len(t, dp1.VanityScalingOptions, 2, "DesignPkg1 should have 2 vanity scaling options")
	require.Contains(t, dp1.VanityScalingOptions, 36, "DesignPkg1 should have 36-inch option")
	require.Contains(t, dp1.VanityScalingOptions, 48, "DesignPkg1 should have 48-inch option")
	require.Equal(t, designPkg1.VanityScalingOptions[36], dp1.VanityScalingOptions[36], "36-inch option should match")
	require.Equal(t, designPkg1.VanityScalingOptions[48], dp1.VanityScalingOptions[48], "48-inch option should match")

	// Check designPkg2 vanity options
	dp2 := designPkgMap[id2]
	require.Len(t, dp2.VanityScalingOptions, 1, "DesignPkg2 should have 1 vanity scaling option")
	require.Contains(t, dp2.VanityScalingOptions, 60, "DesignPkg2 should have 60-inch option")
	require.Equal(t, designPkg2.VanityScalingOptions[60], dp2.VanityScalingOptions[60], "60-inch option should match")

	// Check designPkg3 vanity options (empty)
	dp3 := designPkgMap[id3]
	require.Len(t, dp3.VanityScalingOptions, 0, "DesignPkg3 should have no vanity scaling options")

	// Test DesignPackagesById - should also populate vanity scaling options correctly
	designPkgIDs := []uuid.UUID{id1, id2}
	selectedDesignPkgs, err := db.DesignPackagesById(ctx, designPkgIDs)
	require.NoError(t, err, "DesignPackagesById failed")
	require.Len(t, selectedDesignPkgs, 2, "Should return 2 selected design packages")

	// Verify vanity scaling options are preserved in DesignPackagesById
	selectedMap := make(map[uuid.UUID]usecases.DesignPackage)
	for _, tmpl := range selectedDesignPkgs {
		selectedMap[tmpl.ID] = tmpl
	}

	// Check designPkg1 vanity options from DesignPackagesById
	tmpl1Selected := selectedMap[id1]
	require.Len(t, tmpl1Selected.VanityScalingOptions, 2, "Selected designPkg1 should have 2 vanity scaling options")
	require.Equal(t, designPkg1.VanityScalingOptions[36], tmpl1Selected.VanityScalingOptions[36], "Selected designPkg1 36-inch option should match")
	require.Equal(t, designPkg1.VanityScalingOptions[48], tmpl1Selected.VanityScalingOptions[48], "Selected designPkg1 48-inch option should match")

	// Check designPkg2 vanity options from DesignPackagesById
	tmpl2Selected := selectedMap[id2]
	require.Len(t, tmpl2Selected.VanityScalingOptions, 1, "Selected designPkg2 should have 1 vanity scaling option")
	require.Equal(t, designPkg2.VanityScalingOptions[60], tmpl2Selected.VanityScalingOptions[60], "Selected designPkg2 60-inch option should match")

	// Test ReadDesignPackage individually to ensure consistency
	readDesignPkg1, err := db.ReadDesignPackage(ctx, id1)
	require.NoError(t, err, "ReadDesignPackage for designPkg1 failed")
	require.Equal(t, dp1.VanityScalingOptions, readDesignPkg1.VanityScalingOptions, "ReadDesignPackage should return same vanity options as ReadAllDesignPackages")

	readDesignPkg2, err := db.ReadDesignPackage(ctx, id2)
	require.NoError(t, err, "ReadDesignPackage for designPkg2 failed")
	require.Equal(t, dp2.VanityScalingOptions, readDesignPkg2.VanityScalingOptions, "ReadDesignPackage should return same vanity options as ReadAllDesignPackages")

	readDesignPkg3, err := db.ReadDesignPackage(ctx, id3)
	require.NoError(t, err, "ReadDesignPackage for designPkg3 failed")
	require.Equal(t, dp3.VanityScalingOptions, readDesignPkg3.VanityScalingOptions, "ReadDesignPackage should return same vanity options as ReadAllDesignPackages")
}

// TestRelationalDb_Integration_PanoramicImagesForDesignPackages tests the PanoramicImagesForDesignPackages method
// against a live database to ensure it correctly retrieves panoramic image URLs for design packages.
func TestRelationalDb_Integration_PanoramicImagesForDesignPackages(t *testing.T) {
	// Arrange
	ctx := context.Background()
	db := gateways.NewRelationalDb(testPool, nil)

	// Ensure the database is clean before this test runs.
	_, err := testPool.Exec(ctx, "TRUNCATE TABLE design_pkg.packages CASCADE;")
	require.NoError(t, err, "Failed to truncate packages table before test")
	_, err = testPool.Exec(ctx, "TRUNCATE TABLE design.room_designs CASCADE;")
	require.NoError(t, err, "Failed to truncate room_designs table before test")

	// Create test design packages
	designPkg1 := usecases.DesignPackage{
		Tagged: usecases.Tagged{
			ColorScheme: usecases.Neutral,
			Style:       usecases.Modern,
		},
		Name:            "Panoramic Test Design Package 1",
		Description:     "Design package for panoramic image testing",
		ImageURL:        url.URL{Scheme: "https", Host: "example.com", Path: "/pano-designPkg1.jpg"},
		Inspiration:     "Modern design with panoramic view",
		Atmosphere:      []string{"spacious"},
		ColorPalette:    []string{"white"},
		MaterialPalette: []string{"marble"},
		HighlightedBrandUrls: []url.URL{
			{Scheme: "https", Host: "example.com", Path: "/pano-highlight1.jpg"},
		},
		FixedProductSelections: usecases.FixedProductSelections{
			FloorTile:       ptr(uuid.New()),
			Lighting:        ptr(uuid.New()),
			Mirror:          ptr(uuid.New()),
			Paint:           ptr(uuid.New()),
			Shelving:        ptr(uuid.New()),
			Toilet:          ptr(uuid.New()),
			ShowerFloorTile: ptr(uuid.New()),
			ShowerWallTile:  ptr(uuid.New()),
			TubFiller:       ptr(uuid.New()),
			WallTile:        ptr(uuid.New()),
		},
		ProductSelectionOptions: usecases.ProductSelectionOptions{
			AlcoveTub:          uuid.New(),
			FreestandingTub:    uuid.New(),
			ShowerGlassFixed:   uuid.New(),
			ShowerGlassSliding: uuid.New(),
		},
		WallTilePlacement:  usecases.HalfWall,
		WallpaperPlacement: usecases.NoWallpaper,
	}

	designPkg2 := usecases.DesignPackage{
		Tagged: usecases.Tagged{
			ColorScheme: usecases.Bold,
			Style:       usecases.Traditional,
		},
		Name:            "Panoramic Test Design Package 2",
		Description:     "Another design package for panoramic image testing",
		ImageURL:        url.URL{Scheme: "https", Host: "example.com", Path: "/pano-designPkg2.jpg"},
		Inspiration:     "Traditional design with panoramic view",
		Atmosphere:      []string{"cozy"},
		ColorPalette:    []string{"beige"},
		MaterialPalette: []string{"wood"},
		HighlightedBrandUrls: []url.URL{
			{Scheme: "https", Host: "example.com", Path: "/pano-highlight2.jpg"},
		},
		FixedProductSelections: usecases.FixedProductSelections{
			FloorTile:       ptr(uuid.New()),
			Lighting:        ptr(uuid.New()),
			Mirror:          ptr(uuid.New()),
			Paint:           ptr(uuid.New()),
			Shelving:        ptr(uuid.New()),
			Toilet:          ptr(uuid.New()),
			ShowerFloorTile: ptr(uuid.New()),
			ShowerWallTile:  ptr(uuid.New()),
			TubFiller:       ptr(uuid.New()),
			WallTile:        ptr(uuid.New()),
		},
		ProductSelectionOptions: usecases.ProductSelectionOptions{
			AlcoveTub:          uuid.New(),
			FreestandingTub:    uuid.New(),
			ShowerGlassFixed:   uuid.New(),
			ShowerGlassSliding: uuid.New(),
		},
		WallTilePlacement:  usecases.FullWall,
		WallpaperPlacement: usecases.VanityWall,
	}

	// Design Package without panoramic image (no preset/rendition)
	designPkg3 := usecases.DesignPackage{
		Tagged: usecases.Tagged{
			ColorScheme: usecases.Neutral,
			Style:       usecases.Transitional,
		},
		Name:            "Design Package Without Panoramic",
		Description:     "Design package without panoramic image",
		ImageURL:        url.URL{Scheme: "https", Host: "example.com", Path: "/no-pano-design-package.jpg"},
		Inspiration:     "Transitional design",
		Atmosphere:      []string{"balanced"},
		ColorPalette:    []string{"gray"},
		MaterialPalette: []string{"stone"},
		HighlightedBrandUrls: []url.URL{
			{Scheme: "https", Host: "example.com", Path: "/no-pano-highlight.jpg"},
		},
		FixedProductSelections: usecases.FixedProductSelections{
			FloorTile:       ptr(uuid.New()),
			Lighting:        ptr(uuid.New()),
			Mirror:          ptr(uuid.New()),
			Paint:           ptr(uuid.New()),
			Shelving:        ptr(uuid.New()),
			Toilet:          ptr(uuid.New()),
			ShowerFloorTile: ptr(uuid.New()),
			ShowerWallTile:  ptr(uuid.New()),
			TubFiller:       ptr(uuid.New()),
			WallTile:        ptr(uuid.New()),
		},
		ProductSelectionOptions: usecases.ProductSelectionOptions{
			AlcoveTub:          uuid.New(),
			FreestandingTub:    uuid.New(),
			ShowerGlassFixed:   uuid.New(),
			ShowerGlassSliding: uuid.New(),
		},
		WallTilePlacement:  usecases.HalfWall,
		WallpaperPlacement: usecases.AllWalls,
	}

	// Insert design packages
	designPkgID1, err := db.InsertDesignPackage(ctx, designPkg1, "P1")
	require.NoError(t, err, "Failed to insert design package 1")

	designPkgID2, err := db.InsertDesignPackage(ctx, designPkg2, "P2")
	require.NoError(t, err, "Failed to insert design package 2")

	designPkgID3, err := db.InsertDesignPackage(ctx, designPkg3, "P3")
	require.NoError(t, err, "Failed to insert design package 3")

	// Create presets with panoramic images for design packages 1 and 2
	panoramicURL1 := url.URL{Scheme: "https", Host: "panoramic.example.com", Path: "/designPkg1-panoramic.webp"}
	panoramicURL2 := url.URL{Scheme: "https", Host: "panoramic.example.com", Path: "/designPkg2-panoramic.webp"}

	preset1 := usecases.Preset{
		Id:          "P1",
		DesignPkgId: designPkgID1,
		Design: usecases.Design{
			ID:                 uuid.New(),
			ProjectID:          "PRJ-PANORAMIC-1",
			Status:             usecases.Preview,
			WallpaperPlacement: usecases.NoWallpaper,
			WallTilePlacement:  usecases.HalfWall,
			DesignOptions: usecases.DesignOptions{
				Title:            sql.NullString{String: "Panoramic Design 1", Valid: true},
				ColorScheme:      ptr(usecases.Neutral),
				Style:            ptr(usecases.Modern),
				FloorTilePattern: ptr(usecases.Herringbone),
				FixedProductSelections: usecases.FixedProductSelections{
					FloorTile: ptr(uuid.New()),
					Lighting:  ptr(uuid.New()),
					Mirror:    ptr(uuid.New()),
					Paint:     ptr(uuid.New()),
					Toilet:    ptr(uuid.New()),
				},
				Faucet: ptr(uuid.New()),
				Vanity: ptr(uuid.New()),
			},
		},
		Rendition: entities.Rendition{
			Id:     uuid.New(),
			Status: entities.RenditionCompleted,
			URL:    &panoramicURL1,
		},
	}

	preset2 := usecases.Preset{
		Id:          "P2",
		DesignPkgId: designPkgID2,
		Design: usecases.Design{
			ID:                 uuid.New(),
			ProjectID:          "PRJ-PANORAMIC-2",
			Status:             usecases.Preview,
			WallpaperPlacement: usecases.VanityWall,
			WallTilePlacement:  usecases.FullWall,
			DesignOptions: usecases.DesignOptions{
				Title:            sql.NullString{String: "Panoramic Design 2", Valid: true},
				ColorScheme:      ptr(usecases.Bold),
				Style:            ptr(usecases.Traditional),
				FloorTilePattern: ptr(usecases.VerticalStacked),
				FixedProductSelections: usecases.FixedProductSelections{
					FloorTile: ptr(uuid.New()),
					Lighting:  ptr(uuid.New()),
					Mirror:    ptr(uuid.New()),
					Paint:     ptr(uuid.New()),
					Toilet:    ptr(uuid.New()),
				},
				Faucet: ptr(uuid.New()),
				Vanity: ptr(uuid.New()),
			},
		},
		Rendition: entities.Rendition{
			Id:     uuid.New(),
			Status: entities.RenditionCompleted,
			URL:    &panoramicURL2,
		},
	}

	// Insert presets (this creates the legacy_lookup entries and renditions)
	err = db.InsertPreset(ctx, preset1)
	require.NoError(t, err, "Failed to insert preset 1")

	err = db.InsertPreset(ctx, preset2)
	require.NoError(t, err, "Failed to insert preset 2")

	// Act - Get panoramic images for all design packages
	allDesignPkgIDs := []uuid.UUID{designPkgID1, designPkgID2, designPkgID3}
	panoramicImages, err := db.PanoramicImagesForDesignPackages(ctx, allDesignPkgIDs)
	require.NoError(t, err, "PanoramicImagesForDesignPackages should succeed")

	// Assert - Verify the results
	assert.Len(t, panoramicImages, 2, "Should have panoramic images for 2 design packages")

	// Verify design package 1 has correct panoramic image
	assert.Contains(t, panoramicImages, designPkgID1, "Should have panoramic image for design package 1")
	panoURL1 := panoramicImages[designPkgID1]
	assert.Equal(t, panoramicURL1.String(), (&panoURL1).String(), "Design Package 1 panoramic URL should match")

	// Verify design package 2 has correct panoramic image
	assert.Contains(t, panoramicImages, designPkgID2, "Should have panoramic image for design package 2")
	panoURL2 := panoramicImages[designPkgID2]
	assert.Equal(t, panoramicURL2.String(), (&panoURL2).String(), "Design Package 2 panoramic URL should match")

	// Verify design package 3 has no panoramic image
	assert.NotContains(t, panoramicImages, designPkgID3, "Should not have panoramic image for design package 3")

	// Test with subset of design package IDs
	subsetDesignPkgIDs := []uuid.UUID{designPkgID1}
	subsetPanoramicImages, err := db.PanoramicImagesForDesignPackages(ctx, subsetDesignPkgIDs)
	require.NoError(t, err, "PanoramicImagesForDesignPackages with subset should succeed")
	assert.Len(t, subsetPanoramicImages, 1, "Should have panoramic image for 1 design package")
	assert.Contains(t, subsetPanoramicImages, designPkgID1, "Should have panoramic image for design package 1")
	subsetPanoURL1 := subsetPanoramicImages[designPkgID1]
	assert.Equal(t, panoramicURL1.String(), (&subsetPanoURL1).String(), "Design Package 1 panoramic URL should match")

	// Test with empty design package IDs list
	emptyPanoramicImages, err := db.PanoramicImagesForDesignPackages(ctx, []uuid.UUID{})
	require.NoError(t, err, "PanoramicImagesForDesignPackages with empty list should succeed")
	assert.Len(t, emptyPanoramicImages, 0, "Should return empty map for empty input")

	// Test with non-existent design package IDs
	nonExistentDesignPkgIDs := []uuid.UUID{uuid.New(), uuid.New()}
	nonExistentPanoramicImages, err := db.PanoramicImagesForDesignPackages(ctx, nonExistentDesignPkgIDs)
	require.NoError(t, err, "PanoramicImagesForDesignPackages with non-existent IDs should succeed")
	assert.Len(t, nonExistentPanoramicImages, 0, "Should return empty map for non-existent design package IDs")
}
