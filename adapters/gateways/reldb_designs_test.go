//go:build integration

package gateways_test

import (
	"context"
	"database/sql"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// TestRelationalDb_Integration_Designs_CRUD performs a full create, read, update, and delete
// cycle against a live database to ensure the entire lifecycle works as expected.
func TestRelationalDb_Integration_Designs_CRUD(t *testing.T) {
	// Arrange
	ctx := context.Background()
	db := gateways.NewRelationalDb(testPool, nil)

	// Ensure the database is clean before this test runs.
	// CASCADE is crucial to also truncate tables with foreign keys.
	_, err := testPool.Exec(ctx, "TRUNCATE TABLE design.room_designs CASCADE;")
	require.NoError(t, err, "Failed to truncate tables before test")

	testDesign := usecases.Design{
		ProjectID:          "PRJ-TEST123",
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.NoWallpaper,
		WallTilePlacement:  usecases.HalfWall,
		DesignOptions: usecases.DesignOptions{
			Title:            sql.NullString{String: "Integration Test", Valid: true},
			Description:      sql.NullString{Valid: false},
			ColorScheme:      ptr(usecases.Neutral),
			Style:            ptr(usecases.Modern),
			FloorTilePattern: ptr(usecases.Herringbone),
			FixedProductSelections: usecases.FixedProductSelections{
				FloorTile:      ptr(uuid.New()),
				Lighting:       ptr(uuid.New()),
				Mirror:         ptr(uuid.New()),
				Paint:          ptr(uuid.New()),
				ShowerWallTile: &uuid.Nil,
				Toilet:         ptr(uuid.New()),
			},
			Faucet:            ptr(uuid.New()),
			ShowerGlass:       &uuid.Nil,
			TubDoor:           &uuid.Nil,
			Vanity:            ptr(uuid.New()),
			NumSKUs:           sql.NullInt32{Int32: 10, Valid: true},
			TotalPriceInCents: sql.NullInt32{Int32: 1000, Valid: true},
			LeadTimeDays:      sql.NullInt32{Int32: 30, Valid: true},
		},
	}
	*testDesign.ShowerWallTile = uuid.New()
	*testDesign.ShowerGlass = uuid.New()
	*testDesign.TubDoor = uuid.New()

	// --- 1. Test Create ---
	createdID, err := db.UpsertDesign(ctx, testDesign)
	require.NoError(t, err, "Create failed")
	require.NotEqual(t, uuid.Nil, createdID, "Create should return a valid UUID")

	// --- 2. Test Read ---
	readDesign, err := db.ReadDesign(ctx, createdID)
	require.NoError(t, err, "Read failed after create")
	require.NotNil(t, readDesign)
	require.NotNil(t, readDesign.Created)
	require.NotEmpty(t, readDesign.ID)

	assert.Equal(t, createdID, readDesign.ID)
	assert.Equal(t, testDesign.ProjectID, readDesign.ProjectID)
	assert.Equal(t, testDesign.Title.String, readDesign.Title.String)
	assert.Equal(t, usecases.Herringbone, *readDesign.FloorTilePattern)
	assert.Equal(t, usecases.Modern, *readDesign.Style)
	assert.Equal(t, usecases.Neutral, *readDesign.ColorScheme)
	assert.Equal(t, usecases.Preview, readDesign.Status)
	assert.Equal(t, readDesign.Created, readDesign.LastUpdated)
	assert.Equal(t, testDesign.NumSKUs.Int32, readDesign.NumSKUs.Int32)
	assert.Equal(t, testDesign.TotalPriceInCents.Int32, readDesign.TotalPriceInCents.Int32)
	assert.Equal(t, testDesign.LeadTimeDays.Int32, readDesign.LeadTimeDays.Int32)

	// --- 3. Test Update ---
	readDesign.Title = sql.NullString{String: "Updated Title", Valid: true}
	readDesign.FloorTilePattern = ptr(usecases.VerticalStacked)
	readDesign.Style = ptr(usecases.Traditional)
	readDesign.ColorScheme = ptr(usecases.ColorScheme(""))
	readDesign.Status = usecases.Fave
	readDesign.TubDoorVisible = true
	readDesign.NichesVisible = true

	_, err = db.UpsertDesign(ctx, readDesign)
	require.NoError(t, err, "Update failed")

	// --- 4. Read again to verify update ---
	updatedDesign, err := db.ReadDesign(ctx, createdID)
	require.NoError(t, err, "Read failed after update")
	require.NotNil(t, updatedDesign)

	assert.Equal(t, "Updated Title", updatedDesign.Title.String)
	assert.Equal(t, usecases.VerticalStacked, *updatedDesign.FloorTilePattern)
	assert.Equal(t, usecases.Traditional, *updatedDesign.Style)
	assert.Equal(t, *readDesign.ColorScheme, *updatedDesign.ColorScheme)
	assert.Equal(t, usecases.Fave, updatedDesign.Status)
	assert.True(t, updatedDesign.TubDoorVisible)
	assert.True(t, updatedDesign.NichesVisible)
	assert.Greater(t, updatedDesign.LastUpdated, updatedDesign.Created)
	assert.Greater(t, updatedDesign.LastUpdated, readDesign.LastUpdated)

	// --- 5. Test DesignsForProject ---
	// Create another design for the same project
	_, err = db.UpsertDesign(ctx, usecases.Design{
		ProjectID:          "PRJ-TEST123",
		Status:             usecases.Fave,
		WallpaperPlacement: usecases.AllWalls,
		WallTilePlacement:  usecases.HalfWall,
		DesignOptions: usecases.DesignOptions{
			FloorTilePattern: ptr(usecases.ThirdOffset),
			FixedProductSelections: usecases.FixedProductSelections{
				FloorTile: ptr(uuid.New()),
				Toilet:    ptr(uuid.New()),
				Mirror:    ptr(uuid.New()),
				Lighting:  ptr(uuid.New()),
				Paint:     ptr(uuid.New()),
			},
			Vanity: ptr(uuid.New()),
			Faucet: ptr(uuid.New()),
		},
	})
	require.NoError(t, err, "Create failed for second design")

	// Fetch both designs for the original project
	designs, err := db.DesignsForProject(ctx, "PRJ-TEST123")
	require.NoError(t, err, "Fetch designs failed")
	assert.Len(t, designs, 2, "Expected 2 designs for the project")

	// --- 6. Test DesignsByProject ---
	// Create another designs for a different project
	_, err = db.UpsertDesign(ctx, usecases.Design{
		ProjectID:          "PRJ-TEST456",
		WallpaperPlacement: usecases.VanityWall,
		WallTilePlacement:  usecases.FullWall,
		DesignOptions: usecases.DesignOptions{
			FixedProductSelections: usecases.FixedProductSelections{
				FloorTile: ptr(uuid.New()),
				Toilet:    ptr(uuid.New()),
				Mirror:    ptr(uuid.New()),
				Lighting:  ptr(uuid.New()),
				Paint:     ptr(uuid.New()),
			},
			FloorTilePattern: ptr(usecases.VerticalStacked),
			Vanity:           ptr(uuid.New()),
			Faucet:           ptr(uuid.New()),
		},
	})
	require.NoError(t, err, "Create failed for third design")

	// Fetch all designs for both projects
	projectDesigns, errors, err := db.DesignsByProject(ctx, []entities.ProjectId{"PRJ-TEST123", "PRJ-TEST456"})
	require.NoError(t, err, "Fetch designs by project failed")
	assert.Len(t, errors, 0, "Expected no errors")
	assert.Len(t, projectDesigns, 2, "Expected designs for 2 projects")

	// --- 7. Test Delete ---
	err = db.DeleteDesign(ctx, createdID)
	require.NoError(t, err, "Delete failed")
}

// TestRelationalDb_Integration_DeleteDesignsForProjectExceptSpecified tests the
// DeleteDesignsForProjectExceptSpecified method against a live database to ensure
// it correctly deletes designs while preserving specified ones.
func TestRelationalDb_Integration_DeleteDesignsForProjectExceptSpecified(t *testing.T) {
	// Arrange
	ctx := context.Background()
	db := gateways.NewRelationalDb(testPool, nil)

	// Ensure the database is clean before this test runs.
	_, err := testPool.Exec(ctx, "TRUNCATE TABLE design.room_designs CASCADE;")
	require.NoError(t, err, "Failed to truncate tables before test")

	projectID := entities.NewProjectId("PRJ-DELETE-TEST")

	// Create multiple designs for the same project
	design1 := usecases.Design{
		ProjectID:          projectID,
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.NoWallpaper,
		WallTilePlacement:  usecases.HalfWall,
		DesignOptions: usecases.DesignOptions{
			Title:            sql.NullString{String: "Design 1", Valid: true},
			ColorScheme:      ptr(usecases.Neutral),
			Style:            ptr(usecases.Modern),
			FloorTilePattern: ptr(usecases.Herringbone),
			FixedProductSelections: usecases.FixedProductSelections{
				FloorTile: ptr(uuid.New()),
				Toilet:    ptr(uuid.New()),
			},
		},
	}

	design2 := usecases.Design{
		ProjectID:          projectID,
		Status:             usecases.Fave,
		WallpaperPlacement: usecases.VanityWall,
		WallTilePlacement:  usecases.FullWall,
		DesignOptions: usecases.DesignOptions{
			Title:            sql.NullString{String: "Design 2", Valid: true},
			ColorScheme:      ptr(usecases.Bold),
			Style:            ptr(usecases.Traditional),
			FloorTilePattern: ptr(usecases.VerticalStacked),
			FixedProductSelections: usecases.FixedProductSelections{
				FloorTile: ptr(uuid.New()),
				Toilet:    ptr(uuid.New()),
			},
		},
	}

	design3 := usecases.Design{
		ProjectID:          projectID,
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.AllWalls,
		WallTilePlacement:  usecases.HalfWall,
		DesignOptions: usecases.DesignOptions{
			Title:            sql.NullString{String: "Design 3", Valid: true},
			ColorScheme:      ptr(usecases.Neutral),
			Style:            ptr(usecases.Transitional),
			FloorTilePattern: ptr(usecases.ThirdOffset),
			FixedProductSelections: usecases.FixedProductSelections{
				FloorTile: ptr(uuid.New()),
				Toilet:    ptr(uuid.New()),
			},
		},
	}

	// Create a design for a different project to ensure it's not affected
	designOtherProject := usecases.Design{
		ProjectID:          entities.NewProjectId("PRJ-OTHER"),
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.NoWallpaper,
		WallTilePlacement:  usecases.HalfWall,
		DesignOptions: usecases.DesignOptions{
			Title:            sql.NullString{String: "Other Project Design", Valid: true},
			ColorScheme:      ptr(usecases.Neutral),
			Style:            ptr(usecases.Modern),
			FloorTilePattern: ptr(usecases.Herringbone),
			FixedProductSelections: usecases.FixedProductSelections{
				FloorTile: ptr(uuid.New()),
				Toilet:    ptr(uuid.New()),
			},
		},
	}

	// Create all designs
	id1, err := db.UpsertDesign(ctx, design1)
	require.NoError(t, err, "Failed to create design 1")
	require.NotEqual(t, uuid.Nil, id1)

	id2, err := db.UpsertDesign(ctx, design2)
	require.NoError(t, err, "Failed to create design 2")
	require.NotEqual(t, uuid.Nil, id2)

	id3, err := db.UpsertDesign(ctx, design3)
	require.NoError(t, err, "Failed to create design 3")
	require.NotEqual(t, uuid.Nil, id3)

	idOther, err := db.UpsertDesign(ctx, designOtherProject)
	require.NoError(t, err, "Failed to create other project design")
	require.NotEqual(t, uuid.Nil, idOther)

	// Verify all designs were created
	allDesigns, err := db.DesignsForProject(ctx, projectID)
	require.NoError(t, err, "Failed to fetch designs for project")
	assert.Len(t, allDesigns, 3, "Should have 3 designs for the project")

	otherDesigns, err := db.DesignsForProject(ctx, entities.NewProjectId("PRJ-OTHER"))
	require.NoError(t, err, "Failed to fetch designs for other project")
	assert.Len(t, otherDesigns, 1, "Should have 1 design for the other project")

	// --- Test 1: Delete all except specified designs ---
	designsToKeep := []uuid.UUID{id1, id3} // Keep design 1 and 3, delete design 2

	err = db.DeleteDesignsForProjectExceptSpecified(ctx, projectID, designsToKeep)
	require.NoError(t, err, "DeleteDesignsForProjectExceptSpecified should succeed")

	// Verify only the specified designs remain
	remainingDesigns, err := db.DesignsForProject(ctx, projectID)
	require.NoError(t, err, "Failed to fetch remaining designs")
	assert.Len(t, remainingDesigns, 2, "Should have 2 remaining designs")

	remainingIDs := make([]uuid.UUID, len(remainingDesigns))
	for i, design := range remainingDesigns {
		remainingIDs[i] = design.ID
	}
	assert.Contains(t, remainingIDs, id1, "Design 1 should remain")
	assert.Contains(t, remainingIDs, id3, "Design 3 should remain")
	assert.NotContains(t, remainingIDs, id2, "Design 2 should be deleted")

	// Verify the other project's design is unaffected
	otherDesignsAfter, err := db.DesignsForProject(ctx, entities.NewProjectId("PRJ-OTHER"))
	require.NoError(t, err, "Failed to fetch designs for other project after deletion")
	assert.Len(t, otherDesignsAfter, 1, "Other project should still have 1 design")
	assert.Equal(t, idOther, otherDesignsAfter[0].ID, "Other project design should be unchanged")

	// --- Test 2: Delete all designs (empty keep list) ---
	err = db.DeleteDesignsForProjectExceptSpecified(ctx, projectID, []uuid.UUID{})
	require.NoError(t, err, "DeleteDesignsForProjectExceptSpecified with empty list should succeed")

	// Verify all designs are deleted
	finalDesigns, err := db.DesignsForProject(ctx, projectID)
	require.NoError(t, err, "Failed to fetch final designs")
	assert.Len(t, finalDesigns, 0, "Should have no remaining designs")

	// --- Test 3: Delete from non-existent project (should not error) ---
	err = db.DeleteDesignsForProjectExceptSpecified(ctx, entities.NewProjectId("PRJ-NONEXISTENT"), []uuid.UUID{uuid.New()})
	require.NoError(t, err, "DeleteDesignsForProjectExceptSpecified for non-existent project should not error")

	// --- Test 4: Keep non-existent design IDs (should not error) ---
	// Create one more design to test with
	finalDesign := usecases.Design{
		ProjectID:          projectID,
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.NoWallpaper,
		WallTilePlacement:  usecases.HalfWall,
		DesignOptions: usecases.DesignOptions{
			Title: sql.NullString{String: "Final Design", Valid: true},
			FixedProductSelections: usecases.FixedProductSelections{
				FloorTile: ptr(uuid.New()),
				Toilet:    ptr(uuid.New()),
			},
		},
	}

	finalID, err := db.UpsertDesign(ctx, finalDesign)
	require.NoError(t, err, "Failed to create final design")

	// Try to keep both the real design and a non-existent one
	nonExistentID := uuid.New()
	err = db.DeleteDesignsForProjectExceptSpecified(ctx, projectID, []uuid.UUID{finalID, nonExistentID})
	require.NoError(t, err, "DeleteDesignsForProjectExceptSpecified with non-existent ID should not error")

	// Verify the real design remains
	finalCheck, err := db.DesignsForProject(ctx, projectID)
	require.NoError(t, err, "Failed to fetch designs for final check")
	assert.Len(t, finalCheck, 1, "Should have 1 remaining design")
	assert.Equal(t, finalID, finalCheck[0].ID, "Final design should remain")
}

// TestRelationalDb_Integration_ProjectIdForDesign tests the ProjectIdForDesign method
// against a live database to ensure it correctly retrieves project IDs for designs.
func TestRelationalDb_Integration_ProjectIdForDesign(t *testing.T) {
	// Arrange
	ctx := context.Background()
	db := gateways.NewRelationalDb(testPool, nil)

	// Ensure the database is clean before this test runs.
	_, err := testPool.Exec(ctx, "TRUNCATE TABLE design.room_designs CASCADE;")
	require.NoError(t, err, "Failed to truncate tables before test")

	// --- Test 1: Successfully retrieve project ID for existing design ---
	projectID1 := entities.NewProjectId("PRJ-LOOKUP-TEST-001")
	testDesign1 := usecases.Design{
		ProjectID:          projectID1,
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.NoWallpaper,
		WallTilePlacement:  usecases.HalfWall,
		DesignOptions: usecases.DesignOptions{
			Title:       sql.NullString{String: "Project Lookup Test Design 1", Valid: true},
			Description: sql.NullString{String: "Test design for project ID lookup", Valid: true},
			FixedProductSelections: usecases.FixedProductSelections{
				FloorTile: ptr(uuid.New()),
				Toilet:    ptr(uuid.New()),
			},
		},
	}

	designID1, err := db.UpsertDesign(ctx, testDesign1)
	require.NoError(t, err, "Failed to create test design 1")

	// Test retrieving the project ID
	retrievedProjectID, err := db.ProjectIdForDesign(ctx, designID1)
	require.NoError(t, err, "Failed to retrieve project ID for design")
	assert.Equal(t, projectID1, retrievedProjectID, "Retrieved project ID should match the original")

	// --- Test 2: Test with different project ID ---
	projectID2 := entities.NewProjectId("PRJ-LOOKUP-TEST-002")
	testDesign2 := usecases.Design{
		ProjectID:          projectID2,
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.NoWallpaper,
		WallTilePlacement:  usecases.HalfWall,
		DesignOptions: usecases.DesignOptions{
			Title:       sql.NullString{String: "Project Lookup Test Design 2", Valid: true},
			Description: sql.NullString{String: "Second test design for project ID lookup", Valid: true},
			FixedProductSelections: usecases.FixedProductSelections{
				FloorTile: ptr(uuid.New()),
				Toilet:    ptr(uuid.New()),
			},
		},
	}

	designID2, err := db.UpsertDesign(ctx, testDesign2)
	require.NoError(t, err, "Failed to create test design 2")

	// Test retrieving the second project ID
	retrievedProjectID2, err := db.ProjectIdForDesign(ctx, designID2)
	require.NoError(t, err, "Failed to retrieve project ID for design 2")
	assert.Equal(t, projectID2, retrievedProjectID2, "Retrieved project ID should match the second project")

	// Verify the two designs have different project IDs
	assert.NotEqual(t, retrievedProjectID, retrievedProjectID2, "The two designs should have different project IDs")

	// --- Test 3: Test error handling for non-existent design ---
	nonExistentDesignID := uuid.New()
	_, err = db.ProjectIdForDesign(ctx, nonExistentDesignID)
	require.Error(t, err, "Should return error for non-existent design")
	assert.Equal(t, usecases.ErrNotFound, err, "Should return ErrNotFound for non-existent design")

	// --- Test 4: Test with multiple designs in same project ---
	projectID3 := entities.NewProjectId("PRJ-LOOKUP-TEST-003")

	// Create first design in project 3
	testDesign3a := usecases.Design{
		ProjectID:          projectID3,
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.NoWallpaper,
		WallTilePlacement:  usecases.HalfWall,
		DesignOptions: usecases.DesignOptions{
			Title: sql.NullString{String: "Project 3 Design A", Valid: true},
			FixedProductSelections: usecases.FixedProductSelections{
				FloorTile: ptr(uuid.New()),
				Toilet:    ptr(uuid.New()),
			},
		},
	}

	designID3a, err := db.UpsertDesign(ctx, testDesign3a)
	require.NoError(t, err, "Failed to create test design 3a")

	// Create second design in same project
	testDesign3b := usecases.Design{
		ProjectID:          projectID3,
		Status:             usecases.Preview,
		WallpaperPlacement: usecases.NoWallpaper,
		WallTilePlacement:  usecases.HalfWall,
		DesignOptions: usecases.DesignOptions{
			Title: sql.NullString{String: "Project 3 Design B", Valid: true},
			FixedProductSelections: usecases.FixedProductSelections{
				FloorTile: ptr(uuid.New()),
				Toilet:    ptr(uuid.New()),
			},
		},
	}

	designID3b, err := db.UpsertDesign(ctx, testDesign3b)
	require.NoError(t, err, "Failed to create test design 3b")

	// Both designs should return the same project ID
	retrievedProjectID3a, err := db.ProjectIdForDesign(ctx, designID3a)
	require.NoError(t, err, "Failed to retrieve project ID for design 3a")
	assert.Equal(t, projectID3, retrievedProjectID3a, "Design 3a should return correct project ID")

	retrievedProjectID3b, err := db.ProjectIdForDesign(ctx, designID3b)
	require.NoError(t, err, "Failed to retrieve project ID for design 3b")
	assert.Equal(t, projectID3, retrievedProjectID3b, "Design 3b should return correct project ID")

	assert.Equal(t, retrievedProjectID3a, retrievedProjectID3b, "Both designs in same project should return same project ID")
}
