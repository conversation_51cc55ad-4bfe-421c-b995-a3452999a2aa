package gateways

import (
	"context"
	"database/sql"
	"errors"
	"fmt"
	"log"
	"log/slog"
	"net/url"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"

	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// InsertDesignPackage persists a DesignPackage entity to the database.
func (r *RelationalDb) InsertDesignPackage(ctx context.Context,
	designPkg usecases.DesignPackage, legacyId string) (uuid.UUID, error) {

	var zeroUUID uuid.UUID
	if designPkg.ID == zeroUUID {
		designPkg.ID = uuid.New()
		r.logger.InfoContext(ctx, "Generated new ID for design package",
			slog.String("designPkgId", designPkg.ID.String()))
	}

	tx, err := r.db.Begin(ctx)
	if err != nil {
		return zeroUUID, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer func() {
		if err != nil {
			if err := tx.Rollback(ctx); err != nil {
				r.logger.ErrorContext(ctx, "Failed to rollback transaction",
					slog.String("error", err.Error()))
			}
		}
	}()

	designPkgQuery := `
		INSERT INTO design_pkg.packages (
			id, color_scheme, style, name, description, image_url, inspiration,
			atmosphere, color_palette, material_palette, highlighted_brand_urls
		) VALUES (
			@id, @color_scheme, @style, @name, @description, @image_url, @inspiration,
			@atmosphere, @color_palette, @material_palette, @highlighted_brand_urls
		)`

	brandUrls := make([]string, len(designPkg.HighlightedBrandUrls))
	for i, url := range designPkg.HighlightedBrandUrls {
		brandUrls[i] = url.String()
	}

	_, err = tx.Exec(ctx, designPkgQuery, pgx.NamedArgs{
		"id":                     designPkg.ID,
		"color_scheme":           designPkg.ColorScheme,
		"style":                  designPkg.Style,
		"name":                   designPkg.Name,
		"description":            designPkg.Description,
		"image_url":              designPkg.ImageURL.String(),
		"inspiration":            designPkg.Inspiration,
		"atmosphere":             designPkg.Atmosphere,
		"color_palette":          designPkg.ColorPalette,
		"material_palette":       designPkg.MaterialPalette,
		"highlighted_brand_urls": brandUrls,
	})
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to insert into design_pkg.packages", slog.String("error", err.Error()))
		return zeroUUID, fmt.Errorf("unable to create design package: %w", err)
	}

	productSelectionsQuery := `
		INSERT INTO design_pkg.product_selections (
			design_pkg_id, floor_tile, lighting, mirror, paint, shelving, toilet,
			shower_floor_tile, shower_wall_tile, tub_filler, wall_tile_placement,
			wall_tile, wallpaper_placement, wallpaper
		) VALUES (
			@design_pkg_id, @floor_tile, @lighting, @mirror, @paint, @shelving, @toilet,
			@shower_floor_tile, @shower_wall_tile, @tub_filler, @wall_tile_placement,
			@wall_tile, @wallpaper_placement, @wallpaper
		)`

	_, err = tx.Exec(ctx, productSelectionsQuery, pgx.NamedArgs{
		"design_pkg_id":       designPkg.ID,
		"floor_tile":          designPkg.FloorTile,
		"lighting":            designPkg.Lighting,
		"mirror":              designPkg.Mirror,
		"paint":               designPkg.Paint,
		"shelving":            designPkg.Shelving,
		"toilet":              designPkg.Toilet,
		"shower_floor_tile":   designPkg.ShowerFloorTile,
		"shower_wall_tile":    designPkg.ShowerWallTile,
		"tub_filler":          designPkg.TubFiller,
		"wall_tile_placement": designPkg.WallTilePlacement,
		"wall_tile":           designPkg.WallTile,
		"wallpaper_placement": designPkg.WallpaperPlacement,
		"wallpaper":           designPkg.Wallpaper,
	})
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to insert into design_pkg.product_selections",
			slog.String("error", err.Error()), slog.String("designPkgId", designPkg.ID.String()))
		return zeroUUID, fmt.Errorf("could not save design package product selections: %w", err)
	}

	optionsQuery := `
		INSERT INTO design_pkg.options (
			design_pkg_id, alcove_tub, freestanding_tub, shower_glass_fixed,
			shower_glass_sliding, shower_system_full, shower_system_shower,
			tub_door_fixed, tub_door_sliding
		) VALUES (
			@design_pkg_id, @alcove_tub, @freestanding_tub, @shower_glass_fixed,
			@shower_glass_sliding, @shower_system_full, @shower_system_shower,
			@tub_door_fixed, @tub_door_sliding
		)`

	_, err = tx.Exec(ctx, optionsQuery, pgx.NamedArgs{
		"design_pkg_id":        designPkg.ID,
		"alcove_tub":           designPkg.AlcoveTub,
		"freestanding_tub":     designPkg.FreestandingTub,
		"shower_glass_fixed":   designPkg.ShowerGlassFixed,
		"shower_glass_sliding": designPkg.ShowerGlassSliding,
		"shower_system_full":   designPkg.ShowerSystemCombo,
		"shower_system_shower": designPkg.ShowerSystemSolo,
		"tub_door_fixed":       designPkg.TubDoorFixed,
		"tub_door_sliding":     designPkg.TubDoorSliding,
	})
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to insert into design_pkg.options",
			slog.String("error", err.Error()), slog.String("designPkgId", designPkg.ID.String()))
		return zeroUUID, fmt.Errorf("could not save design package options: %w", err)
	}

	if designPkg.LightingBrand != nil || designPkg.PlumbingBrand != nil || designPkg.ToiletBrand != nil || designPkg.VanityBrand != nil || designPkg.VanityStorage != nil {
		provenanceQuery := `
			INSERT INTO design_pkg.provenance (
				design_pkg_id, lighting_brand, plumbing_brand, toilet_brand,
				vanity_brand, vanity_storage
			) VALUES (
				@design_pkg_id, @lighting_brand, @plumbing_brand, @toilet_brand,
				@vanity_brand, @vanity_storage
			)`

		lightingBrand := stringFromPtr(designPkg.LightingBrand)
		plumbingBrand := stringFromPtr(designPkg.PlumbingBrand)
		toiletBrand := stringFromPtr(designPkg.ToiletBrand)
		vanityBrand := stringFromPtr(designPkg.VanityBrand)
		vanityStorage := stringFromPtr(designPkg.VanityStorage)

		_, err = tx.Exec(ctx, provenanceQuery, pgx.NamedArgs{
			"design_pkg_id":  designPkg.ID,
			"lighting_brand": lightingBrand,
			"plumbing_brand": plumbingBrand,
			"toilet_brand":   toiletBrand,
			"vanity_brand":   vanityBrand,
			"vanity_storage": vanityStorage,
		})
		if err != nil {
			r.logger.ErrorContext(ctx, "Failed to insert into design_pkg.provenance",
				slog.String("error", err.Error()), slog.String("designPkgId", designPkg.ID.String()))
			return zeroUUID, fmt.Errorf("could not save design package provenance: %w", err)
		}
	}

	if len(designPkg.VanityScalingOptions) > 0 {
		vanityScalingQuery := `
			INSERT INTO design_pkg.vanity_scaling_options (
				design_pkg_id, min_vanity_length_inches, vanity_product_id, faucet_product_id
			) VALUES (
				@design_pkg_id, @min_vanity_length_inches, @vanity_product_id, @faucet_product_id
			)`

		for minVanityLengthInches, option := range designPkg.VanityScalingOptions {
			_, err = tx.Exec(ctx, vanityScalingQuery, pgx.NamedArgs{
				"design_pkg_id":            designPkg.ID,
				"min_vanity_length_inches": minVanityLengthInches,
				"vanity_product_id":        option.VanityProductID,
				"faucet_product_id":        option.FaucetProductID,
			})
			if err != nil {
				r.logger.ErrorContext(ctx, "Failed to insert into vanity_scaling_options",
					slog.String("error", err.Error()), slog.String("designPkgId", designPkg.ID.String()))
				return zeroUUID, fmt.Errorf("could not save vanity scaling options: %w", err)
			}
		}
	}

	if legacyId != "" && len(legacyId) == 2 {
		r.logger.InfoContext(ctx, "Inserting legacy ID for design package", slog.String("legacyID", legacyId),
			slog.String("designPkgId", designPkg.ID.String()))
		legacyLookupQuery := `
		INSERT INTO public.legacy_lookup (id, design_pkg_id)
		VALUES (@id, @design_pkg_id)`

		_, err = tx.Exec(ctx, legacyLookupQuery, pgx.NamedArgs{
			"id":            legacyId,
			"design_pkg_id": designPkg.ID,
		})
		if err != nil {
			r.logger.ErrorContext(ctx, "Failed to insert into legacy_lookup",
				slog.String("error", err.Error()), slog.String("designPkgId", designPkg.ID.String()))
			return zeroUUID, fmt.Errorf("could not save legacy lookup ID: %w", err)
		}
	}

	if err = tx.Commit(ctx); err != nil {
		r.logger.ErrorContext(ctx, "Failed to commit transaction",
			slog.String("error", err.Error()), slog.String("designPkgId", designPkg.ID.String()))
		return zeroUUID, fmt.Errorf("failed to commit transaction: %w", err)
	}

	return designPkg.ID, nil
}

// ReadDesignPackage fetches a single DesignPackage entity from the database by its ID.
// It uses a view that joins related tables to construct the DesignPackage object.
func (r *RelationalDb) ReadDesignPackage(ctx context.Context, id uuid.UUID) (usecases.DesignPackage, error) {
	query := designPkgQueryBase + " WHERE id = $1"

	row := r.db.QueryRow(ctx, query, id)
	designPkg, err := scanDesignPackage(row)
	if err != nil {
		return usecases.DesignPackage{}, err
	}

	// Fetch vanity scaling options separately
	designPkg.VanityScalingOptions, err = r.readVanityScalingOptions(ctx, id)
	if err != nil {
		return usecases.DesignPackage{}, err
	}

	return designPkg, nil
}

// ReadDesignPackageByLegacyId fetches a single DesignPackage entity from the database by its legacy ID.
// It joins with the legacy_lookup table to find the design_pkg_id UUID, then fetches the complete design_pkg.packages.
func (r *RelationalDb) ReadDesignPackageByLegacyId(ctx context.Context, legacyId string) (usecases.DesignPackage, error) {
	query := designPkgQueryBase + " WHERE legacy_id = $1"

	row := r.db.QueryRow(ctx, query, legacyId)
	designPkg, err := scanDesignPackage(row)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			r.logger.ErrorContext(ctx, "Failed to find design_pkg.packages by legacy ID", slog.String("legacyID", legacyId))
			return usecases.DesignPackage{}, usecases.ErrNotFound
		}
		r.logger.ErrorContext(ctx, "Failed to design_pkg.packages row",
			slog.String("error", err.Error()), slog.String("legacyID", legacyId))
		return usecases.DesignPackage{}, err
	}

	// Fetch vanity scaling options separately
	designPkg.VanityScalingOptions, err = r.readVanityScalingOptions(ctx, designPkg.ID)
	if err != nil {
		return usecases.DesignPackage{}, err
	}

	return designPkg, nil
}

// ReadAllDesignPackages fetches all DesignPackage entities from the database.
func (r *RelationalDb) ReadAllDesignPackages(ctx context.Context) ([]usecases.DesignPackage, error) {
	query := designPkgQueryBase + " ORDER BY created_at DESC"

	rows, err := r.db.Query(ctx, query)
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to query design_pkg.packages in DB", slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to query design packages: %w", err)
	}
	defer rows.Close()

	var designPkgs []usecases.DesignPackage
	for rows.Next() {
		designPkg, err := scanDesignPackage(rows)
		if err != nil {
			r.logger.ErrorContext(ctx, "Failed to parse design_pkg.packages row", slog.String("error", err.Error()))
			return nil, fmt.Errorf("failed to scan design package: %w", err)
		}
		designPkgs = append(designPkgs, designPkg)
	}

	if err = rows.Err(); err != nil {
		r.logger.ErrorContext(ctx, "Failed to iterate over design_pkg.packages rows", slog.String("error", err.Error()))
		return nil, fmt.Errorf("error iterating design package rows: %w", err)
	}

	// Fetch vanity scaling options for all design packages in a single query
	err = r.populateVanityScalingOptions(ctx, designPkgs)
	if err != nil {
		return nil, err
	}

	return designPkgs, nil
}

// DesignPackagesById fetches multiple DesignPackage entities from the database by their IDs.
func (r *RelationalDb) DesignPackagesById(ctx context.Context, designPkgIDs []uuid.UUID) ([]usecases.DesignPackage, error) {
	if len(designPkgIDs) == 0 {
		return []usecases.DesignPackage{}, nil
	}

	query := designPkgQueryBase + " WHERE id = ANY($1) ORDER BY created_at DESC"

	rows, err := r.db.Query(ctx, query, designPkgIDs)
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to query design_pkg.packages by IDs in DB", slog.String("error", err.Error()))
		return nil, fmt.Errorf("failed to query design packages by IDs: %w", err)
	}
	defer rows.Close()

	var designPkgs []usecases.DesignPackage
	for rows.Next() {
		designPkg, err := scanDesignPackage(rows)
		if err != nil {
			r.logger.ErrorContext(ctx, "Failed to parse design_pkg.packages row", slog.String("error", err.Error()))
			return nil, fmt.Errorf("failed to scan design package: %w", err)
		}
		designPkgs = append(designPkgs, designPkg)
	}

	if err = rows.Err(); err != nil {
		r.logger.ErrorContext(ctx, "Failed to iterate over design_pkg.packages rows", slog.String("error", err.Error()))
		return nil, fmt.Errorf("error iterating design package rows: %w", err)
	}

	// Fetch vanity scaling options for all design packages in a single query
	err = r.populateVanityScalingOptions(ctx, designPkgs)
	if err != nil {
		return nil, err
	}

	return designPkgs, nil
}

// designPkgQueryBase uses the design_pkg.details view to simplify design package retrieval.
// This view consolidates the complex joins across multiple design package-related tables:
// - design_pkg.packages (main design package data)
// - design_pkg.product_selections (product selections)
// - design_pkg.options (design package options like tub types, shower glass)
// - design_pkg.provenance (brand information, optional)
// - public.legacy_lookup (legacy ID mapping, optional)
// Note: vanity_scaling_options are fetched separately using an optimized batch query.
const designPkgQueryBase = ` SELECT
		id, updated_at, color_scheme, style, name, image_url,
		description, inspiration, atmosphere, color_palette,
		material_palette, highlighted_brand_urls,
		floor_tile, lighting, mirror, paint, shelving, toilet,
		shower_floor_tile, shower_wall_tile, tub_filler,
		wall_tile_placement, wall_tile, wallpaper_placement, wallpaper,
		alcove_tub, freestanding_tub, shower_glass_fixed, shower_glass_sliding,
		shower_system_full, shower_system_shower, tub_door_fixed, tub_door_sliding,
		lighting_brand, plumbing_brand, toilet_brand, vanity_brand, vanity_storage,
		legacy_id
	FROM design_pkg.details
`

// scanDesignPackage scans a single row from a pgxScanner (like pgx.Row or pgx.Rows)
// into a DesignPackage entity.
func scanDesignPackage(row pgxScanner) (usecases.DesignPackage, error) {
	var designPkg usecases.DesignPackage
	var description, inspiration sql.NullString
	var brandUrls []string
	var legacyId sql.NullString
	var imageURL sql.NullString

	// Product selections
	var showerFloorTile, showerWallTile, tubFiller, wallTile, wallpaper *uuid.UUID
	var wallTilePlacement usecases.WallTilePlacement
	var wallpaperPlacement usecases.WallpaperPlacement

	var floorTile, lighting, mirror, paint, shelving, toilet *uuid.UUID

	var alcoveTub, freestandingTub, showerGlassFixed, showerGlassSliding uuid.UUID
	var showerSystemCombo, showerSystemSolo, tubDoorFixed, tubDoorSliding uuid.UUID

	var lightingBrand, plumbingBrand, toiletBrand, vanityBrand, vanityStorage sql.NullString

	err := row.Scan(
		&designPkg.ID, &designPkg.UpdatedAt,
		&designPkg.ColorScheme, &designPkg.Style, &designPkg.Name, &imageURL,
		&description, &inspiration, &designPkg.Atmosphere, &designPkg.ColorPalette,
		&designPkg.MaterialPalette, &brandUrls,
		&floorTile, &lighting, &mirror, &paint, &shelving, &toilet,
		&showerFloorTile, &showerWallTile, &tubFiller,
		&wallTilePlacement, &wallTile, &wallpaperPlacement, &wallpaper,
		&alcoveTub, &freestandingTub, &showerGlassFixed, &showerGlassSliding,
		&showerSystemCombo, &showerSystemSolo, &tubDoorFixed, &tubDoorSliding,
		&lightingBrand, &plumbingBrand, &toiletBrand, &vanityBrand, &vanityStorage,
		&legacyId,
	)

	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			log.Printf("Design package not found.")
			return usecases.DesignPackage{}, usecases.ErrNotFound
		}
		return usecases.DesignPackage{}, fmt.Errorf("failed to scan design package row: %w", err)
	}

	if description.Valid {
		designPkg.Description = description.String
	}
	if inspiration.Valid {
		designPkg.Inspiration = inspiration.String
	}
	if legacyId.Valid {
		designPkg.LegacyId = &legacyId.String
	}
	if imageURL.Valid && imageURL.String != "" {
		url, err := url.Parse(imageURL.String)
		if err != nil || url == nil {
			return usecases.DesignPackage{}, fmt.Errorf("failed to parse image URL: %w", err)
		}
		designPkg.ImageURL = *url
	}

	designPkg.HighlightedBrandUrls = make([]url.URL, len(brandUrls))
	for i, urlStr := range brandUrls {
		if parsedURL, err := url.Parse(urlStr); err == nil {
			designPkg.HighlightedBrandUrls[i] = *parsedURL
		}
	}

	designPkg.FixedProductSelections = usecases.FixedProductSelections{
		FloorTile: floorTile,
		Lighting:  lighting,
		Mirror:    mirror,
		Paint:     paint,
		Shelving:  shelving,
		Toilet:    toilet,
	}

	designPkg.ProductSelectionOptions = usecases.ProductSelectionOptions{
		AlcoveTub:          alcoveTub,
		FreestandingTub:    freestandingTub,
		ShowerGlassFixed:   showerGlassFixed,
		ShowerGlassSliding: showerGlassSliding,
		ShowerSystemCombo:  showerSystemCombo,
		ShowerSystemSolo:   showerSystemSolo,
		TubDoorFixed:       tubDoorFixed,
		TubDoorSliding:     tubDoorSliding,
	}

	designPkg.ShowerFloorTile = showerFloorTile
	designPkg.ShowerWallTile = showerWallTile
	designPkg.TubFiller = tubFiller
	designPkg.WallTilePlacement = wallTilePlacement
	designPkg.WallTile = wallTile
	designPkg.WallpaperPlacement = wallpaperPlacement
	designPkg.Wallpaper = wallpaper

	if lightingBrand.Valid {
		designPkg.LightingBrand = &lightingBrand.String
	}
	if plumbingBrand.Valid {
		designPkg.PlumbingBrand = &plumbingBrand.String
	}
	if toiletBrand.Valid {
		designPkg.ToiletBrand = &toiletBrand.String
	}
	if vanityBrand.Valid {
		designPkg.VanityBrand = &vanityBrand.String
	}
	if vanityStorage.Valid {
		designPkg.VanityStorage = &vanityStorage.String
	}

	return designPkg, nil
}

func (r *RelationalDb) readVanityScalingOptions(ctx context.Context,
	designPkgID uuid.UUID) (map[int]usecases.VanityScalingOption, error) {

	vanityQuery := `
		SELECT min_vanity_length_inches, vanity_product_id, faucet_product_id
		FROM design_pkg.vanity_scaling_options
		WHERE design_pkg_id = $1`

	rows, err := r.db.Query(ctx, vanityQuery, designPkgID)
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to query vanity scaling options for design_package",
			slog.String("error", err.Error()), slog.String("designPkgId", designPkgID.String()))
		return nil, fmt.Errorf("failed to query vanity scaling options: %w", err)
	}
	defer rows.Close()

	vanityScalingOptions := make(map[int]usecases.VanityScalingOption)
	for rows.Next() {
		var size int
		var vanityProductID, faucetProductID uuid.UUID
		err := rows.Scan(&size, &vanityProductID, &faucetProductID)
		if err != nil {
			r.logger.ErrorContext(ctx, "Failed to parse vanity scaling option for design_package",
				slog.String("error", err.Error()), slog.String("designPkgId", designPkgID.String()))
			return vanityScalingOptions, fmt.Errorf("failed to scan vanity scaling option: %w", err)
		}
		vanityScalingOptions[size] = usecases.VanityScalingOption{
			VanityProductID: vanityProductID,
			FaucetProductID: faucetProductID,
		}
	}

	if err = rows.Err(); err != nil {
		r.logger.ErrorContext(ctx, "Failed to iterate over vanity scaling options for design_package",
			slog.String("error", err.Error()), slog.String("designPkgId", designPkgID.String()))
		return vanityScalingOptions, fmt.Errorf("error iterating vanity scaling options: %w", err)
	}
	return vanityScalingOptions, nil
}

// populateVanityScalingOptions fetches vanity scaling options for multiple design packages in a single query
// and populates the VanityScalingOptions field for each design package.
func (r *RelationalDb) populateVanityScalingOptions(ctx context.Context, designPkgs []usecases.DesignPackage) error {
	if len(designPkgs) == 0 {
		return nil
	}

	designPkgIDs := make([]uuid.UUID, len(designPkgs))
	for i, designPkg := range designPkgs {
		designPkgIDs[i] = designPkg.ID
	}

	vanityQuery := `
		SELECT design_pkg_id, min_vanity_length_inches, vanity_product_id, faucet_product_id
		FROM design_pkg.vanity_scaling_options
		WHERE design_pkg_id = ANY($1)
		ORDER BY design_pkg_id, min_vanity_length_inches`

	rows, err := r.db.Query(ctx, vanityQuery, designPkgIDs)
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to query vanity scaling options for design_pkg_ids",
			slog.String("error", err.Error()))
		return fmt.Errorf("failed to query vanity scaling options: %w", err)
	}
	defer rows.Close()

	vanityOptionsByDesignPkg := make(map[uuid.UUID]map[int]usecases.VanityScalingOption)
	for rows.Next() {
		var designPkgID uuid.UUID
		var size int
		var vanityProductID, faucetProductID uuid.UUID
		err := rows.Scan(&designPkgID, &size, &vanityProductID, &faucetProductID)
		if err != nil {
			r.logger.ErrorContext(ctx, "Failed to scan vanity scaling option",
				slog.String("error", err.Error()))
			return fmt.Errorf("failed to scan vanity scaling option: %w", err)
		}

		if vanityOptionsByDesignPkg[designPkgID] == nil {
			vanityOptionsByDesignPkg[designPkgID] = make(map[int]usecases.VanityScalingOption)
		}
		vanityOptionsByDesignPkg[designPkgID][size] = usecases.VanityScalingOption{
			VanityProductID: vanityProductID,
			FaucetProductID: faucetProductID,
		}
	}

	if err = rows.Err(); err != nil {
		r.logger.ErrorContext(ctx, "Failed to iterate over vanity scaling options",
			slog.String("error", err.Error()))
		return fmt.Errorf("error iterating vanity scaling options: %w", err)
	}

	for i := range designPkgs {
		if options, exists := vanityOptionsByDesignPkg[designPkgs[i].ID]; exists {
			designPkgs[i].VanityScalingOptions = options
		} else {
			designPkgs[i].VanityScalingOptions = make(map[int]usecases.VanityScalingOption)
		}
	}

	return nil
}

func stringFromPtr(ptr *string) string {
	if ptr != nil {
		return *ptr
	}
	return ""
}
