package gateways

import (
	"context"
	"fmt"
	"net/url"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// FakeProductSearch is a fake implementation of the productSearch interface for testing
type FakeProductSearch struct {
	results map[string][]uuid.UUID
	errors  map[string]error
}

func NewFakeProductSearch() *FakeProductSearch {
	return &FakeProductSearch{
		results: make(map[string][]uuid.UUID),
		errors:  make(map[string]error),
	}
}

func (f *FakeProductSearch) FindProductsViaAI(ctx context.Context, filters usecases.ProductSearchFilters) ([]uuid.UUID, error) {
	// Use category as key for AI search results, similar to regular search
	key := filters.Category.AsPathSegment()
	if err, exists := f.errors[key]; exists {
		return nil, err
	}
	if results, exists := f.results[key]; exists {
		return results, nil
	}
	return []uuid.UUID{}, nil
}

func (f *FakeProductSearch) FindProducts(ctx context.Context, urlQueryParams string) ([]uuid.UUID, error) {
	if err, hasError := f.errors[urlQueryParams]; hasError {
		return nil, err
	}

	if results, found := f.results[urlQueryParams]; found {
		return results, nil
	}

	return []uuid.UUID{}, nil
}

func (f *FakeProductSearch) AddResults(query string, results []uuid.UUID) {
	f.results[query] = results
}

func (f *FakeProductSearch) AddError(query string, err error) {
	f.errors[query] = err
}

// AddTileResults is a helper method to add tile search results for common tile search patterns
func (f *FakeProductSearch) AddTileResults(location string, productFamilyName *string, color *usecases.ColorGroup, results []uuid.UUID) {
	// Base query without collection and color filters
	baseQuery := fmt.Sprintf("category=Tile&tile_location=%s", location)
	f.results[baseQuery] = results

	// Query with color filter
	if color != nil {
		queryWithColor := fmt.Sprintf("%s&colors=%s", baseQuery, *color)
		f.results[queryWithColor] = results
	}

	// Query with collection filter
	if productFamilyName != nil {
		escapedFamily := url.QueryEscape(*productFamilyName)
		queryWithCollection := fmt.Sprintf("%s&collection=%s", baseQuery, escapedFamily)
		f.results[queryWithCollection] = results

		// Query with both collection and color filters
		if color != nil {
			queryWithBoth := fmt.Sprintf("%s&collection=%s&colors=%s", baseQuery, escapedFamily, *color)
			f.results[queryWithBoth] = results
		}
	}
}
