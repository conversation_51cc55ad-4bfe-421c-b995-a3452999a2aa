//go:build integration

package gateways_test

import (
	"context"
	"log"
	"os"
	"testing"
	"time"

	"github.com/jackc/pgx/v5/pgxpool"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/frameworks/db"
)

const TEST_DATABASE_URL = "postgres://postgres@localhost:5432/room-design-test"

var testPool *pgxpool.Pool // Global pool for all tests in this package

// TestMain is the entry point for tests in this package. It sets up the
// database connection pool and closes it after all tests have run.
func TestMain(m *testing.M) {
	databaseUrl := os.Getenv("TEST_DATABASE_URL")
	if databaseUrl == "" {
		log.Println("TEST_DATABASE_URL env var not set; using default value: ", TEST_DATABASE_URL)
		databaseUrl = TEST_DATABASE_URL
	}

	// Create a test-specific pool configuration with smaller limits
	testPoolConfig := &db.PoolConfig{
		MaxConns:          10,               // Smaller pool for tests
		MinConns:          2,                // Minimum connections for tests
		MaxConnLifetime:   30 * time.Minute, // Shorter lifetime for tests
		MaxConnIdleTime:   15 * time.Minute, // Shorter idle time for tests
		HealthCheckPeriod: 2 * time.Minute,  // Less frequent health checks for tests
	}

	// Parse the connection string and create pool config
	config, err := pgxpool.ParseConfig(databaseUrl)
	if err != nil {
		log.Fatalf("Failed to parse test database URL: %v", err)
	}

	// Apply test pool configuration
	config.MaxConns = testPoolConfig.MaxConns
	config.MinConns = testPoolConfig.MinConns
	config.MaxConnLifetime = testPoolConfig.MaxConnLifetime
	config.MaxConnIdleTime = testPoolConfig.MaxConnIdleTime
	config.HealthCheckPeriod = testPoolConfig.HealthCheckPeriod

	// Create the test pool with configuration
	testPool, err = pgxpool.NewWithConfig(context.Background(), config)
	if err != nil {
		log.Fatalf("Failed to connect to test database: %v", err)
	}
	defer testPool.Close()

	log.Printf("Test database pool created with max_conns=%d, min_conns=%d",
		testPoolConfig.MaxConns, testPoolConfig.MinConns)

	// Run all tests in the package
	exitCode := m.Run()
	os.Exit(exitCode)
}

// TestPoolConfiguration verifies that the connection pool is configured correctly
func TestPoolConfiguration(t *testing.T) {
	require.NotNil(t, testPool, "Test pool should be initialized")

	stats := testPool.Stat()

	// Verify pool configuration is applied
	assert.Equal(t, int32(10), stats.MaxConns(), "Max connections should be 10")
	assert.LessOrEqual(t, stats.TotalConns(), stats.MaxConns(), "Total connections should not exceed max")
	assert.GreaterOrEqual(t, stats.IdleConns(), int32(0), "Idle connections should be non-negative")

	// Test that we can acquire a connection
	conn, err := testPool.Acquire(context.Background())
	require.NoError(t, err, "Should be able to acquire a connection")
	defer conn.Release()

	// Verify connection is working
	var result int
	err = conn.QueryRow(context.Background(), "SELECT 1").Scan(&result)
	require.NoError(t, err, "Should be able to execute a simple query")
	assert.Equal(t, 1, result, "Query should return 1")
}

// Helper function to get a pointer to a value
func ptr[T any](v T) *T {
	return &v
}
