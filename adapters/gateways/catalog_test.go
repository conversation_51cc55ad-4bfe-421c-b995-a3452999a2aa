package gateways

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

func TestNewCatalog(t *testing.T) {
	t.Run("valid hostname", func(t *testing.T) {
		catalog := NewCatalog("example.com")
		assert.NotNil(t, catalog)
		assert.Equal(t, "example.com", catalog.hostname)
	})

	t.Run("empty hostname panics", func(t *testing.T) {
		assert.Panics(t, func() {
			NewCatalog("")
		})
	})
}

func TestCatalog_validateInputs(t *testing.T) {
	t.Run("valid inputs", func(t *testing.T) {
		productId := uuid.New()
		err := validateInputs("vanities", productId)
		assert.NoError(t, err)
	})

	t.Run("empty category", func(t *testing.T) {
		productId := uuid.New()
		err := validateInputs("", productId)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "category cannot be empty")
	})

	t.Run("whitespace only category", func(t *testing.T) {
		productId := uuid.New()
		err := validateInputs("   ", productId)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "category cannot be empty or whitespace only")
	})

	t.Run("nil UUID", func(t *testing.T) {
		err := validateInputs("vanities", uuid.Nil)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "productId cannot be nil/empty")
	})
}

func TestCatalog_ProductInfo(t *testing.T) {
	productId := uuid.New()

	t.Run("successful request", func(t *testing.T) {
		// Create a TLS mock server
		server := httptest.NewTLSServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Verify the request
			expectedPath := fmt.Sprintf("/catalog/v2/products/renderable-products/vanities/%s", productId.String())
			assert.Equal(t, expectedPath, r.URL.Path)
			assert.Equal(t, "GET", r.Method)

			// Return mock response
			response := struct {
				Data []adapters.ProductInfo `json:"data"`
			}{
				Data: []adapters.ProductInfo{
					{
						Id:                productId,
						Length:            "48.0",
						Height:            "34.5",
						ProductFamilyName: "Test Family",
						ColorGroup: &struct {
							Name usecases.ColorGroup `json:"name"`
						}{Name: usecases.White},
					},
				},
			}

			w.Header().Set("Content-Type", "application/json")
			err := json.NewEncoder(w).Encode(response)
			require.NoError(t, err)
		}))
		defer server.Close()

		// Extract hostname from server URL (remove https://)
		hostname := server.URL[8:] // Remove "https://"
		catalog := NewCatalog(hostname)

		// Use the test server's client which accepts self-signed certificates
		originalClient := http.DefaultClient
		http.DefaultClient = server.Client()
		defer func() { http.DefaultClient = originalClient }()

		result, err := catalog.ProductInfo(context.Background(), "vanities", productId)
		require.NoError(t, err)
		assert.Equal(t, productId, result.(usecases.ProductInfo).Id)
		assert.Equal(t, 48.0, result.(usecases.ProductInfo).Length)
		assert.Equal(t, "Test Family", result.(usecases.ProductInfo).ProductFamilyName)
		assert.NotNil(t, result.(usecases.ProductInfo).Color)
		assert.Equal(t, usecases.White, *result.(usecases.ProductInfo).Color)
	})

	t.Run("input validation error", func(t *testing.T) {
		catalog := NewCatalog("example.com")

		_, err := catalog.ProductInfo(context.Background(), "", productId)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "category cannot be empty")
	})

	t.Run("HTTP error", func(t *testing.T) {
		// Create a server that returns an error
		server := httptest.NewTLSServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusInternalServerError)
		}))
		defer server.Close()

		hostname := server.URL[8:]
		catalog := NewCatalog(hostname)

		// Use the test server's client
		originalClient := http.DefaultClient
		http.DefaultClient = server.Client()
		defer func() { http.DefaultClient = originalClient }()

		_, err := catalog.ProductInfo(context.Background(), "vanities", productId)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "error response from Catalog")
	})

	t.Run("empty response data", func(t *testing.T) {
		server := httptest.NewTLSServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			response := struct {
				Data []adapters.ProductInfo `json:"data"`
			}{
				Data: []adapters.ProductInfo{},
			}
			w.Header().Set("Content-Type", "application/json")
			err := json.NewEncoder(w).Encode(response)
			require.NoError(t, err)
		}))
		defer server.Close()

		hostname := server.URL[8:]
		catalog := NewCatalog(hostname)

		// Use the test server's client
		originalClient := http.DefaultClient
		http.DefaultClient = server.Client()
		defer func() { http.DefaultClient = originalClient }()

		_, err := catalog.ProductInfo(context.Background(), "vanities", productId)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "no products returned from Catalog")
		assert.Contains(t, err.Error(), "vanities")
		assert.Contains(t, err.Error(), productId.String())
	})

	t.Run("invalid JSON response", func(t *testing.T) {
		server := httptest.NewTLSServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.Header().Set("Content-Type", "application/json")
			_, err := w.Write([]byte("invalid json"))
			require.NoError(t, err)
		}))
		defer server.Close()

		hostname := server.URL[8:]
		catalog := NewCatalog(hostname)

		// Use the test server's client
		originalClient := http.DefaultClient
		http.DefaultClient = server.Client()
		defer func() { http.DefaultClient = originalClient }()

		_, err := catalog.ProductInfo(context.Background(), "vanities", productId)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to unmarshal response from Catalog")
	})

	t.Run("conversion error", func(t *testing.T) {
		server := httptest.NewTLSServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Return invalid data that will cause conversion to fail
			response := struct {
				Data []adapters.ProductInfo `json:"data"`
			}{
				Data: []adapters.ProductInfo{
					{
						Id:                productId,
						Length:            "invalid_float", // This will cause conversion error
						ProductFamilyName: "Test Family",
					},
				},
			}
			w.Header().Set("Content-Type", "application/json")
			err := json.NewEncoder(w).Encode(response)
			require.NoError(t, err)
		}))
		defer server.Close()

		hostname := server.URL[8:]
		catalog := NewCatalog(hostname)

		// Use the test server's client
		originalClient := http.DefaultClient
		http.DefaultClient = server.Client()
		defer func() { http.DefaultClient = originalClient }()

		_, err := catalog.ProductInfo(context.Background(), "vanities", productId)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to convert product info to usecase format")
	})
}

func TestCatalog_ProductDescriptionsForDesign(t *testing.T) {
	vanityId := uuid.New()
	faucetId := uuid.New()
	mirrorId := uuid.New()

	t.Run("successful request with multiple products", func(t *testing.T) {
		// Create a TLS mock server
		server := httptest.NewTLSServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Verify the request
			assert.Equal(t, "include[]=details", r.URL.RawQuery)
			assert.Equal(t, "GET", r.Method)

			// Return mock response
			response := struct {
				Data []struct {
					ID      uuid.UUID `json:"id"`
					Details struct {
						Description string `json:"description"`
					} `json:"details"`
				} `json:"data"`
			}{
				Data: []struct {
					ID      uuid.UUID `json:"id"`
					Details struct {
						Description string `json:"description"`
					} `json:"details"`
				}{
					{
						ID: vanityId,
						Details: struct {
							Description string `json:"description"`
						}{Description: "Beautiful vanity with modern design"},
					},
					{
						ID: faucetId,
						Details: struct {
							Description string `json:"description"`
						}{Description: "High-quality brass faucet"},
					},
					{
						ID: mirrorId,
						Details: struct {
							Description string `json:"description"`
						}{Description: "Large rectangular mirror"},
					},
				},
			}

			w.Header().Set("Content-Type", "application/json")
			err := json.NewEncoder(w).Encode(response)
			require.NoError(t, err)
		}))
		defer server.Close()

		// Extract hostname from server URL (remove https://)
		hostname := server.URL[8:] // Remove "https://"
		catalog := NewCatalog(hostname)

		// Use the test server's client which accepts self-signed certificates
		originalClient := http.DefaultClient
		http.DefaultClient = server.Client()
		defer func() { http.DefaultClient = originalClient }()

		// Create a design with multiple products
		design := usecases.NewEmptyDesign()
		design.Vanity = &vanityId
		design.Faucet = &faucetId
		design.Mirror = &mirrorId

		result, err := catalog.ProductDescriptionsForDesign(context.Background(), design)
		require.NoError(t, err)
		assert.Len(t, result, 3)
		assert.Equal(t, "Beautiful vanity with modern design", result["vanity"])
		assert.Equal(t, "High-quality brass faucet", result["faucet"])
		assert.Equal(t, "Large rectangular mirror", result["mirror"])
	})

	t.Run("design with no products returns ErrNotFound", func(t *testing.T) {
		catalog := NewCatalog("example.com")

		// Create an empty design
		design := usecases.NewEmptyDesign()

		result, err := catalog.ProductDescriptionsForDesign(context.Background(), design)
		assert.Error(t, err)
		assert.Equal(t, usecases.ErrNotFound, err)
		assert.Empty(t, result)
	})

	t.Run("HTTP error from ProductDescriptions", func(t *testing.T) {
		// Create a server that returns an error
		server := httptest.NewTLSServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusInternalServerError)
		}))
		defer server.Close()

		hostname := server.URL[8:]
		catalog := NewCatalog(hostname)

		// Use the test server's client
		originalClient := http.DefaultClient
		http.DefaultClient = server.Client()
		defer func() { http.DefaultClient = originalClient }()

		// Create a design with one product
		design := usecases.NewEmptyDesign()
		design.Vanity = &vanityId

		_, err := catalog.ProductDescriptionsForDesign(context.Background(), design)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "error response from Catalog")
	})

	t.Run("partial success - some products not found in response", func(t *testing.T) {
		server := httptest.NewTLSServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Return response with only one of the requested products
			response := struct {
				Data []struct {
					ID      uuid.UUID `json:"id"`
					Details struct {
						Description string `json:"description"`
					} `json:"details"`
				} `json:"data"`
			}{
				Data: []struct {
					ID      uuid.UUID `json:"id"`
					Details struct {
						Description string `json:"description"`
					} `json:"details"`
				}{
					{
						ID: vanityId,
						Details: struct {
							Description string `json:"description"`
						}{Description: "Beautiful vanity with modern design"},
					},
					// Note: faucetId is missing from response
				},
			}

			w.Header().Set("Content-Type", "application/json")
			err := json.NewEncoder(w).Encode(response)
			require.NoError(t, err)
		}))
		defer server.Close()

		hostname := server.URL[8:]
		catalog := NewCatalog(hostname)

		// Use the test server's client
		originalClient := http.DefaultClient
		http.DefaultClient = server.Client()
		defer func() { http.DefaultClient = originalClient }()

		// Create a design with two products
		design := usecases.NewEmptyDesign()
		design.Vanity = &vanityId
		design.Faucet = &faucetId

		result, err := catalog.ProductDescriptionsForDesign(context.Background(), design)
		require.NoError(t, err)
		assert.Len(t, result, 1) // Only vanity should be in result
		assert.Equal(t, "Beautiful vanity with modern design", result["vanity"])
		assert.NotContains(t, result, "faucet") // faucet should be missing
	})
}
