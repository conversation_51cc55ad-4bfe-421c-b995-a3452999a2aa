package gateways

import (
	"context"
	"net/url"
	"slices"
	"sync"
	"time"

	"github.com/google/uuid"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// FakeRelDb is a fake implementation of the usecases.designRepository interface
// for use in unit tests.
type FakeRelDb struct {
	mu                 sync.Mutex
	designs            map[uuid.UUID]*usecases.Design
	projIdx            map[entities.ProjectId][]*usecases.Design
	designPkgs         map[uuid.UUID]*usecases.DesignPackage
	renditions         map[uuid.UUID]*entities.Rendition
	designRenditionIdx map[uuid.UUID][]*entities.Rendition
}

func NewFakeRelDb() *FakeRelDb {
	return &FakeRelDb{
		designs:            make(map[uuid.UUID]*usecases.Design),
		projIdx:            make(map[entities.ProjectId][]*usecases.Design),
		designPkgs:         make(map[uuid.UUID]*usecases.DesignPackage),
		renditions:         make(map[uuid.UUID]*entities.Rendition),
		designRenditionIdx: make(map[uuid.UUID][]*entities.Rendition),
	}
}
func (r *FakeRelDb) LegacyIdMapping(ctx context.Context) (map[uuid.UUID]string, error) {
	return nil, nil
}

func (r *FakeRelDb) FindPresetByLegacyId(ctx context.Context, designPkgID string) (usecases.Preset, error) {
	return usecases.Preset{}, nil
}

func (r *FakeRelDb) ProjectIdForDesign(ctx context.Context, designId uuid.UUID) (entities.ProjectId, error) {
	r.mu.Lock()
	defer r.mu.Unlock()
	design, exists := r.designs[designId]
	if !exists {
		return "", usecases.ErrNotFound
	}
	return design.ProjectID, nil
}

func (r *FakeRelDb) MarkRenditionsOutdatedForDesign(ctx context.Context, designId uuid.UUID) error {
	return nil
}

func (r *FakeRelDb) InsertRendition(ctx context.Context, designId uuid.UUID, rendition entities.Rendition) (uuid.UUID, error) {
	r.mu.Lock()
	defer r.mu.Unlock()
	var zeroUUID uuid.UUID
	if rendition.Id == zeroUUID || rendition.Id == uuid.Nil {
		rendition.Id = uuid.New()
	}
	rendition.DesignId = &designId
	r.renditions[rendition.Id] = &rendition
	r.designRenditionIdx[designId] = append(r.designRenditionIdx[designId], &rendition)
	return rendition.Id, nil
}
func (r *FakeRelDb) Renditions(ctx context.Context, ids []uuid.UUID) ([]entities.Rendition, error) {
	r.mu.Lock()
	defer r.mu.Unlock()
	renditions := make([]entities.Rendition, 0, len(ids))
	for _, id := range ids {
		if rendition, ok := r.renditions[id]; ok {
			renditions = append(renditions, *rendition)
		}
	}
	return renditions, nil
}
func (r *FakeRelDb) PanoramicImagesForDesignPackages(ctx context.Context, designPkgIDs []uuid.UUID) (map[uuid.UUID]url.URL, error) {
	r.mu.Lock()
	defer r.mu.Unlock()
	imgURLs := make(map[uuid.UUID]url.URL)
	for _, id := range designPkgIDs {
		if designPkg, exists := r.designPkgs[id]; exists {
			// This logic is wrong but will work fine for tests.
			imgURLs[id] = designPkg.ImageURL
		}
	}
	return imgURLs, nil
}
func (r *FakeRelDb) RenditionsForDesign(ctx context.Context, designId uuid.UUID) ([]entities.Rendition, error) {
	r.mu.Lock()
	defer r.mu.Unlock()
	renditionPtrs, ok := r.designRenditionIdx[designId]
	if !ok {
		return []entities.Rendition{}, nil
	}
	renditions := make([]entities.Rendition, len(renditionPtrs))
	for i, r := range renditionPtrs {
		renditions[i] = *r
	}
	return renditions, nil
}
func (r *FakeRelDb) UpdateRendition(ctx context.Context, rendition entities.RenditionDiff) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	existing, ok := r.renditions[rendition.Id]
	if !ok {
		return usecases.ErrNotFound
	}
	if err := existing.MergeWith(rendition); err != nil {
		return err
	}
	return nil
}
func (r *FakeRelDb) DeleteRendition(ctx context.Context, id uuid.UUID) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	renditionToDelete, ok := r.renditions[id]
	if !ok {
		return usecases.ErrNotFound
	}
	delete(r.renditions, id)
	if renditionToDelete.DesignId != nil {
		designId := *renditionToDelete.DesignId
		if renditions, ok := r.designRenditionIdx[designId]; ok {
			for i, rendition := range renditions {
				if rendition.Id == id {
					r.designRenditionIdx[designId] = slices.Delete(renditions, i, i+1)
					break
				}
			}
		}
	}
	return nil
}

func (r *FakeRelDb) UpsertDesign(ctx context.Context, design usecases.Design) (uuid.UUID, error) {
	r.mu.Lock()
	defer r.mu.Unlock()

	var zeroUUID uuid.UUID
	if design.ID == zeroUUID || design.ID == uuid.Nil {
		design.ID = uuid.New()
	}
	design.LastUpdated = time.Now()
	if design.Created.IsZero() {
		design.Created = design.LastUpdated
	}

	// If design exists, we might need to update the project index if the project ID changed.
	if oldDesign, exists := r.designs[design.ID]; exists {
		if oldDesign.ProjectID != design.ProjectID {
			// Remove from old project index
			if oldProjectDesigns, ok := r.projIdx[oldDesign.ProjectID]; ok {
				for i, d := range oldProjectDesigns {
					if d.ID == oldDesign.ID {
						r.projIdx[oldDesign.ProjectID] = slices.Delete(oldProjectDesigns, i, i+1)
						break
					}
				}
			}
		}
	}

	r.designs[design.ID] = &design

	// Update or add to the new project index.
	newProjectDesigns := r.projIdx[design.ProjectID]
	found := false
	for i, d := range newProjectDesigns {
		if d.ID == design.ID {
			newProjectDesigns[i] = &design
			found = true
			break
		}
	}
	if !found {
		r.projIdx[design.ProjectID] = append(newProjectDesigns, &design)
	}

	return design.ID, nil
}

func (r *FakeRelDb) ReadDesign(ctx context.Context, id uuid.UUID) (usecases.Design, error) {
	r.mu.Lock()
	defer r.mu.Unlock()
	d, ok := r.designs[id]
	if !ok {
		return usecases.Design{}, usecases.ErrNotFound
	}
	return *d, nil
}

func (m *FakeRelDb) DesignsForProject(ctx context.Context, projectId entities.ProjectId) ([]usecases.Design, error) {
	m.mu.Lock()
	defer m.mu.Unlock()
	results, ok := m.projIdx[projectId]
	if !ok {
		return []usecases.Design{}, nil
	}
	designs := make([]usecases.Design, len(results))
	for i, d := range results {
		designs[i] = *d
	}
	return designs, nil
}

func (r *FakeRelDb) DesignsByProject(ctx context.Context, projectIDs []entities.ProjectId) (
	map[entities.ProjectId][]usecases.Design, []error, error) {
	r.mu.Lock()
	defer r.mu.Unlock()
	results := make(map[entities.ProjectId][]usecases.Design)
	errors := []error{}
	for _, projectId := range projectIDs {
		designs, err := r.designsForProjectUnlocked(projectId)
		if err != nil {
			errors = append(errors, err)
			continue
		}
		results[projectId] = designs
	}
	return results, errors, nil
}

func (r *FakeRelDb) designsForProjectUnlocked(projectId entities.ProjectId) ([]usecases.Design, error) {
	results, ok := r.projIdx[projectId]
	if !ok {
		return []usecases.Design{}, nil
	}
	designs := make([]usecases.Design, len(results))
	for i, d := range results {
		designs[i] = *d
	}
	return designs, nil
}

func (r *FakeRelDb) DeleteDesign(ctx context.Context, id uuid.UUID) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	d, ok := r.designs[id]
	if !ok {
		return usecases.ErrNotFound
	}
	delete(r.designs, id)

	if projDesigns, ok := r.projIdx[d.ProjectID]; ok {
		r.projIdx[d.ProjectID] = slices.DeleteFunc(projDesigns, func(design *usecases.Design) bool {
			return design.ID == id
		})
	}
	return nil
}

func (r *FakeRelDb) DeleteDesignsForProjectExceptSpecified(ctx context.Context, projectId entities.ProjectId, designIdsToKeep []uuid.UUID) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	designs, ok := r.projIdx[projectId]
	if !ok {
		return nil
	}
	designIdsToDelete := make([]uuid.UUID, 0)
	designsToKeep := []*usecases.Design{}
	for _, d := range designs {
		if slices.Contains(designIdsToKeep, d.ID) {
			designsToKeep = append(designsToKeep, d)
			continue
		}
		designIdsToDelete = append(designIdsToDelete, d.ID)
	}
	if len(designIdsToDelete) == 0 {
		return nil
	}
	r.projIdx[projectId] = designsToKeep
	for _, designId := range designIdsToDelete {
		delete(r.designs, designId)
	}
	return nil
}

func (r *FakeRelDb) MarkAllCompletedRenditionsOutdatedForProject(ctx context.Context, projectId entities.ProjectId) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	designs, ok := r.projIdx[projectId]
	if !ok {
		return nil
	}
	for _, design := range designs {
		for _, rendition := range r.designRenditionIdx[design.ID] {
			if rendition.Status == entities.RenditionCompleted {
				rendition.Status = entities.RenditionOutdated
			}
		}
	}
	return nil
}

func (r *FakeRelDb) InsertDesignPackage(ctx context.Context, designPkg usecases.DesignPackage, legacyId string) (uuid.UUID, error) {
	r.mu.Lock()
	defer r.mu.Unlock()
	var zeroUUID uuid.UUID
	if designPkg.ID == zeroUUID || designPkg.ID == uuid.Nil {
		designPkg.ID = uuid.New()
	}
	designPkg.UpdatedAt = time.Now()
	r.designPkgs[designPkg.ID] = &designPkg
	return designPkg.ID, nil
}

func (r *FakeRelDb) ReadDesignPackage(ctx context.Context, designPkgID uuid.UUID) (usecases.DesignPackage, error) {
	r.mu.Lock()
	defer r.mu.Unlock()
	t, ok := r.designPkgs[designPkgID]
	if !ok {
		return usecases.DesignPackage{}, usecases.ErrNotFound
	}
	return *t, nil
}

func (r *FakeRelDb) ReadAllDesignPackages(ctx context.Context) ([]usecases.DesignPackage, error) {
	r.mu.Lock()
	defer r.mu.Unlock()
	designPkgs := make([]usecases.DesignPackage, 0, len(r.designPkgs))
	for _, t := range r.designPkgs {
		designPkgs = append(designPkgs, *t)
	}
	return designPkgs, nil
}

func (r *FakeRelDb) DesignPackagesById(ctx context.Context, designPkgIDs []uuid.UUID) ([]usecases.DesignPackage, error) {
	r.mu.Lock()
	defer r.mu.Unlock()
	designPkgs := make([]usecases.DesignPackage, 0, len(designPkgIDs))
	for _, id := range designPkgIDs {
		if designPkg, exists := r.designPkgs[id]; exists {
			designPkgs = append(designPkgs, *designPkg)
		}
	}
	return designPkgs, nil
}

func (r *FakeRelDb) ReadDesignPackageByLegacyId(ctx context.Context, legacyId string) (usecases.DesignPackage, error) {
	r.mu.Lock()
	defer r.mu.Unlock()
	// In the fake implementation, we'll search through design packages to find one with matching legacy ID
	for _, t := range r.designPkgs {
		if t.LegacyId != nil && *t.LegacyId == legacyId {
			return *t, nil
		}
	}
	return usecases.DesignPackage{}, usecases.ErrNotFound
}

//
// ============================================================================
// Cart Inclusions Repository Methods
// ============================================================================
//

func (r *FakeRelDb) UpsertCartInclusion(ctx context.Context, designId uuid.UUID, inclusion usecases.CartInclusion) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	design, exists := r.designs[designId]
	if !exists {
		return usecases.ErrNotFound
	}

	key := usecases.CartInclusionKey{
		ProductID: inclusion.ProductID,
		Location:  inclusion.Location,
	}
	design.CartInclusions[key] = inclusion
	return nil
}

func (r *FakeRelDb) UpsertCartInclusionsForDesign(ctx context.Context, designId uuid.UUID, inclusions usecases.CartInclusions) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	design, exists := r.designs[designId]
	if !exists {
		return usecases.ErrNotFound
	}

	if design.CartInclusions == nil {
		design.CartInclusions = inclusions
		return nil
	}

	// Merge the provided inclusions with existing ones
	// This overwrites existing inclusions for the same key and preserves others
	for key, inclusion := range inclusions {
		design.CartInclusions[key] = inclusion
	}

	return nil
}

func (r *FakeRelDb) ReplaceCartInclusionsForDesign(ctx context.Context, designId uuid.UUID, inclusions usecases.CartInclusions) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	design, exists := r.designs[designId]
	if !exists {
		return usecases.ErrNotFound
	}

	// Full replacement - clear existing and set new inclusions
	design.CartInclusions = inclusions
	return nil
}

func (r *FakeRelDb) CartInclusionsForDesign(ctx context.Context, designId uuid.UUID) (usecases.CartInclusions, error) {
	r.mu.Lock()
	defer r.mu.Unlock()

	design, exists := r.designs[designId]
	if !exists {
		return make(usecases.CartInclusions), nil
	}

	return design.CartInclusions, nil
}

func (r *FakeRelDb) DeleteCartInclusion(ctx context.Context, designId uuid.UUID, productId uuid.UUID, location usecases.Location) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	design, exists := r.designs[designId]
	if !exists {
		return usecases.ErrNotFound
	}

	key := usecases.CartInclusionKey{
		ProductID: productId,
		Location:  location,
	}
	if _, keyExists := design.CartInclusions[key]; keyExists {
		delete(design.CartInclusions, key)
		return nil
	}
	return usecases.ErrNotFound
}

func (r *FakeRelDb) DeleteCartInclusionsForDesign(ctx context.Context, designId uuid.UUID) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	design, exists := r.designs[designId]
	if !exists {
		return usecases.ErrNotFound
	}

	design.CartInclusions = make(usecases.CartInclusions)
	return nil
}

//
// ============================================================================
// End Cart Inclusions Repository Methods
// ============================================================================
//
