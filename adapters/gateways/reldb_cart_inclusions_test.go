//go:build integration

package gateways_test

import (
	"context"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// TestRelationalDb_Integration_CartInclusions_SaveAndGet tests the basic save and retrieve
// operations for cart inclusions against a live database.
func TestRelationalDb_Integration_CartInclusions_SaveAndGet(t *testing.T) {
	// Arrange
	ctx := context.Background()
	db := gateways.NewRelationalDb(testPool, nil)

	// Ensure the database is clean before this test runs.
	_, err := testPool.Exec(ctx, "TRUNCATE TABLE design.room_designs CASCADE;")
	require.NoError(t, err, "Failed to truncate tables before test")

	// Create a test design first
	design := usecases.NewEmptyDesign()
	designId, err := db.UpsertDesign(ctx, design)
	require.NoError(t, err)

	// Create test cart inclusions
	productID1 := uuid.New()
	productID2 := uuid.New()

	inclusion1 := usecases.CartInclusion{
		ProductID:    productID1,
		Location:     usecases.LocationFloor,
		Include:      true,
		QuantityDiff: 2,
	}

	inclusion2 := usecases.CartInclusion{
		ProductID:    productID2,
		Location:     usecases.LocationWall,
		Include:      false,
		QuantityDiff: -1,
	}

	// Save cart inclusions
	err = db.UpsertCartInclusion(ctx, designId, inclusion1)
	require.NoError(t, err)

	err = db.UpsertCartInclusion(ctx, designId, inclusion2)
	require.NoError(t, err)

	// Retrieve cart inclusions
	inclusions, err := db.CartInclusionsForDesign(ctx, designId)
	require.NoError(t, err)
	require.Len(t, inclusions, 2)

	// Verify inclusions (now a map with composite keys)
	key1 := usecases.CartInclusionKey{ProductID: productID1, Location: usecases.LocationFloor}
	result1, exists := inclusions[key1]
	require.True(t, exists, "Expected inclusion for productID1")
	assert.Equal(t, productID1, result1.ProductID)
	assert.Equal(t, usecases.LocationFloor, result1.Location)
	assert.True(t, result1.Include)
	assert.Equal(t, 2, result1.QuantityDiff)

	key2 := usecases.CartInclusionKey{ProductID: productID2, Location: usecases.LocationWall}
	result2, exists := inclusions[key2]
	require.True(t, exists, "Expected inclusion for productID2")
	assert.Equal(t, productID2, result2.ProductID)
	assert.Equal(t, usecases.LocationWall, result2.Location)
	assert.False(t, result2.Include)
	assert.Equal(t, -1, result2.QuantityDiff)
}

// TestRelationalDb_Integration_CartInclusions_UpdateExisting tests the upsert functionality
// to ensure existing cart inclusions can be updated properly.
func TestRelationalDb_Integration_CartInclusions_UpdateExisting(t *testing.T) {
	// Arrange
	ctx := context.Background()
	db := gateways.NewRelationalDb(testPool, nil)

	// Ensure the database is clean before this test runs.
	_, err := testPool.Exec(ctx, "TRUNCATE TABLE design.room_designs CASCADE;")
	require.NoError(t, err, "Failed to truncate tables before test")

	// Create a test design
	design := usecases.NewEmptyDesign()
	designId, err := db.UpsertDesign(ctx, design)
	require.NoError(t, err)

	productID := uuid.New()

	// Save initial inclusion
	initial := usecases.CartInclusion{
		ProductID:    productID,
		Location:     usecases.LocationFloor,
		Include:      true,
		QuantityDiff: 1,
	}
	err = db.UpsertCartInclusion(ctx, designId, initial)
	require.NoError(t, err)

	// Update the inclusion
	updated := usecases.CartInclusion{
		ProductID:    productID,
		Location:     usecases.LocationFloor,
		Include:      false,
		QuantityDiff: 3,
	}
	err = db.UpsertCartInclusion(ctx, designId, updated)
	require.NoError(t, err)

	// Verify update
	inclusions, err := db.CartInclusionsForDesign(ctx, designId)
	require.NoError(t, err)
	require.Len(t, inclusions, 1)

	key := usecases.CartInclusionKey{ProductID: productID, Location: usecases.LocationFloor}
	result, exists := inclusions[key]
	require.True(t, exists, "Expected inclusion for productID")
	assert.Equal(t, productID, result.ProductID)
	assert.Equal(t, usecases.LocationFloor, result.Location)
	assert.False(t, result.Include)
	assert.Equal(t, 3, result.QuantityDiff)
}

// TestRelationalDb_Integration_CartInclusions_DeleteSpecific tests the deletion of
// specific cart inclusions while preserving others.
func TestRelationalDb_Integration_CartInclusions_DeleteSpecific(t *testing.T) {
	// Arrange
	ctx := context.Background()
	db := gateways.NewRelationalDb(testPool, nil)

	// Ensure the database is clean before this test runs.
	_, err := testPool.Exec(ctx, "TRUNCATE TABLE design.room_designs CASCADE;")
	require.NoError(t, err, "Failed to truncate tables before test")

	// Create a test design
	design := usecases.NewEmptyDesign()
	designId, err := db.UpsertDesign(ctx, design)
	require.NoError(t, err)

	productID1 := uuid.New()
	productID2 := uuid.New()

	// Save two inclusions
	result1 := usecases.CartInclusion{ProductID: productID1, Location: usecases.LocationFloor, Include: true, QuantityDiff: 1}
	result2 := usecases.CartInclusion{ProductID: productID2, Location: usecases.LocationWall, Include: false, QuantityDiff: -1}

	err = db.UpsertCartInclusion(ctx, designId, result1)
	require.NoError(t, err)
	err = db.UpsertCartInclusion(ctx, designId, result2)
	require.NoError(t, err)

	// Delete one inclusion
	err = db.DeleteCartInclusion(ctx, designId, productID1, usecases.LocationFloor)
	require.NoError(t, err)

	// Verify only one remains
	inclusions, err := db.CartInclusionsForDesign(ctx, designId)
	require.NoError(t, err)
	require.Len(t, inclusions, 1)

	key2 := usecases.CartInclusionKey{ProductID: productID2, Location: usecases.LocationWall}
	adj, exists := inclusions[key2]
	require.True(t, exists, "Expected inclusion for productID2")
	assert.Equal(t, productID2, adj.ProductID)
	assert.Equal(t, usecases.LocationWall, adj.Location)
}

// TestRelationalDb_Integration_CartInclusions_DeleteAll tests the deletion of
// all cart inclusions for a design.
func TestRelationalDb_Integration_CartInclusions_DeleteAll(t *testing.T) {
	// Arrange
	ctx := context.Background()
	db := gateways.NewRelationalDb(testPool, nil)

	// Ensure the database is clean before this test runs.
	_, err := testPool.Exec(ctx, "TRUNCATE TABLE design.room_designs CASCADE;")
	require.NoError(t, err, "Failed to truncate tables before test")

	// Create a test design
	design := usecases.NewEmptyDesign()
	designId, err := db.UpsertDesign(ctx, design)
	require.NoError(t, err)

	// Save multiple inclusions
	locations := []usecases.Location{usecases.LocationFloor, usecases.LocationWall, usecases.LocationShowerFloor}
	for i := 0; i < 3; i++ {
		adj := usecases.CartInclusion{
			ProductID:    uuid.New(),
			Location:     locations[i],
			Include:      i%2 == 0,
			QuantityDiff: i,
		}
		err = db.UpsertCartInclusion(ctx, designId, adj)
		require.NoError(t, err)
	}

	// Verify inclusions exist
	inclusions, err := db.CartInclusionsForDesign(ctx, designId)
	require.NoError(t, err)
	require.Len(t, inclusions, 3)

	// Delete all inclusions
	err = db.DeleteCartInclusionsForDesign(ctx, designId)
	require.NoError(t, err)

	// Verify all are gone
	inclusions, err = db.CartInclusionsForDesign(ctx, designId)
	require.NoError(t, err)
	assert.Len(t, inclusions, 0)
}

// TestRelationalDb_Integration_CartInclusions_UpsertMultiple tests the bulk upsert
// functionality for multiple cart inclusions in a single transaction.
func TestRelationalDb_Integration_CartInclusions_UpsertMultiple(t *testing.T) {
	// Arrange
	ctx := context.Background()
	db := gateways.NewRelationalDb(testPool, nil)

	// Ensure the database is clean before this test runs.
	_, err := testPool.Exec(ctx, "TRUNCATE TABLE design.room_designs CASCADE;")
	require.NoError(t, err, "Failed to truncate tables before test")

	// Create a test design first
	design := usecases.NewEmptyDesign()
	designId, err := db.UpsertDesign(ctx, design)
	require.NoError(t, err)

	// Create multiple cart inclusions
	productID1 := uuid.New()
	productID2 := uuid.New()
	productID3 := uuid.New()

	key1 := usecases.CartInclusionKey{ProductID: productID1, Location: usecases.LocationFloor}
	key2 := usecases.CartInclusionKey{ProductID: productID2, Location: usecases.LocationWall}
	key3 := usecases.CartInclusionKey{ProductID: productID3, Location: usecases.LocationShowerFloor}

	inclusions := usecases.CartInclusions{
		key1: {
			ProductID:    productID1,
			Location:     usecases.LocationFloor,
			Include:      true,
			QuantityDiff: 2,
		},
		key2: {
			ProductID:    productID2,
			Location:     usecases.LocationWall,
			Include:      false,
			QuantityDiff: -1,
		},
		key3: {
			ProductID:    productID3,
			Location:     usecases.LocationShowerFloor,
			Include:      true,
			QuantityDiff: 5,
		},
	}

	// Bulk upsert
	err = db.UpsertCartInclusionsForDesign(ctx, designId, inclusions)
	require.NoError(t, err)

	// Verify all inclusions were saved
	retrievedInclusions, err := db.CartInclusionsForDesign(ctx, designId)
	require.NoError(t, err)
	require.Len(t, retrievedInclusions, 3)

	// Verify each inclusion
	for key, expectedInclusion := range inclusions {
		actualInclusion, exists := retrievedInclusions[key]
		require.True(t, exists, "Expected inclusion for product %s at location %s", key.ProductID, key.Location)
		assert.Equal(t, expectedInclusion.ProductID, actualInclusion.ProductID)
		assert.Equal(t, expectedInclusion.Location, actualInclusion.Location)
		assert.Equal(t, expectedInclusion.Include, actualInclusion.Include)
		assert.Equal(t, expectedInclusion.QuantityDiff, actualInclusion.QuantityDiff)
	}
}

// TestRelationalDb_Integration_CartInclusions_UpsertMultiple_Empty tests the bulk upsert
// functionality with an empty map to ensure it handles edge cases properly.
func TestRelationalDb_Integration_CartInclusions_UpsertMultiple_Empty(t *testing.T) {
	// Arrange
	ctx := context.Background()
	db := gateways.NewRelationalDb(testPool, nil)

	// Ensure the database is clean before this test runs.
	_, err := testPool.Exec(ctx, "TRUNCATE TABLE design.room_designs CASCADE;")
	require.NoError(t, err, "Failed to truncate tables before test")

	// Create a test design first
	design := usecases.NewEmptyDesign()
	designId, err := db.UpsertDesign(ctx, design)
	require.NoError(t, err)

	// Upsert empty map
	err = db.UpsertCartInclusionsForDesign(ctx, designId, usecases.CartInclusions{})
	require.NoError(t, err)

	// Verify no inclusions were created
	inclusions, err := db.CartInclusionsForDesign(ctx, designId)
	require.NoError(t, err)
	assert.Len(t, inclusions, 0)
}

// TestRelationalDb_Integration_CartInclusions_GetEmptyDesign tests retrieving cart inclusions
// for a design that has no inclusions to ensure it returns an empty result.
func TestRelationalDb_Integration_CartInclusions_GetEmptyDesign(t *testing.T) {
	// Arrange
	ctx := context.Background()
	db := gateways.NewRelationalDb(testPool, nil)

	// Ensure the database is clean before this test runs.
	_, err := testPool.Exec(ctx, "TRUNCATE TABLE design.room_designs CASCADE;")
	require.NoError(t, err, "Failed to truncate tables before test")

	// Create a test design with no inclusions
	design := usecases.NewEmptyDesign()
	designId, err := db.UpsertDesign(ctx, design)
	require.NoError(t, err)

	// Get inclusions for empty design
	inclusions, err := db.CartInclusionsForDesign(ctx, designId)
	require.NoError(t, err)
	assert.Len(t, inclusions, 0)
}

// TestRelationalDb_Integration_CartInclusions_DeleteNonExistent tests the error handling
// when attempting to delete a cart inclusion that doesn't exist.
func TestRelationalDb_Integration_CartInclusions_DeleteNonExistent(t *testing.T) {
	// Arrange
	ctx := context.Background()
	db := gateways.NewRelationalDb(testPool, nil)

	// Ensure the database is clean before this test runs.
	_, err := testPool.Exec(ctx, "TRUNCATE TABLE design.room_designs CASCADE;")
	require.NoError(t, err, "Failed to truncate tables before test")

	// Create a test design
	design := usecases.NewEmptyDesign()
	designId, err := db.UpsertDesign(ctx, design)
	require.NoError(t, err)

	// Try to delete non-existent inclusion
	err = db.DeleteCartInclusion(ctx, designId, uuid.New(), usecases.LocationFloor)
	assert.Error(t, err)
	assert.Equal(t, usecases.ErrNotFound, err)
}

// TestRelationalDb_Integration_CartInclusions_ReplaceAll tests the complete replacement
// of all cart inclusions for a design in a single atomic transaction.
func TestRelationalDb_Integration_CartInclusions_ReplaceAll(t *testing.T) {
	// Arrange
	ctx := context.Background()
	db := gateways.NewRelationalDb(testPool, nil)

	// Ensure the database is clean before this test runs.
	_, err := testPool.Exec(ctx, "TRUNCATE TABLE design.room_designs CASCADE;")
	require.NoError(t, err, "Failed to truncate tables before test")

	// Create a test design
	design := usecases.NewEmptyDesign()
	designId, err := db.UpsertDesign(ctx, design)
	require.NoError(t, err)

	// Add initial inclusions
	productID1 := uuid.New()
	productID2 := uuid.New()
	initial1 := usecases.CartInclusion{ProductID: productID1, Location: usecases.LocationFloor, Include: true, QuantityDiff: 1}
	initial2 := usecases.CartInclusion{ProductID: productID2, Location: usecases.LocationWall, Include: false, QuantityDiff: -1}

	err = db.UpsertCartInclusion(ctx, designId, initial1)
	require.NoError(t, err)
	err = db.UpsertCartInclusion(ctx, designId, initial2)
	require.NoError(t, err)

	// Verify initial inclusions exist
	inclusions, err := db.CartInclusionsForDesign(ctx, designId)
	require.NoError(t, err)
	require.Len(t, inclusions, 2)

	// Replace with completely different inclusions
	productID3 := uuid.New()
	productID4 := uuid.New()
	key3 := usecases.CartInclusionKey{ProductID: productID3, Location: usecases.LocationShowerFloor}
	key4 := usecases.CartInclusionKey{ProductID: productID4, Location: usecases.LocationShowerWall}

	replacementInclusions := usecases.CartInclusions{
		key3: {ProductID: productID3, Location: usecases.LocationShowerFloor, Include: true, QuantityDiff: 5},
		key4: {ProductID: productID4, Location: usecases.LocationShowerWall, Include: false, QuantityDiff: -2},
	}

	err = db.ReplaceCartInclusionsForDesign(ctx, designId, replacementInclusions)
	require.NoError(t, err)

	// Verify replacement worked - should only have the new inclusions
	finalInclusions, err := db.CartInclusionsForDesign(ctx, designId)
	require.NoError(t, err)
	require.Len(t, finalInclusions, 2)

	// Verify the old inclusions are gone and new ones are present
	_, exists := finalInclusions[usecases.CartInclusionKey{ProductID: productID1, Location: usecases.LocationFloor}]
	assert.False(t, exists, "Old inclusion should be removed")
	_, exists = finalInclusions[usecases.CartInclusionKey{ProductID: productID2, Location: usecases.LocationWall}]
	assert.False(t, exists, "Old inclusion should be removed")

	newInclusion3, exists := finalInclusions[key3]
	require.True(t, exists, "New inclusion should be present")
	assert.Equal(t, productID3, newInclusion3.ProductID)
	assert.Equal(t, usecases.LocationShowerFloor, newInclusion3.Location)
	assert.True(t, newInclusion3.Include)
	assert.Equal(t, 5, newInclusion3.QuantityDiff)

	newInclusion4, exists := finalInclusions[key4]
	require.True(t, exists, "New inclusion should be present")
	assert.Equal(t, productID4, newInclusion4.ProductID)
	assert.Equal(t, usecases.LocationShowerWall, newInclusion4.Location)
	assert.False(t, newInclusion4.Include)
	assert.Equal(t, -2, newInclusion4.QuantityDiff)
}
