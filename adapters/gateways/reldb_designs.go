package gateways

import (
	"context"
	"errors"
	"fmt"
	"log/slog"

	"github.com/google/uuid"
	"github.com/jackc/pgx/v5"

	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// UpsertDesign persists a Design entity to the database.
// It uses a transaction to ensure atomicity across multiple table insertions.
func (r *RelationalDb) UpsertDesign(ctx context.Context, design usecases.Design) (uuid.UUID, error) {
	var zeroUUID uuid.UUID
	if design.ID == zeroUUID {
		design.ID = uuid.New()
		r.logger.InfoContext(ctx, "Generating new ID for design", "designID", design.ID)
	}
	tx, err := r.db.Begin(ctx)
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to begin transaction", slog.String("error", err.Error()))
		return zeroUUID, fmt.Errorf("failed to begin transaction: %w", err)
	}
	defer func() {
		if err != nil {
			if err := tx.Rollback(ctx); err != nil {
				r.logger.ErrorContext(ctx, "Failed to rollback transaction", slog.String("error", err.Error()))
			}
		}
	}()

	// 1. Insert into room_designs
	if design.ColorScheme != nil && *design.ColorScheme == "" {
		design.ColorScheme = nil
	}
	if design.Style != nil && *design.Style == "" {
		design.Style = nil
	}
	roomDesignQuery := `
		MERGE INTO design.room_designs
		USING (VALUES (@id, @project_id, @status, @style, @color_scheme, @title, @description))
			AS source (id, project_id, status, style, color_scheme, title, description)
		ON design.room_designs.id = source.id::uuid
		WHEN MATCHED THEN UPDATE SET
			project_id = source.project_id,
			status = source.status,
			style = source.style::style_enum,
			color_scheme = source.color_scheme::color_scheme_enum,
			title = source.title,
			description = source.description,
			updated_at = NOW()
		WHEN NOT MATCHED THEN
			INSERT (id, project_id, status, style, color_scheme, title, description)
			VALUES (source.id::uuid, source.project_id, source.status,
					source.style::style_enum, source.color_scheme::color_scheme_enum,
					source.title, source.description
			)
		`
	_, err = tx.Exec(ctx, roomDesignQuery, pgx.NamedArgs{
		"id":           design.ID,
		"project_id":   design.ProjectID,
		"status":       design.Status,
		"style":        design.Style,
		"color_scheme": design.ColorScheme,
		"title":        design.Title,
		"description":  design.Description,
	})
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to upsert into room_designs", slog.String("error", err.Error()))
		return design.ID, fmt.Errorf("failed to save design: %w", err)
	}

	// 2. Insert into default_products
	productsQuery := `
		INSERT INTO design.default_products (
			room_design_id, floor_tile, floor_tile_pattern, toilet, vanity, faucet, mirror,
			lighting, paint, shelving, wall_tile_placement, wall_tile, wall_tile_pattern,
			wallpaper_placement, wallpaper, shower_system, shower_floor_tile,
			shower_floor_tile_pattern, shower_wall_tile, shower_wall_tile_pattern,
			shower_short_wall_tile, shower_glass, niche_tile, tub, tub_filler, tub_door
		) VALUES (
			@room_design_id, @floor_tile, @floor_tile_pattern, @toilet, @vanity, @faucet, @mirror,
			@lighting, @paint, @shelving, @wall_tile_placement, @wall_tile, @wall_tile_pattern,
			@wallpaper_placement, @wallpaper, @shower_system, @shower_floor_tile,
			@shower_floor_tile_pattern, @shower_wall_tile, @shower_wall_tile_pattern,
			@shower_short_wall_tile, @shower_glass, @niche_tile, @tub, @tub_filler, @tub_door
		)
		ON CONFLICT (room_design_id) DO UPDATE SET
			floor_tile = EXCLUDED.floor_tile, floor_tile_pattern = EXCLUDED.floor_tile_pattern,
			toilet = EXCLUDED.toilet, paint = EXCLUDED.paint,
			vanity = EXCLUDED.vanity, faucet = EXCLUDED.faucet, mirror = EXCLUDED.mirror,
			lighting = EXCLUDED.lighting, shelving = EXCLUDED.shelving,
			wall_tile_placement = EXCLUDED.wall_tile_placement,
			wall_tile = EXCLUDED.wall_tile, wall_tile_pattern = EXCLUDED.wall_tile_pattern,
			wallpaper_placement = EXCLUDED.wallpaper_placement, wallpaper = EXCLUDED.wallpaper,
			shower_floor_tile = EXCLUDED.shower_floor_tile,
			shower_floor_tile_pattern = EXCLUDED.shower_floor_tile_pattern,
			shower_wall_tile = EXCLUDED.shower_wall_tile,
			shower_wall_tile_pattern = EXCLUDED.shower_wall_tile_pattern,
			shower_short_wall_tile = EXCLUDED.shower_short_wall_tile,
			niche_tile = EXCLUDED.niche_tile,
			shower_system = EXCLUDED.shower_system, shower_glass = EXCLUDED.shower_glass,
			tub = EXCLUDED.tub, tub_filler = EXCLUDED.tub_filler, tub_door = EXCLUDED.tub_door
		`
	_, err = tx.Exec(ctx, productsQuery, pgx.NamedArgs{
		"room_design_id":            design.ID,
		"floor_tile":                design.FloorTile,
		"floor_tile_pattern":        design.FloorTilePattern,
		"toilet":                    design.Toilet,
		"vanity":                    design.Vanity,
		"faucet":                    design.Faucet,
		"mirror":                    design.Mirror,
		"lighting":                  design.Lighting,
		"paint":                     design.Paint,
		"shelving":                  design.Shelving,
		"wall_tile_placement":       design.WallTilePlacement,
		"wall_tile":                 design.WallTile,
		"wall_tile_pattern":         design.WallTilePattern,
		"wallpaper_placement":       design.WallpaperPlacement,
		"wallpaper":                 design.Wallpaper,
		"shower_system":             design.ShowerSystem,
		"shower_floor_tile":         design.ShowerFloorTile,
		"shower_floor_tile_pattern": design.ShowerFloorTilePattern,
		"shower_wall_tile":          design.ShowerWallTile,
		"shower_wall_tile_pattern":  design.ShowerWallTilePattern,
		"shower_short_wall_tile":    design.ShowerShortWallTile,
		"shower_glass":              design.ShowerGlass,
		"niche_tile":                design.NicheTile,
		"tub":                       design.Tub,
		"tub_filler":                design.TubFiller,
		"tub_door":                  design.TubDoor,
	})
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to upsert into default_products", slog.String("error", err.Error()))
		return design.ID, fmt.Errorf("failed to save product selections for design: %w", err)
	}

	// 3. Insert into render_prefs
	prefsQuery := `
		INSERT INTO design.render_prefs (
		    room_design_id, shower_glass_visible, tub_door_visible, niches_visible
		)
		VALUES (@room_design_id, @shower_glass_visible, @tub_door_visible, @niches_visible)
		ON CONFLICT (room_design_id)
		DO UPDATE SET
		    shower_glass_visible = EXCLUDED.shower_glass_visible,
		    tub_door_visible = EXCLUDED.tub_door_visible,
		    niches_visible = EXCLUDED.niches_visible
		`
	_, err = tx.Exec(ctx, prefsQuery, pgx.NamedArgs{
		"room_design_id":       design.ID,
		"shower_glass_visible": design.ShowerGlassVisible,
		"tub_door_visible":     design.TubDoorVisible,
		"niches_visible":       design.NichesVisible,
	})
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to upsert into render_prefs", slog.String("error", err.Error()))
		return design.ID, fmt.Errorf("failed to save rendering preferences for design: %w", err)
	}

	// 4. Insert into retail_info
	if design.TotalPriceInCents.Int32 > 0 || design.LeadTimeDays.Int32 > 0 || design.NumSKUs.Int32 > 0 {
		retailInfoQuery := `
		INSERT INTO design.retail_info (
			room_design_id, total_price_cents, lead_time_days, sku_count
		)
		VALUES (@room_design_id, @total_price_cents, @lead_time_days, @sku_count)
		ON CONFLICT (room_design_id)
		DO UPDATE SET
			total_price_cents = EXCLUDED.total_price_cents,
			lead_time_days = EXCLUDED.lead_time_days,
			sku_count = EXCLUDED.sku_count
		`
		_, err = tx.Exec(ctx, retailInfoQuery, pgx.NamedArgs{
			"room_design_id":    design.ID,
			"total_price_cents": design.TotalPriceInCents,
			"lead_time_days":    design.LeadTimeDays,
			"sku_count":         design.NumSKUs,
		})
		if err != nil {
			r.logger.Error("Failed to upsert into retail_info", "err", err.Error(),
				"projectID", design.ProjectID, "designID", design.ID,
				"price", design.TotalPriceInCents, "leadTime", design.LeadTimeDays, "skuCount", design.NumSKUs)
			return design.ID, fmt.Errorf("failed to save retail info for design: %w", err)
		}
	}

	if err = tx.Commit(ctx); err != nil {
		r.logger.ErrorContext(ctx, "Failed to commit transaction", slog.String("error", err.Error()))
		return design.ID, fmt.Errorf("failed to commit transaction: %w", err)
	}
	return design.ID, nil
}

// ReadDesign fetches a single Design entity from the database by its ID.
// It joins all related tables to construct the complete Design object.
func (r *RelationalDb) ReadDesign(ctx context.Context, id uuid.UUID) (usecases.Design, error) {
	query := fmt.Sprintf("%s WHERE rd.id = $1", readDesignBaseQuery)
	row := r.db.QueryRow(ctx, query, id)
	if usecases.IsNil(row) {
		r.logger.ErrorContext(ctx, "Failed to find design by ID", slog.String("designID", id.String()))
		return usecases.Design{}, usecases.ErrNotFound
	}
	design, err := scanDesign(row)
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to scan design row",
			slog.String("error", err.Error()), slog.String("designID", id.String()))
		return usecases.Design{}, err
	}

	// Retrieve renditions associated with the design
	renditions, err := r.RenditionsForDesign(ctx, id)
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to read renditions for design",
			slog.String("error", err.Error()), slog.String("designID", id.String()))
		// TODO: consider returning an error here.
		return design, nil
	}
	design.Renditions = renditions

	// Retrieve cart inclusions associated with the design
	cartInclusions, err := r.CartInclusionsForDesign(ctx, design.ID)
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to load cart inclusions for design",
			slog.String("error", err.Error()), slog.String("designID", id.String()))
		// Don't fail the entire request if cart loading fails; use an empty map instead.
		cartInclusions = make(usecases.CartInclusions)
	}
	design.CartInclusions = cartInclusions

	return design, nil
}

// DesignsForProject retrieves all designs associated with a given project ID.
func (r *RelationalDb) DesignsForProject(ctx context.Context, projectID entities.ProjectId) ([]usecases.Design, error) {
	query := fmt.Sprintf(`%s WHERE rd.project_id = $1 AND rd.status != 'Archived'
		ORDER BY rd.updated_at DESC`, readDesignBaseQuery)
	rows, err := r.db.Query(ctx, query, projectID)
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to query designs by project id",
			slog.String("error", err.Error()), slog.String("projectID", string(projectID)))
		return nil, fmt.Errorf("failed to query designs by project id: %w", err)
	}
	defer rows.Close()

	var designs []usecases.Design
	for rows.Next() {
		design, err := scanDesign(rows)
		if err != nil {
			return nil, err
		}
		designs = append(designs, design)
	}

	if err = rows.Err(); err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			return designs, usecases.ErrNotFound
		}
		r.logger.ErrorContext(ctx, "Error during rows iteration", slog.String("error", err.Error()))
		return nil, fmt.Errorf("error during rows iteration: %w", err)
	}
	return designs, nil
}

// ProjectIdForDesign retrieves the project ID associated with a given design ID.
func (r *RelationalDb) ProjectIdForDesign(ctx context.Context, designId uuid.UUID) (entities.ProjectId, error) {
	query := "SELECT project_id FROM design.room_designs WHERE id = $1"
	row := r.db.QueryRow(ctx, query, designId)

	var projectId entities.ProjectId
	err := row.Scan(&projectId)
	if err != nil {
		if errors.Is(err, pgx.ErrNoRows) {
			r.logger.ErrorContext(ctx, "Could not find project for design",
				slog.String("designID", designId.String()), slog.String("error", err.Error()))
			return "", usecases.ErrNotFound
		}
		r.logger.ErrorContext(ctx, "Unable to parse project ID for design",
			slog.String("designID", designId.String()), slog.String("error", err.Error()))
		return "", fmt.Errorf("failed to query project id for design %s: %w", designId, err)
	}

	return projectId, nil
}

// DesignsByProject retrieves all designs associated with the given project IDs.
func (r *RelationalDb) DesignsByProject(ctx context.Context, projectIDs []entities.ProjectId) (map[entities.ProjectId][]usecases.Design, []error, error) {
	var errors []error
	projectDesigns := make(map[entities.ProjectId][]usecases.Design)
	query := fmt.Sprintf(`%s WHERE rd.project_id = ANY($1) AND rd.status != 'Archived'
		ORDER BY rd.project_id, rd.updated_at DESC`,
		readDesignBaseQuery)
	rows, err := r.db.Query(ctx, query, projectIDs)
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to query designs by project ids",
			slog.String("error", err.Error()), slog.String("projectIDs", fmt.Sprintf("%v", projectIDs)))
		return nil, nil, fmt.Errorf("failed to query designs by project ids (%v): %w", projectIDs, err)
	}
	defer rows.Close()

	for rows.Next() {
		design, err := scanDesign(rows)
		if err != nil {
			r.logger.ErrorContext(ctx, "Failed to scan design row", slog.String("error", err.Error()))
			errors = append(errors, err)
			continue
		}
		designs := projectDesigns[design.ProjectID]
		designs = append(designs, design)
		projectDesigns[design.ProjectID] = designs
	}
	if err = rows.Err(); err != nil {
		r.logger.ErrorContext(ctx, "Error during rows iteration", slog.String("error", err.Error()))
		return nil, nil, fmt.Errorf("error during rows iteration: %w", err)
	}

	if len(projectDesigns) < len(projectIDs) {
		r.logger.DebugContext(ctx, "Warning: Some projects have no designs.", "projectIds", projectIDs)
	}
	return projectDesigns, errors, nil
}

func (r *RelationalDb) DeleteDesignsForProjectExceptSpecified(ctx context.Context, projectId entities.ProjectId, designIdsToKeep []uuid.UUID) error {
	query := `UPDATE design.room_designs SET status = 'Archived' WHERE project_id = $1 AND id != ALL($2)`

	cmdTag, err := r.db.Exec(ctx, query, projectId, designIdsToKeep)
	if err != nil {
		return fmt.Errorf("failed to archive designs: %w", err)
	}

	if cmdTag.RowsAffected() == 0 {
		r.logger.DebugContext(ctx, "No designs found to archive for project.", slog.String("projectId", string(projectId)))
	}
	r.logger.InfoContext(ctx, "Archived orphaned designs for project",
		slog.String("projectId", string(projectId)), slog.Int("count", int(cmdTag.RowsAffected())))

	return nil
}

// DeleteDesign archives a Design entity.
func (r *RelationalDb) DeleteDesign(ctx context.Context, id uuid.UUID) error {
	query := `UPDATE design.room_designs SET status = 'Archived' WHERE id = $1`

	cmdTag, err := r.db.Exec(ctx, query, id)
	if err != nil {
		r.logger.ErrorContext(ctx, "Failed to archive design",
			slog.String("error", err.Error()), slog.String("designId", id.String()))
		return fmt.Errorf("failed to archive design: %w", err)
	}

	if cmdTag.RowsAffected() == 0 {
		r.logger.ErrorContext(ctx, "No design found to archive", slog.String("designId", id.String()))
		return usecases.ErrNotFound
	}

	return nil
}
