//go:build integration

package gateways_test

import (
	"context"
	"database/sql"
	"log"
	"net/url"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters/gateways"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

// TestRelationalDb_Integration_InsertPreset tests the InsertPreset method
// against a live database to ensure it correctly creates all related records.
func TestRelationalDb_Integration_InsertPreset(t *testing.T) {
	// Arrange
	ctx := context.Background()
	db := gateways.NewRelationalDb(testPool, nil)
	legacyId := "42"

	// Ensure the database is clean before this test runs.
	_, err := testPool.Exec(ctx, "TRUNCATE TABLE design_pkg.packages CASCADE;")
	require.NoError(t, err, "Failed to truncate packages table before test")
	_, err = testPool.Exec(ctx, "TRUNCATE TABLE design.room_designs CASCADE;")
	require.NoError(t, err, "Failed to truncate room_designs table before test")

	// First create a design package that the preset can reference
	testDesignPkg := usecases.DesignPackage{
		Tagged: usecases.Tagged{
			ColorScheme: usecases.Neutral,
			Style:       usecases.Modern,
		},
		Name:            "Test Design Package for Preset",
		Description:     "A test design package for preset testing",
		ImageURL:        url.URL{Scheme: "https", Host: "example.com", Path: "test.jpg"},
		Inspiration:     "Modern design",
		Atmosphere:      []string{"calm"},
		ColorPalette:    []string{"white"},
		MaterialPalette: []string{"marble"},
		HighlightedBrandUrls: []url.URL{
			{Scheme: "https", Host: "example.com", Path: "highlight1.jpg"},
			{Scheme: "https", Host: "example.com", Path: "highlight2.jpg"},
		},
		FixedProductSelections: usecases.FixedProductSelections{
			FloorTile:       ptr(uuid.New()),
			Lighting:        ptr(uuid.New()),
			Mirror:          ptr(uuid.New()),
			Paint:           ptr(uuid.New()),
			Shelving:        ptr(uuid.New()),
			Toilet:          ptr(uuid.New()),
			ShowerFloorTile: ptr(uuid.New()),
			ShowerWallTile:  ptr(uuid.New()),
			TubFiller:       ptr(uuid.New()),
			WallTile:        ptr(uuid.New()),
		},
		ProductSelectionOptions: usecases.ProductSelectionOptions{
			AlcoveTub:          uuid.New(),
			FreestandingTub:    uuid.New(),
			ShowerGlassFixed:   uuid.New(),
			ShowerGlassSliding: uuid.New(),
			ShowerSystemCombo:  uuid.New(),
			ShowerSystemSolo:   uuid.New(),
			TubDoorFixed:       uuid.New(),
			TubDoorSliding:     uuid.New(),
		},
		WallTilePlacement:  usecases.HalfWall,
		WallpaperPlacement: usecases.NoWallpaper,
	}

	designPkgID, err := db.InsertDesignPackage(ctx, testDesignPkg, legacyId)
	require.NoError(t, err, "Failed to create test design package")

	testPreset := usecases.Preset{
		Id:          legacyId,
		DesignPkgId: designPkgID,
		Design: usecases.Design{
			ID:                 uuid.New(),
			ProjectID:          "PRJ-PRESET-TEST",
			Status:             usecases.Preview,
			WallpaperPlacement: usecases.NoWallpaper,
			WallTilePlacement:  usecases.HalfWall,
			DesignOptions: usecases.DesignOptions{
				Title:            sql.NullString{String: "Test Preset Design", Valid: true},
				Description:      sql.NullString{String: "A test preset design", Valid: true},
				ColorScheme:      ptr(usecases.Neutral),
				Style:            ptr(usecases.Modern),
				FloorTilePattern: ptr(usecases.Herringbone),
				FixedProductSelections: usecases.FixedProductSelections{
					FloorTile: ptr(uuid.New()),
					Lighting:  ptr(uuid.New()),
					Mirror:    ptr(uuid.New()),
					Paint:     ptr(uuid.New()),
					Toilet:    ptr(uuid.New()),
				},
				Faucet:            ptr(uuid.New()),
				Vanity:            ptr(uuid.New()),
				NumSKUs:           sql.NullInt32{Int32: 10, Valid: true},
				TotalPriceInCents: sql.NullInt32{Int32: 1000, Valid: true},
				LeadTimeDays:      sql.NullInt32{Int32: 30, Valid: true},
			},
			ShowerGlassVisible: true,
			TubDoorVisible:     false,
			NichesVisible:      true,
		},
		Rendition: entities.Rendition{
			Id:     uuid.New(),
			Status: entities.RenditionPending,
			URL:    &url.URL{Scheme: "https", Host: "example.com", Path: "/image.webp"},
		},
	}

	// Act - Insert the preset
	err = db.InsertPreset(ctx, testPreset)
	if err != nil {
		log.Printf("Failed to insert preset with rendition image URL %v: %v",
			testPreset.Rendition.URL.String(), err)
	}
	require.NoError(t, err, "InsertPreset failed")

	// Assert - Read the preset back to verify it was created correctly
	readPreset, err := db.FindPresetByLegacyId(ctx, testPreset.Id)
	require.NoError(t, err, "ReadPreset failed after insert")
	require.NotNil(t, readPreset)

	// Verify preset fields
	assert.Equal(t, testPreset.Id, readPreset.Id)

	// Verify design fields
	assert.NotEqual(t, uuid.Nil, readPreset.Design.ID)
	testPreset.Design.Created = readPreset.Design.Created
	testPreset.Design.LastUpdated = readPreset.Design.LastUpdated
	testPreset.Design.CartInclusions = make(usecases.CartInclusions)
	assert.Equal(t, testPreset.Design, readPreset.Design)

	// Verify rendition fields
	assert.NotEqual(t, uuid.Nil, readPreset.Rendition.Id)
	testPreset.Rendition.CreatedAt = readPreset.Rendition.CreatedAt
	testPreset.Rendition.UpdatedAt = readPreset.Rendition.UpdatedAt
	assert.Equal(t, testPreset.Rendition, readPreset.Rendition)
}

// TestRelationalDb_Integration_LegacyIdMapping tests the LegacyIdMapping method
// against a live database to ensure it correctly retrieves the mapping of design UUIDs to legacy IDs.
func TestRelationalDb_Integration_LegacyIdMapping(t *testing.T) {
	// Arrange
	ctx := context.Background()
	db := gateways.NewRelationalDb(testPool, nil)

	// Ensure the database is clean before this test runs.
	_, err := testPool.Exec(ctx, "TRUNCATE TABLE design_pkg.packages CASCADE;")
	require.NoError(t, err, "Failed to truncate packages table before test")
	_, err = testPool.Exec(ctx, "TRUNCATE TABLE design.room_designs CASCADE;")
	require.NoError(t, err, "Failed to truncate room_designs table before test")

	// Create test design packages and presets with legacy IDs
	legacyId1 := "01"
	legacyId2 := "02"

	// Create first design package
	designPkg1 := usecases.DesignPackage{
		Tagged: usecases.Tagged{
			ColorScheme: usecases.Neutral,
			Style:       usecases.Modern,
		},
		Name:            "Legacy Test Design Package 1",
		Description:     "A test design package for legacy mapping",
		ImageURL:        url.URL{Scheme: "https", Host: "example.com", Path: "/designPkg1.jpg"},
		Inspiration:     "Modern design",
		Atmosphere:      []string{"calm"},
		ColorPalette:    []string{"white"},
		MaterialPalette: []string{"marble"},
		HighlightedBrandUrls: []url.URL{
			{Scheme: "https", Host: "example.com", Path: "/highlight1.jpg"},
		},
		FixedProductSelections: usecases.FixedProductSelections{
			FloorTile:       ptr(uuid.New()),
			Lighting:        ptr(uuid.New()),
			Mirror:          ptr(uuid.New()),
			Paint:           ptr(uuid.New()),
			Shelving:        ptr(uuid.New()),
			Toilet:          ptr(uuid.New()),
			ShowerFloorTile: ptr(uuid.New()),
			ShowerWallTile:  ptr(uuid.New()),
			TubFiller:       ptr(uuid.New()),
			WallTile:        ptr(uuid.New()),
		},
		ProductSelectionOptions: usecases.ProductSelectionOptions{
			AlcoveTub:          uuid.New(),
			FreestandingTub:    uuid.New(),
			ShowerGlassFixed:   uuid.New(),
			ShowerGlassSliding: uuid.New(),
		},
		WallTilePlacement:  usecases.HalfWall,
		WallpaperPlacement: usecases.NoWallpaper,
	}

	designPkgID1, err := db.InsertDesignPackage(ctx, designPkg1, legacyId1)
	require.NoError(t, err, "Failed to create test design package 1")

	// Create second design package
	designPkg2 := usecases.DesignPackage{
		Tagged: usecases.Tagged{
			ColorScheme: usecases.Bold,
			Style:       usecases.Traditional,
		},
		Name:            "Legacy Test Design Package 2",
		Description:     "Another test design package for legacy mapping",
		ImageURL:        url.URL{Scheme: "https", Host: "example.com", Path: "/designPkg2.jpg"},
		Inspiration:     "Traditional design",
		Atmosphere:      []string{"warm"},
		ColorPalette:    []string{"beige"},
		MaterialPalette: []string{"wood"},
		HighlightedBrandUrls: []url.URL{
			{Scheme: "https", Host: "example.com", Path: "/highlight2.jpg"},
		},
		FixedProductSelections: usecases.FixedProductSelections{
			FloorTile:       ptr(uuid.New()),
			Lighting:        ptr(uuid.New()),
			Mirror:          ptr(uuid.New()),
			Paint:           ptr(uuid.New()),
			Shelving:        ptr(uuid.New()),
			Toilet:          ptr(uuid.New()),
			ShowerFloorTile: ptr(uuid.New()),
			ShowerWallTile:  ptr(uuid.New()),
			TubFiller:       ptr(uuid.New()),
			WallTile:        ptr(uuid.New()),
		},
		ProductSelectionOptions: usecases.ProductSelectionOptions{
			AlcoveTub:          uuid.New(),
			FreestandingTub:    uuid.New(),
			ShowerGlassFixed:   uuid.New(),
			ShowerGlassSliding: uuid.New(),
		},
		WallTilePlacement:  usecases.FullWall,
		WallpaperPlacement: usecases.VanityWall,
	}

	designPkgID2, err := db.InsertDesignPackage(ctx, designPkg2, legacyId2)
	require.NoError(t, err, "Failed to create test design package 2")

	// Create presets for both design packages
	preset1 := usecases.Preset{
		Id:          legacyId1,
		DesignPkgId: designPkgID1,
		Design: usecases.Design{
			ID:                 uuid.New(),
			ProjectID:          "PRJ-LEGACY-TEST-1",
			Status:             usecases.Preview,
			WallpaperPlacement: usecases.NoWallpaper,
			WallTilePlacement:  usecases.HalfWall,
			DesignOptions: usecases.DesignOptions{
				Title:            sql.NullString{String: "Legacy Test Design 1", Valid: true},
				ColorScheme:      ptr(usecases.Neutral),
				Style:            ptr(usecases.Modern),
				FloorTilePattern: ptr(usecases.Herringbone),
				FixedProductSelections: usecases.FixedProductSelections{
					FloorTile: ptr(uuid.New()),
					Lighting:  ptr(uuid.New()),
					Mirror:    ptr(uuid.New()),
					Paint:     ptr(uuid.New()),
					Toilet:    ptr(uuid.New()),
				},
				Faucet: ptr(uuid.New()),
				Vanity: ptr(uuid.New()),
			},
		},
		Rendition: entities.Rendition{
			Id:     uuid.New(),
			Status: entities.RenditionCompleted,
			URL:    &url.URL{Scheme: "https", Host: "example.com", Path: "/preset1.webp"},
		},
	}

	preset2 := usecases.Preset{
		Id:          legacyId2,
		DesignPkgId: designPkgID2,
		Design: usecases.Design{
			ID:                 uuid.New(),
			ProjectID:          "PRJ-LEGACY-TEST-2",
			Status:             usecases.Preview,
			WallpaperPlacement: usecases.VanityWall,
			WallTilePlacement:  usecases.FullWall,
			DesignOptions: usecases.DesignOptions{
				Title:            sql.NullString{String: "Legacy Test Design 2", Valid: true},
				ColorScheme:      ptr(usecases.Bold),
				Style:            ptr(usecases.Traditional),
				FloorTilePattern: ptr(usecases.VerticalStacked),
				FixedProductSelections: usecases.FixedProductSelections{
					FloorTile: ptr(uuid.New()),
					Lighting:  ptr(uuid.New()),
					Mirror:    ptr(uuid.New()),
					Paint:     ptr(uuid.New()),
					Toilet:    ptr(uuid.New()),
				},
				Faucet: ptr(uuid.New()),
				Vanity: ptr(uuid.New()),
			},
		},
		Rendition: entities.Rendition{
			Id:     uuid.New(),
			Status: entities.RenditionCompleted,
			URL:    &url.URL{Scheme: "https", Host: "example.com", Path: "/preset2.webp"},
		},
	}

	// Insert both presets
	err = db.InsertPreset(ctx, preset1)
	require.NoError(t, err, "Failed to insert preset 1")

	err = db.InsertPreset(ctx, preset2)
	require.NoError(t, err, "Failed to insert preset 2")

	// Act - Get the legacy ID mapping
	mapping, err := db.LegacyIdMapping(ctx)
	require.NoError(t, err, "LegacyIdMapping should succeed")

	// Assert - Verify the mapping contains both designs
	require.Len(t, mapping, 2, "Should have 2 entries in the mapping")

	// Verify the mapping contains the correct design IDs and legacy IDs
	assert.Contains(t, mapping, preset1.Design.ID, "Mapping should contain design 1 ID")
	assert.Contains(t, mapping, preset2.Design.ID, "Mapping should contain design 2 ID")
	assert.Equal(t, legacyId1, mapping[preset1.Design.ID], "Design 1 should map to legacy ID 1")
	assert.Equal(t, legacyId2, mapping[preset2.Design.ID], "Design 2 should map to legacy ID 2")

	// Test edge case: empty database
	_, err = testPool.Exec(ctx, "TRUNCATE TABLE design_pkg.packages CASCADE;")
	require.NoError(t, err, "Failed to truncate tables for edge case test")

	emptyMapping, err := db.LegacyIdMapping(ctx)
	require.Error(t, err, "LegacyIdMapping should return error when no legacy IDs exist")
	assert.ErrorIs(t, err, usecases.ErrNotFound, "Should return ErrNotFound when no legacy IDs exist")
	assert.Nil(t, emptyMapping, "Empty database should return nil mapping")
}

// TestRelationalDb_Integration_ReadDesignPackageByLegacyId tests the ReadDesignPackageByLegacyId method
// against a live database to ensure it correctly retrieves design packages by their legacy IDs.
func TestRelationalDb_Integration_ReadDesignPackageByLegacyId(t *testing.T) {
	// Arrange
	ctx := context.Background()
	db := gateways.NewRelationalDb(testPool, nil)

	// Ensure the database is clean before this test runs.
	_, err := testPool.Exec(ctx, "TRUNCATE TABLE design_pkg.packages CASCADE;")
	require.NoError(t, err, "Failed to truncate packages table before test")

	legacyId := "99"
	testDesignPkg := usecases.DesignPackage{
		Tagged: usecases.Tagged{
			ColorScheme: usecases.Neutral,
			Style:       usecases.Modern,
		},
		Name:            "Legacy ID Test Design Package",
		Description:     "A test design package for legacy ID lookup",
		ImageURL:        url.URL{Scheme: "https", Host: "example.com", Path: "/legacy-test.jpg"},
		Inspiration:     "Modern minimalist design for legacy testing",
		Atmosphere:      []string{"calm", "serene", "modern"},
		ColorPalette:    []string{"white", "gray", "black"},
		MaterialPalette: []string{"marble", "wood", "metal"},
		HighlightedBrandUrls: []url.URL{
			{Scheme: "https", Host: "example.com", Path: "/legacy-highlight1.jpg"},
			{Scheme: "https", Host: "example.com", Path: "/legacy-highlight2.jpg"},
		},
		FixedProductSelections: usecases.FixedProductSelections{
			FloorTile:       ptr(uuid.New()),
			Lighting:        ptr(uuid.New()),
			Mirror:          ptr(uuid.New()),
			Paint:           ptr(uuid.New()),
			Shelving:        ptr(uuid.New()),
			Toilet:          ptr(uuid.New()),
			ShowerFloorTile: ptr(uuid.New()),
			ShowerWallTile:  ptr(uuid.New()),
			TubFiller:       ptr(uuid.New()),
			WallTile:        ptr(uuid.New()),
			Wallpaper:       ptr(uuid.New()),
		},
		ProductSelectionOptions: usecases.ProductSelectionOptions{
			AlcoveTub:          uuid.New(),
			FreestandingTub:    uuid.New(),
			ShowerGlassFixed:   uuid.New(),
			ShowerGlassSliding: uuid.New(),
			ShowerSystemCombo:  uuid.New(),
			ShowerSystemSolo:   uuid.New(),
			TubDoorFixed:       uuid.New(),
			TubDoorSliding:     uuid.New(),
		},
		DesignPackageProvenance: usecases.DesignPackageProvenance{
			LightingBrand: ptr("Legacy Test Lighting Brand"),
			PlumbingBrand: ptr("Legacy Test Plumbing Brand"),
			ToiletBrand:   ptr("Legacy Test Toilet Brand"),
			VanityBrand:   ptr("Legacy Test Vanity Brand"),
			VanityStorage: ptr("Legacy Test Storage"),
		},
		WallTilePlacement:  usecases.HalfWall,
		WallpaperPlacement: usecases.VanityWall,
		VanityScalingOptions: map[int]usecases.VanityScalingOption{
			36: {
				VanityProductID: uuid.New(),
				FaucetProductID: uuid.New(),
			},
			48: {
				VanityProductID: uuid.New(),
				FaucetProductID: uuid.New(),
			},
			60: {
				VanityProductID: uuid.New(),
				FaucetProductID: uuid.New(),
			},
		},
	}

	// Act - Insert the design package with a legacy ID
	designPkgID, err := db.InsertDesignPackage(ctx, testDesignPkg, legacyId)
	require.NoError(t, err, "InsertDesignPackage should succeed")
	require.NotEqual(t, uuid.Nil, designPkgID, "InsertDesignPackage should return a valid UUID")

	// Act - Read the design package by legacy ID
	readDesignPkg, err := db.ReadDesignPackageByLegacyId(ctx, legacyId)
	require.NoError(t, err, "ReadDesignPackageByLegacyId should succeed")

	// Assert - Verify design package fields match
	assert.Equal(t, designPkgID, readDesignPkg.ID, "Design package ID should match")
	assert.Equal(t, legacyId, *readDesignPkg.LegacyId, "Legacy ID should match")
	assert.Equal(t, testDesignPkg.Name, readDesignPkg.Name, "Design package name should match")
	assert.Equal(t, testDesignPkg.Description, readDesignPkg.Description, "Design package description should match")
	assert.Equal(t, testDesignPkg.ColorScheme, readDesignPkg.ColorScheme, "Color scheme should match")
	assert.Equal(t, testDesignPkg.Style, readDesignPkg.Style, "Style should match")
	assert.Equal(t, testDesignPkg.ImageURL.String(), readDesignPkg.ImageURL.String(), "Image URL should match")
	assert.Equal(t, testDesignPkg.Inspiration, readDesignPkg.Inspiration, "Inspiration should match")
	assert.Equal(t, testDesignPkg.Atmosphere, readDesignPkg.Atmosphere, "Atmosphere should match")
	assert.Equal(t, testDesignPkg.ColorPalette, readDesignPkg.ColorPalette, "Color palette should match")
	assert.Equal(t, testDesignPkg.MaterialPalette, readDesignPkg.MaterialPalette, "Material palette should match")
	assert.Equal(t, testDesignPkg.WallTilePlacement, readDesignPkg.WallTilePlacement, "Wall tile placement should match")
	assert.Equal(t, testDesignPkg.WallpaperPlacement, readDesignPkg.WallpaperPlacement, "Wallpaper placement should match")

	// Verify vanity scaling options
	assert.Len(t, readDesignPkg.VanityScalingOptions, 3, "Should have 3 vanity scaling options")
	assert.Equal(t, testDesignPkg.VanityScalingOptions[36], readDesignPkg.VanityScalingOptions[36], "36-inch option should match")
	assert.Equal(t, testDesignPkg.VanityScalingOptions[48], readDesignPkg.VanityScalingOptions[48], "48-inch option should match")
	assert.Equal(t, testDesignPkg.VanityScalingOptions[60], readDesignPkg.VanityScalingOptions[60], "60-inch option should match")

	// Verify highlighted brand URLs
	assert.Len(t, readDesignPkg.HighlightedBrandUrls, 2, "Should have 2 highlighted brand URLs")
	assert.Equal(t, testDesignPkg.HighlightedBrandUrls[0].String(), readDesignPkg.HighlightedBrandUrls[0].String(), "First highlighted URL should match")
	assert.Equal(t, testDesignPkg.HighlightedBrandUrls[1].String(), readDesignPkg.HighlightedBrandUrls[1].String(), "Second highlighted URL should match")

	// Test consistency with ReadDesignPackage by UUID
	readByUUID, err := db.ReadDesignPackage(ctx, designPkgID)
	require.NoError(t, err, "ReadDesignPackage by UUID should succeed")
	assert.Equal(t, readDesignPkg, readByUUID, "ReadDesignPackageByLegacyId and ReadDesignPackage should return identical results")

	// Test error case: non-existent legacy ID
	_, err = db.ReadDesignPackageByLegacyId(ctx, "non-existent")
	require.Error(t, err, "ReadDesignPackageByLegacyId with non-existent ID should fail")
	assert.Contains(t, err.Error(), "not found", "Error should indicate design package not found")
}
