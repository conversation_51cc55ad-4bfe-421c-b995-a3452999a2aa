package gateways

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

func TestNewMonolith(t *testing.T) {
	t.Run("creates monolith with hostname", func(t *testing.T) {
		monolith := NewMonolith("example.com")
		assert.NotNil(t, monolith)
		assert.Equal(t, "example.com", monolith.hostname)
	})
}

func TestMonolith_UpdateCurrentDesignIdForProject(t *testing.T) {
	projectId := entities.NewProjectId("test-project-123")
	designId := uuid.New()

	t.Run("successful request", func(t *testing.T) {
		// Create a TLS mock server
		server := httptest.NewTLSServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Verify the request
			expectedPath := fmt.Sprintf("/v2/projects/%s", projectId.String())
			assert.Equal(t, expectedPath, r.URL.Path)
			assert.Equal(t, "PUT", r.Method)
			assert.Equal(t, "application/json", r.Header.Get("Content-Type"))

			// Verify the request body
			var payload map[string]any
			err := json.NewDecoder(r.Body).Decode(&payload)
			require.NoError(t, err)

			products, ok := payload["products"].(map[string]any)
			require.True(t, ok, "Expected 'products' field in payload")
			currentDesignId, ok := products["currentDesignId"].(string)
			require.True(t, ok, "Expected 'currentDesignId' field in products")
			assert.Equal(t, designId.String(), currentDesignId)

			// Return success response
			w.WriteHeader(http.StatusOK)
		}))
		defer server.Close()

		// Extract hostname from server URL (remove https://)
		hostname := server.URL[8:] // Remove "https://"
		monolith := NewMonolith(hostname)

		// Use the test server's client which accepts self-signed certificates
		originalClient := http.DefaultClient
		http.DefaultClient = server.Client()
		defer func() { http.DefaultClient = originalClient }()

		err := monolith.UpdateCurrentDesignIdForProject(context.Background(), projectId, designId)
		require.NoError(t, err)
	})

	t.Run("HTTP error response", func(t *testing.T) {
		// Create a server that returns an error
		server := httptest.NewTLSServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusInternalServerError)
		}))
		defer server.Close()

		hostname := server.URL[8:]
		monolith := NewMonolith(hostname)

		// Use the test server's client
		originalClient := http.DefaultClient
		http.DefaultClient = server.Client()
		defer func() { http.DefaultClient = originalClient }()

		err := monolith.UpdateCurrentDesignIdForProject(context.Background(), projectId, designId)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "monolith refused to update current design Id")
		assert.Contains(t, err.Error(), projectId.String())
		assert.Contains(t, err.Error(), designId.String())
	})

	t.Run("network error", func(t *testing.T) {
		// Use localhost with a closed port to trigger immediate connection error
		monolith := NewMonolith("localhost:1")

		err := monolith.UpdateCurrentDesignIdForProject(context.Background(), projectId, designId)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to execute request")
	})
}

func TestMonolith_UpdateScanAndMeasurementsForProject(t *testing.T) {
	projectId := entities.NewProjectId("test-project-123")
	scanData := json.RawMessage(`{"scan": "data"}`)
	measurementsData := json.RawMessage(`{"measurements": "data"}`)

	t.Run("successful request", func(t *testing.T) {
		// Create a TLS mock server
		server := httptest.NewTLSServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Verify the request
			expectedPath := fmt.Sprintf("/v2/projects/%s", projectId.String())
			assert.Equal(t, expectedPath, r.URL.Path)
			assert.Equal(t, "PUT", r.Method)
			assert.Equal(t, "application/json", r.Header.Get("Content-Type"))

			// Verify the request body
			var payload map[string]any
			err := json.NewDecoder(r.Body).Decode(&payload)
			require.NoError(t, err)

			scan, ok := payload["scan"]
			require.True(t, ok, "Expected 'scan' field in payload")
			measurements, ok := payload["measurements"]
			require.True(t, ok, "Expected 'measurements' field in payload")

			// Verify the scan and measurements data
			scanBytes, err := json.Marshal(scan)
			require.NoError(t, err)
			measurementsBytes, err := json.Marshal(measurements)
			require.NoError(t, err)

			assert.JSONEq(t, string(scanData), string(scanBytes))
			assert.JSONEq(t, string(measurementsData), string(measurementsBytes))

			// Return success response
			w.WriteHeader(http.StatusOK)
		}))
		defer server.Close()

		// Extract hostname from server URL (remove https://)
		hostname := server.URL[8:] // Remove "https://"
		monolith := NewMonolith(hostname)

		// Use the test server's client which accepts self-signed certificates
		originalClient := http.DefaultClient
		http.DefaultClient = server.Client()
		defer func() { http.DefaultClient = originalClient }()

		err := monolith.UpdateScanAndMeasurementsForProject(context.Background(), projectId, scanData, measurementsData)
		require.NoError(t, err)
	})

	t.Run("HTTP error response with body", func(t *testing.T) {
		errorMessage := "Invalid project data"
		server := httptest.NewTLSServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusBadRequest)
			_, err := w.Write([]byte(errorMessage))
			require.NoError(t, err)
		}))
		defer server.Close()

		hostname := server.URL[8:]
		monolith := NewMonolith(hostname)

		// Use the test server's client
		originalClient := http.DefaultClient
		http.DefaultClient = server.Client()
		defer func() { http.DefaultClient = originalClient }()

		err := monolith.UpdateScanAndMeasurementsForProject(context.Background(), projectId, scanData, measurementsData)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "monolith refused to update scan and measurements")
		assert.Contains(t, err.Error(), projectId.String())
		assert.Contains(t, err.Error(), errorMessage)
	})

	t.Run("network error", func(t *testing.T) {
		// Use localhost with a closed port to trigger immediate connection error
		monolith := NewMonolith("localhost:1")

		err := monolith.UpdateScanAndMeasurementsForProject(context.Background(), projectId, scanData, measurementsData)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to execute request")
	})
}

func TestMonolith_GetLayoutForProject(t *testing.T) {
	projectId := entities.NewProjectId("test-project-123")

	t.Run("successful request with scan and measurements", func(t *testing.T) {
		// Create mock room layout data with valid UUIDs
		floorId := uuid.New()
		toiletId := uuid.New()
		showerId := uuid.New()

		mockScanData := adapters.RoomLayout{
			Areas: adapters.Areas{
				Showers: []adapters.Shower{
					{
						BaseElement: adapters.BaseElement{
							Identifier: showerId.String(),
						},
						CurbHeight: 0.5,
					},
				},
			},
			Floors: []adapters.Floor{
				{
					ShapedElement: adapters.ShapedElement{
						BaseElement: adapters.BaseElement{
							Identifier: floorId.String(),
						},
					},
				},
			},
			Toilets: []adapters.Toilet{
				{
					ScalableElement: adapters.ScalableElement{
						BaseElement: adapters.BaseElement{
							Identifier: toiletId.String(),
						},
					},
				},
			},
			Measurements: &adapters.Measurements{
				CeilingArea: func() *float64 { v := 100.5; return &v }(),
			},
		}

		scanBytes, err := json.Marshal(mockScanData)
		require.NoError(t, err)

		measurementsBytes, err := json.Marshal(mockScanData.Measurements)
		require.NoError(t, err)

		// Create a TLS mock server
		server := httptest.NewTLSServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Verify the request
			expectedPath := fmt.Sprintf("/v2/projects/%s", projectId.String())
			assert.Equal(t, expectedPath, r.URL.Path)
			assert.Equal(t, "GET", r.Method)

			// Return mock response
			response := struct {
				Data []struct {
					Scan         json.RawMessage `json:"scan"`
					Measurements json.RawMessage `json:"measurements"`
				} `json:"data"`
			}{
				Data: []struct {
					Scan         json.RawMessage `json:"scan"`
					Measurements json.RawMessage `json:"measurements"`
				}{
					{
						Scan:         scanBytes,
						Measurements: measurementsBytes,
					},
				},
			}

			w.Header().Set("Content-Type", "application/json")
			err := json.NewEncoder(w).Encode(response)
			require.NoError(t, err)
		}))
		defer server.Close()

		// Extract hostname from server URL (remove https://)
		hostname := server.URL[8:] // Remove "https://"
		monolith := NewMonolith(hostname)

		// Use the test server's client which accepts self-signed certificates
		originalClient := http.DefaultClient
		http.DefaultClient = server.Client()
		defer func() { http.DefaultClient = originalClient }()

		result, err := monolith.GetLayoutForProject(context.Background(), projectId)
		require.NoError(t, err)

		// Verify the result
		assert.NotZero(t, result.Hash)
		assert.NotEmpty(t, result.RawData)
		assert.Equal(t, 100.5, result.AreaSqFt)
		assert.Len(t, result.FloorIds, 1)
		assert.Len(t, result.ToiletIds, 1)
		assert.Equal(t, floorId.String(), result.FloorIds[0].String())
		assert.Equal(t, toiletId.String(), result.ToiletIds[0].String())
	})

	t.Run("successful request with scan only (no measurements)", func(t *testing.T) {
		// Create mock room layout data without measurements
		floorId := uuid.New()

		mockScanData := adapters.RoomLayout{
			Floors: []adapters.Floor{
				{
					ShapedElement: adapters.ShapedElement{
						BaseElement: adapters.BaseElement{
							Identifier: floorId.String(),
						},
					},
				},
			},
		}

		scanBytes, err := json.Marshal(mockScanData)
		require.NoError(t, err)

		// Create a TLS mock server
		server := httptest.NewTLSServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Return mock response with null measurements
			response := struct {
				Data []struct {
					Scan         json.RawMessage `json:"scan"`
					Measurements json.RawMessage `json:"measurements"`
				} `json:"data"`
			}{
				Data: []struct {
					Scan         json.RawMessage `json:"scan"`
					Measurements json.RawMessage `json:"measurements"`
				}{
					{
						Scan:         scanBytes,
						Measurements: nil,
					},
				},
			}

			w.Header().Set("Content-Type", "application/json")
			err := json.NewEncoder(w).Encode(response)
			require.NoError(t, err)
		}))
		defer server.Close()

		hostname := server.URL[8:]
		monolith := NewMonolith(hostname)

		originalClient := http.DefaultClient
		http.DefaultClient = server.Client()
		defer func() { http.DefaultClient = originalClient }()

		result, err := monolith.GetLayoutForProject(context.Background(), projectId)
		require.NoError(t, err)

		// Verify the result
		assert.NotZero(t, result.Hash)
		assert.NotEmpty(t, result.RawData)
		assert.Len(t, result.FloorIds, 1)
		assert.Equal(t, floorId.String(), result.FloorIds[0].String())
	})

	t.Run("HTTP error response", func(t *testing.T) {
		errorMessage := "Project not found"
		server := httptest.NewTLSServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.WriteHeader(http.StatusNotFound)
			_, err := w.Write([]byte(errorMessage))
			require.NoError(t, err)
		}))
		defer server.Close()

		hostname := server.URL[8:]
		monolith := NewMonolith(hostname)

		originalClient := http.DefaultClient
		http.DefaultClient = server.Client()
		defer func() { http.DefaultClient = originalClient }()

		_, err := monolith.GetLayoutForProject(context.Background(), projectId)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "monolith refused to return data for project")
		assert.Contains(t, err.Error(), projectId.String())
		assert.Contains(t, err.Error(), "404")
		assert.Contains(t, err.Error(), errorMessage)
	})

	t.Run("empty response data", func(t *testing.T) {
		server := httptest.NewTLSServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			response := struct {
				Data []struct {
					Scan         json.RawMessage `json:"scan"`
					Measurements json.RawMessage `json:"measurements"`
				} `json:"data"`
			}{
				Data: []struct {
					Scan         json.RawMessage `json:"scan"`
					Measurements json.RawMessage `json:"measurements"`
				}{},
			}

			w.Header().Set("Content-Type", "application/json")
			err := json.NewEncoder(w).Encode(response)
			require.NoError(t, err)
		}))
		defer server.Close()

		hostname := server.URL[8:]
		monolith := NewMonolith(hostname)

		originalClient := http.DefaultClient
		http.DefaultClient = server.Client()
		defer func() { http.DefaultClient = originalClient }()

		_, err := monolith.GetLayoutForProject(context.Background(), projectId)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "no data in response")
	})

	t.Run("invalid scan data", func(t *testing.T) {
		server := httptest.NewTLSServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Send response with invalid scan data (valid JSON but invalid structure)
			_, err := w.Write([]byte(`{"data":[{"scan":"not a valid room layout","measurements":null}]}`))
			require.NoError(t, err)
		}))
		defer server.Close()

		hostname := server.URL[8:]
		monolith := NewMonolith(hostname)

		originalClient := http.DefaultClient
		http.DefaultClient = server.Client()
		defer func() { http.DefaultClient = originalClient }()

		_, err := monolith.GetLayoutForProject(context.Background(), projectId)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to unmarshal scan data")
	})

	t.Run("invalid JSON response", func(t *testing.T) {
		server := httptest.NewTLSServer(http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			w.Header().Set("Content-Type", "application/json")
			_, err := w.Write([]byte("invalid json"))
			require.NoError(t, err)
		}))
		defer server.Close()

		hostname := server.URL[8:]
		monolith := NewMonolith(hostname)

		originalClient := http.DefaultClient
		http.DefaultClient = server.Client()
		defer func() { http.DefaultClient = originalClient }()

		_, err := monolith.GetLayoutForProject(context.Background(), projectId)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to unmarshal response")
	})

	t.Run("network error", func(t *testing.T) {
		// Use localhost with a closed port to trigger immediate connection error
		monolith := NewMonolith("localhost:1")

		_, err := monolith.GetLayoutForProject(context.Background(), projectId)
		assert.Error(t, err)
		assert.Contains(t, err.Error(), "failed to execute request")
	})
}
