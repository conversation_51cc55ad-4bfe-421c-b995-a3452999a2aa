package adapters

import (
	"fmt"
	"log"
	"math"
	"strconv"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

type FaucetHoleSpacing string

const (
	// These are the string values used by Catalog.
	SingleHole FaucetHoleSpacing = "SINGLE"
	WideSpread FaucetHoleSpacing = "EIGHT"
	CenterSet  FaucetHoleSpacing = "FOUR"
)

func (f FaucetHoleSpacing) ToUsecase() (usecases.FaucetHoleSpacing, error) {
	switch f {
	case SingleHole:
		return usecases.SingleHole, nil
	case WideSpread:
		return usecases.WideSpread, nil
	case CenterSet:
		return usecases.CenterSet, nil
	default:
		return "", fmt.Errorf("unknown faucet hole spacing: %s", f)
	}
}

func FaucetHoleSpacingFromUsecase(fhs usecases.FaucetHoleSpacing) FaucetHoleSpacing {
	switch fhs {
	case usecases.WideSpread:
		return WideSpread
	case usecases.CenterSet:
		return CenterSet
	default:
		log.Printf("Unknown faucet hole spacing (%s) encountered when converting from usecase so defaulting to SingleHole", fhs)
		fallthrough
	case usecases.SingleHole:
		return SingleHole
	}
}

type MirrorType string

const (
	MirrorTypeMedicineCabinet MirrorType = "Medicine Cabinet"
	MirrorTypeMirror          MirrorType = "Mirror"
)

type ToiletType string

const (
	ToiletTypeFreestanding ToiletType = "Freestanding"
	ToiletTypeWallHung     ToiletType = "Wall Hung"
)

type ProductInfo struct {
	Id       uuid.UUID `json:"id"`
	Category struct {
		Name usecases.Category `json:"name"`
	} `json:"category"`
	Name              string  `json:"name"`
	ModelNumber       string  `json:"modelNumber"`
	Price             uint    `json:"price"`
	Length            string  `json:"length"`
	Height            string  `json:"height"`
	Brand             *string `json:"brand"`
	ProductFamilyName string  `json:"productFamilyName"`
	ColorGroup        *struct {
		Name usecases.ColorGroup `json:"name"`
	} `json:"colorGroup,omitempty"`
	Description   *string `json:"description,omitempty"`
	FeaturedImage *struct {
		URL string `json:"url"`
	} `json:"featuredImage,omitempty"`

	// This field is overloaded by both mirrors & toilets!
	Type *string `json:"type,omitempty"`

	HoleSpacingCompatibility []FaucetHoleSpacing `json:"holeSpacingCompatibility,omitempty"`

	AbovePlacementDefaultRotation *uint16 `json:"abovePlacementDefaultRotation,omitempty"`
	NumberOfLights                *uint8  `json:"numberOfLights,omitempty"`

	IsElectric *bool `json:"isElectric,omitempty"`

	ShelfCount *uint8 `json:"shelfCount,omitempty"`

	HasTubSpout           *bool `json:"hasTubSpout,omitempty"`
	HandshowerKitIncluded *bool `json:"handshowerKitIncluded,omitempty"`

	AvailableForShowerWall  *bool `json:"availableForShowerWall,omitempty"`
	AvailableForShowerFloor *bool `json:"availableForShowerFloor,omitempty"`
	AvailableForFloor       *bool `json:"availableForFloor,omitempty"`

	TubType *usecases.TubType  `json:"tubType,omitempty"`
	Shape   *usecases.TubShape `json:"shape,omitempty"`

	MountingPosition *usecases.TubFillerMountingPosition `json:"mountingPosition,omitempty"`

	FaucetHoleSpacing *FaucetHoleSpacing `json:"faucetHoleSpacing,omitempty"`
	NumberOfSinks     *uint8             `json:"numberOfSinks,omitempty"`
	SinkOffset        *string            `json:"sinkOffset,omitempty"`
}

func (p *ProductInfo) ToUsecase() (usecases.RenderableProduct, error) {
	var err error
	result := usecases.ProductInfo{
		Id:                p.Id,
		Category:          p.Category.Name,
		Name:              p.Name,
		ProductFamilyName: p.ProductFamilyName,
	}
	if p.Description != nil {
		result.Description = *p.Description
	}
	result.Length, err = strconv.ParseFloat(p.Length, 64)
	if err != nil {
		return result, err
	}
	if p.ColorGroup != nil {
		result.Color = &p.ColorGroup.Name
	}
	if p.AbovePlacementDefaultRotation != nil {
		if *p.AbovePlacementDefaultRotation > 180 {
			return result, fmt.Errorf("invalid above placement default rotation (%d) for %s", *p.AbovePlacementDefaultRotation, p.Id.String())
		}
		if *p.AbovePlacementDefaultRotation == 90 {
			if p.Height == "0.00000" {
				return result, fmt.Errorf("height of %s is 0 but above placement default rotation is 90", p.Id.String())
			}
			result.Length, err = strconv.ParseFloat(p.Height, 64)
			if err != nil {
				return result, err
			}
		}
	}

	switch p.Category.Name {
	case usecases.CategoryFaucet:
		if len(p.HoleSpacingCompatibility) == 0 {
			return result, fmt.Errorf("hole spacing compatibility missing for faucet %s", p.Id.String())
		}
		holeSpacingCompatibility := make([]usecases.FaucetHoleSpacing, len(p.HoleSpacingCompatibility))
		for i, fhs := range p.HoleSpacingCompatibility {
			holeSpacingCompatibility[i], err = fhs.ToUsecase()
			if err != nil {
				return result, fmt.Errorf("unknown faucet hole spacing for faucet %s: %w", p.Id.String(), err)
			}
		}
		return usecases.Faucet{
			ProductInfo:              result,
			HoleSpacingCompatibility: holeSpacingCompatibility,
		}, nil
	case usecases.CategoryLighting:
		if p.NumberOfLights == nil {
			return result, fmt.Errorf("number of lights missing for lighting %s", p.Id.String())
		}
		return usecases.Lighting{
			ProductInfo:   result,
			NumberOfBulbs: *p.NumberOfLights,
		}, nil
	case usecases.CategoryMirror:
		if p.Type == nil {
			return result, fmt.Errorf("type missing for mirror %s", p.Id.String())
		}
		isMedCab := *p.Type == string(MirrorTypeMedicineCabinet)
		return usecases.Mirror{
			ProductInfo:       result,
			IsMedicineCabinet: isMedCab,
			IsLighted:         p.IsElectric != nil && *p.IsElectric,
		}, nil
	case usecases.CategoryShelving:
		if p.ShelfCount == nil {
			return result, fmt.Errorf("shelf count missing for shelving %s", p.Id.String())
		}
		return usecases.Shelving{
			ProductInfo: result,
			ShelfCount:  *p.ShelfCount,
		}, nil
	case usecases.CategoryShower:
		if p.HasTubSpout == nil {
			return result, fmt.Errorf("tub spout presence indicator missing for shower %s", p.Id.String())
		}
		return usecases.Shower{
			ProductInfo:           result,
			HasTubSpout:           *p.HasTubSpout,
			HasHandheldShowerhead: p.HandshowerKitIncluded,
		}, nil
	case usecases.CategoryTile:
		return usecases.Tile{
			ProductInfo:             result,
			AvailableForShowerWall:  p.AvailableForShowerWall,
			AvailableForShowerFloor: p.AvailableForShowerFloor,
			AvailableForFloor:       p.AvailableForFloor,
		}, nil
	case usecases.CategoryToilet:
		if p.Type == nil {
			return result, fmt.Errorf("type missing for toilet %s", p.Id.String())
		}
		switch *p.Type {
		case string(ToiletTypeFreestanding):
		case string(ToiletTypeWallHung):
		}
	case usecases.CategoryTub:
		if p.TubType == nil {
			return result, fmt.Errorf("type missing for tub %s", p.Id.String())
		}
		return usecases.Tub{
			ProductInfo: result,
			Type:        *p.TubType,
			Shape:       p.Shape,
		}, nil
	case usecases.CategoryTubFiller:
		if p.MountingPosition == nil {
			return result, fmt.Errorf("mounting position missing for tub filler %s", p.Id.String())
		}
		return usecases.TubFiller{
			ProductInfo:      result,
			MountingPosition: p.MountingPosition,
		}, nil
	case usecases.CategoryVanity:
		if p.FaucetHoleSpacing == nil {
			return result, fmt.Errorf("faucet hole spacing missing for faucet %s", p.Id.String())
		}
		if p.NumberOfSinks == nil {
			return result, fmt.Errorf("number of sinks missing for vanity %s", p.Id.String())
		}
		faucetHoleSpacing, err := p.FaucetHoleSpacing.ToUsecase()
		if err != nil {
			return result, fmt.Errorf("unknown faucet hole spacing for vanity %s: %w", p.Id.String(), err)
		}
		if *p.NumberOfSinks > 3 || *p.NumberOfSinks == 0 {
			return result, fmt.Errorf("invalid number of sinks (%d) for %s", *p.NumberOfSinks, p.Id.String())
		}
		if p.SinkOffset == nil {
			return result, fmt.Errorf("sink offset missing for vanity %s", p.Id.String())
		}
		sinkOffset, err := strconv.ParseFloat(*p.SinkOffset, 64)
		if err != nil {
			return result, fmt.Errorf("failed to parse sink offset (%s) for vanity %s: %w", *p.SinkOffset, p.Id.String(), err)
		}
		if *p.NumberOfSinks > 1 && math.Abs(sinkOffset) < 0.001 {
			return result, fmt.Errorf("unrealistic sink offset (%v) for %s", p.SinkOffset, p.Id.String())
		}
		return usecases.Vanity{
			ProductInfo:       result,
			FaucetHoleSpacing: faucetHoleSpacing,
			NumberOfSinks:     *p.NumberOfSinks,
			SinkOffset:        sinkOffset,
		}, nil
	}

	return result, nil
}

func FromUsecaseProductInfo(productInfo usecases.ProductInfo) ProductInfo {
	result := ProductInfo{
		Id: productInfo.Id,
		Category: struct {
			Name usecases.Category `json:"name"`
		}{Name: productInfo.Category},
		Name:              productInfo.Name,
		Description:       &productInfo.Description,
		Length:            strconv.FormatFloat(productInfo.Length, 'f', 5, 64),
		ProductFamilyName: productInfo.ProductFamilyName,
	}
	if productInfo.Color != nil {
		result.ColorGroup = &struct {
			Name usecases.ColorGroup `json:"name"`
		}{Name: *productInfo.Color}
	}
	return result
}
func FromUsecaseRenderableProduct(rp usecases.RenderableProduct) ProductInfo {
	var result ProductInfo
	switch rp := rp.(type) {
	case usecases.Faucet:
		result = FromUsecaseProductInfo(rp.ProductInfo)
		result.HoleSpacingCompatibility = make([]FaucetHoleSpacing, len(rp.HoleSpacingCompatibility))
		for i, fhs := range rp.HoleSpacingCompatibility {
			result.HoleSpacingCompatibility[i] = FaucetHoleSpacingFromUsecase(fhs)
		}
	case usecases.Lighting:
		result = FromUsecaseProductInfo(rp.ProductInfo)
		result.NumberOfLights = &rp.NumberOfBulbs
	case usecases.Mirror:
		result = FromUsecaseProductInfo(rp.ProductInfo)
		var mType string
		if rp.IsMedicineCabinet {
			mType = string(MirrorTypeMedicineCabinet)
		} else {
			mType = string(MirrorTypeMirror)
		}
		result.Type = &mType
		result.IsElectric = &rp.IsLighted
	case usecases.Shelving:
		result = FromUsecaseProductInfo(rp.ProductInfo)
		result.ShelfCount = &rp.ShelfCount
	case usecases.Shower:
		result = FromUsecaseProductInfo(rp.ProductInfo)
		result.HasTubSpout = &rp.HasTubSpout
		result.HandshowerKitIncluded = rp.HasHandheldShowerhead
	case usecases.Tile:
		result = FromUsecaseProductInfo(rp.ProductInfo)
		result.AvailableForShowerWall = rp.AvailableForShowerWall
		result.AvailableForShowerFloor = rp.AvailableForShowerFloor
		result.AvailableForFloor = rp.AvailableForFloor
	case usecases.Toilet:
		result = FromUsecaseProductInfo(rp.ProductInfo)
		if rp.MountingPosition != nil {
			var toiletType string
			switch *rp.MountingPosition {
			case usecases.ToiletMountingPositionFloor:
				toiletType = string(ToiletTypeFreestanding)
			case usecases.ToiletMountingPositionWall:
				toiletType = string(ToiletTypeWallHung)
			}
			result.Type = &toiletType
		}
	case usecases.Tub:
		result = FromUsecaseProductInfo(rp.ProductInfo)
		result.TubType = &rp.Type
		result.Shape = rp.Shape
	case usecases.TubFiller:
		result = FromUsecaseProductInfo(rp.ProductInfo)
		result.MountingPosition = rp.MountingPosition
	case usecases.Vanity:
		result = FromUsecaseProductInfo(rp.ProductInfo)
		result.NumberOfSinks = &rp.NumberOfSinks
		sinkOffset := strconv.FormatFloat(rp.SinkOffset, 'f', 5, 64)
		result.SinkOffset = &sinkOffset
		fhs := FaucetHoleSpacingFromUsecase(rp.FaucetHoleSpacing)
		result.FaucetHoleSpacing = &fhs
	}
	return result
}
