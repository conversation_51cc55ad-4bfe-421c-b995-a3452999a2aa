package adapters

import (
	"errors"
	"log"
	"net/url"
	"strconv"
	"strings"
	"time"

	"github.com/google/uuid"

	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

func tagsAsInt64(t usecases.Tagged) int64 {
	tags := int64(0)
	switch t.ColorScheme {
	case usecases.Neutral:
		tags |= 32
	case usecases.Bold:
		tags |= 16
	}
	switch t.Style {
	case usecases.MidCentury:
		tags |= 1
	case usecases.Traditional:
		tags |= 2
	case usecases.Modern:
		tags |= 4
	case usecases.Transitional:
		tags |= 8
	}
	return tags
}

type DesignPackage struct {
	ID        string    `json:"id"`
	LegacyId  *string   `json:"legacy_id,omitempty"`
	UpdatedAt time.Time `json:"updatedAt,omitempty"`
	usecases.Tagged
	Name            string    `json:"name"`
	ImageURL        string    `json:"image_url"`
	Description     string    `json:"description"`
	Inspiration     string    `json:"inspiration"`
	Atmosphere      string    `json:"atmosphere"`
	ColorPalette    string    `json:"color_palette"`
	MaterialPalette string    `json:"material_palette"`
	Materials       Materials `json:"materials"`

	HighlightedBrandUrls []string `json:"highlighted_brand_urls,omitempty"`
	VanityStorage        string   `json:"vanity_storage"`
	VanityBrand          string   `json:"vanity_brand"`
	LightingBrand        string   `json:"lighting_brand"`
	ToiletBrand          string   `json:"toilet_brand"`
	PlumbingBrand        string   `json:"plumbing_brand"`
}
type Materials struct {
	ID                 string                      `json:"id"`
	Tags               int64                       `json:"tags"`
	Paint              uuid.UUID                   `json:"paint"`
	Mirror             uuid.UUID                   `json:"mirror"`
	Toilet             uuid.UUID                   `json:"toilet"`
	Shelves            uuid.UUID                   `json:"shelves"`
	Lighting           uuid.UUID                   `json:"lighting"`
	FloorTile          uuid.UUID                   `json:"floorTile"`
	TubFiller          uuid.UUID                   `json:"tubFiller"`
	ShowerWallTile     uuid.UUID                   `json:"showerWallTile"`
	ShowerFloorTile    uuid.UUID                   `json:"showerFloorTile"`
	AlcoveTub          uuid.UUID                   `json:"alcoveTub"`
	FreestandingTub    uuid.UUID                   `json:"freestandingTub"`
	ShowerGlassFixed   uuid.UUID                   `json:"showerGlassFixed"`
	ShowerGlassSliding uuid.UUID                   `json:"showerGlassSliding"`
	ShowerSystemFull   uuid.UUID                   `json:"showerSystemFull"`
	ShowerSystemShower uuid.UUID                   `json:"showerSystemShower"`
	TubDoorFixed       uuid.UUID                   `json:"tubDoorFixed"`
	TubDoorSliding     uuid.UUID                   `json:"tubDoorSliding"`
	WallpaperPlacement usecases.WallpaperPlacement `json:"wallpaperPlacement"`
	Wallpaper          *uuid.UUID                  `json:"wallpaper,omitempty"`
	WallTilePlacement  usecases.WallTilePlacement  `json:"wallTilePlacement"`
	WallTile           *uuid.UUID                  `json:"wallTile,omitempty"`
	FaucetDict         map[string]uuid.UUID        `json:"faucetDict"`
	VanityDict         map[string]uuid.UUID        `json:"vanityDict"`
}

func ToUsecaseDesignPackage(t DesignPackage) (usecases.DesignPackage, error) {
	// Parse the design package ID as UUID
	designPkgUUID, err := uuid.Parse(t.ID)
	if err != nil {
		return usecases.DesignPackage{}, usecases.ErrInvalidPayload
	}

	if len(t.Materials.VanityDict) != len(t.Materials.FaucetDict) {
		log.Printf("Vanity and faucet dicts have different lengths: %d vs %d",
			len(t.Materials.VanityDict), len(t.Materials.FaucetDict))
		return usecases.DesignPackage{}, usecases.ErrInvalidPayload
	}

	// Parse atmosphere and color/material palettes from strings to arrays
	atmosphere := []string{}
	if t.Atmosphere != "" {
		atmosphere = strings.Split(strings.TrimSpace(t.Atmosphere), ", ")
	}
	colorPalette := []string{}
	if t.ColorPalette != "" {
		colorPalette = strings.Split(strings.TrimSpace(t.ColorPalette), ", ")
	}
	materialPalette := []string{}
	if t.MaterialPalette != "" {
		materialPalette = strings.Split(strings.TrimSpace(t.MaterialPalette), ", ")
	}

	imgURL, err := url.Parse(t.ImageURL)
	if err != nil {
		log.Printf("Error parsing image URL: %v", err)
		return usecases.DesignPackage{}, usecases.ErrInvalidPayload
	}

	highlightedBrandUrls := make([]url.URL, 0, len(t.HighlightedBrandUrls))
	for _, brandURL := range t.HighlightedBrandUrls {
		if parsedURL, err := url.Parse(brandURL); err == nil {
			highlightedBrandUrls = append(highlightedBrandUrls, *parsedURL)
			continue
		}
		log.Printf("Error parsing highlighted brand URL: %v", err)
	}
	if len(highlightedBrandUrls) < len(t.HighlightedBrandUrls) {
		log.Printf("Failed to parse some highlighted brand URLs: %d parsed out of %d",
			len(highlightedBrandUrls), len(t.HighlightedBrandUrls))
	}

	designPkgUsecase := usecases.DesignPackage{
		ID:        designPkgUUID,
		LegacyId:  t.LegacyId,
		UpdatedAt: t.UpdatedAt,
		Tagged: usecases.Tagged{
			ColorScheme: t.ColorScheme,
			Style:       t.Style,
		},
		Name:                 t.Name,
		ImageURL:             *imgURL,
		Description:          t.Description,
		Inspiration:          t.Inspiration,
		Atmosphere:           atmosphere,
		ColorPalette:         colorPalette,
		MaterialPalette:      materialPalette,
		HighlightedBrandUrls: highlightedBrandUrls,
		FixedProductSelections: usecases.FixedProductSelections{
			FloorTile:       &t.Materials.FloorTile,
			Lighting:        &t.Materials.Lighting,
			Mirror:          &t.Materials.Mirror,
			Paint:           &t.Materials.Paint,
			Shelving:        &t.Materials.Shelves,
			Toilet:          &t.Materials.Toilet,
			ShowerFloorTile: &t.Materials.ShowerFloorTile,
			ShowerWallTile:  &t.Materials.ShowerWallTile,
			TubFiller:       &t.Materials.TubFiller,
			WallTile:        t.Materials.WallTile,
		},
		ProductSelectionOptions: usecases.ProductSelectionOptions{
			AlcoveTub:          t.Materials.AlcoveTub,
			FreestandingTub:    t.Materials.FreestandingTub,
			ShowerGlassFixed:   t.Materials.ShowerGlassFixed,
			ShowerGlassSliding: t.Materials.ShowerGlassSliding,
			ShowerSystemCombo:  t.Materials.ShowerSystemFull,
			ShowerSystemSolo:   t.Materials.ShowerSystemShower,
			TubDoorFixed:       t.Materials.TubDoorFixed,
			TubDoorSliding:     t.Materials.TubDoorSliding,
		},
		DesignPackageProvenance: usecases.DesignPackageProvenance{
			LightingBrand: &t.LightingBrand,
			PlumbingBrand: &t.PlumbingBrand,
			ToiletBrand:   &t.ToiletBrand,
			VanityBrand:   &t.VanityBrand,
			VanityStorage: &t.VanityStorage,
		},
		WallTilePlacement:  t.Materials.WallTilePlacement,
		WallpaperPlacement: t.Materials.WallpaperPlacement,
	}

	// Handle wallpaper if present
	designPkgUsecase.Wallpaper = t.Materials.Wallpaper

	// Vanity scaling options
	designPkgUsecase.VanityScalingOptions = make(map[int]usecases.VanityScalingOption)
	for k, v := range t.Materials.VanityDict {
		minVanityLengthInches, err := strconv.Atoi(k)
		if err != nil {
			continue
		}
		designPkgUsecase.VanityScalingOptions[minVanityLengthInches] = usecases.VanityScalingOption{
			VanityProductID: v,
			FaucetProductID: t.Materials.FaucetDict[k],
		}
	}

	return designPkgUsecase, nil
}

// FromUsecaseDesignPackage converts a usecases.DesignPackage domain entity to a DesignPackage API model.
func FromUsecaseDesignPackage(t usecases.DesignPackage, useLegacyIds bool) DesignPackage {
	uuidPtrToUUID := func(u *uuid.UUID) uuid.UUID {
		if u == nil {
			return uuid.Nil
		}
		return *u
	}
	stringPtrToString := func(s *string) string {
		if s == nil {
			return ""
		}
		return *s
	}

	// Convert arrays to comma-separated strings
	atmosphereStr := strings.Join(t.Atmosphere, ", ")
	colorPaletteStr := strings.Join(t.ColorPalette, ", ")
	materialPaletteStr := strings.Join(t.MaterialPalette, ", ")

	highlightedBrandUrls := make([]string, len(t.HighlightedBrandUrls))
	for i, url := range t.HighlightedBrandUrls {
		highlightedBrandUrls[i] = url.String()
	}

	// Use legacy ID as the primary ID if available, otherwise fall back to UUID
	var responseId string
	if useLegacyIds && t.LegacyId != nil && *t.LegacyId != "" {
		responseId = *t.LegacyId
	} else {
		responseId = t.ID.String()
	}

	designPkg := DesignPackage{
		ID:                   responseId,
		UpdatedAt:            t.UpdatedAt,
		Tagged:               t.Tagged,
		Name:                 t.Name,
		Description:          t.Description,
		Atmosphere:           atmosphereStr,
		ColorPalette:         colorPaletteStr,
		MaterialPalette:      materialPaletteStr,
		Inspiration:          t.Inspiration,
		PlumbingBrand:        stringPtrToString(t.PlumbingBrand),
		LightingBrand:        stringPtrToString(t.LightingBrand),
		VanityBrand:          stringPtrToString(t.VanityBrand),
		ToiletBrand:          stringPtrToString(t.ToiletBrand),
		VanityStorage:        stringPtrToString(t.VanityStorage),
		ImageURL:             t.ImageURL.String(),
		HighlightedBrandUrls: highlightedBrandUrls,
	}

	// Set materials
	designPkg.Materials.ID = responseId
	designPkg.Materials.Tags = tagsAsInt64(t.Tagged)
	designPkg.Materials.Paint = uuidPtrToUUID(t.Paint)
	designPkg.Materials.Mirror = uuidPtrToUUID(t.Mirror)
	designPkg.Materials.Toilet = uuidPtrToUUID(t.Toilet)
	designPkg.Materials.Shelves = uuidPtrToUUID(t.Shelving)
	designPkg.Materials.Lighting = uuidPtrToUUID(t.Lighting)
	designPkg.Materials.FloorTile = uuidPtrToUUID(t.FloorTile)
	designPkg.Materials.TubFiller = uuidPtrToUUID(t.TubFiller)
	designPkg.Materials.ShowerWallTile = uuidPtrToUUID(t.ShowerWallTile)
	designPkg.Materials.ShowerFloorTile = uuidPtrToUUID(t.ShowerFloorTile)
	designPkg.Materials.WallTile = t.WallTile
	designPkg.Materials.AlcoveTub = t.AlcoveTub
	designPkg.Materials.FreestandingTub = t.FreestandingTub
	designPkg.Materials.ShowerGlassFixed = t.ShowerGlassFixed
	designPkg.Materials.ShowerGlassSliding = t.ShowerGlassSliding
	designPkg.Materials.ShowerSystemFull = t.ShowerSystemCombo
	designPkg.Materials.ShowerSystemShower = t.ShowerSystemSolo
	designPkg.Materials.TubDoorFixed = t.TubDoorFixed
	designPkg.Materials.TubDoorSliding = t.TubDoorSliding
	designPkg.Materials.WallTilePlacement = t.WallTilePlacement
	designPkg.Materials.WallpaperPlacement = t.WallpaperPlacement
	designPkg.Materials.Wallpaper = t.Wallpaper

	// Convert vanity scaling options back to dicts
	designPkg.Materials.VanityDict = make(map[string]uuid.UUID)
	designPkg.Materials.FaucetDict = make(map[string]uuid.UUID)
	for minLength, option := range t.VanityScalingOptions {
		key := strconv.Itoa(minLength)
		designPkg.Materials.VanityDict[key] = option.VanityProductID
		designPkg.Materials.FaucetDict[key] = option.FaucetProductID
	}

	return designPkg
}

// AlignId aligns the design package ID with the provided ID from the URL path.
// If the design package ID is empty, it sets it to the provided ID.
// If the design package ID is different from the provided ID, it returns an error.
func (t *DesignPackage) AlignId(id string) error {
	_, err := uuid.Parse(id)
	if err != nil {
		return errors.New("invalid design package UUID")
	}

	if t.ID == "" {
		t.ID = id
	} else if t.ID != id {
		return errors.New("design package ID mismatch")
	}
	return nil
}
