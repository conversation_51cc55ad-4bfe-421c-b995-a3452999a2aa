package adapters_test

import (
	"encoding/json"
	"net/url"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"gitlab.com/arc-studio-ai/services/room-design/adapters"
	"gitlab.com/arc-studio-ai/services/room-design/usecases"
)

const jsonBlob = `{
	"id": "20",
    "style": "Modern",
    "color_scheme": "Bold",
    "name": "Modern Geometry",
    "description": "This modern bathroom design is inspired by the vibrant, bold geometries of Mexico",
    "atmosphere": "geometric, bold, edgy",
    "color_palette": "black and white, matte black hardware, wood vanity",
    "material_palette": "handmade tile, geometric tile, oval elogated mirrors, dark wood vanity, matte black fixtures",
    "inspiration": "bold geometries of mexico",
    "plumbing_brand": "Delta",
    "lighting_brand": "Elegant Lighting",
    "vanity_brand": "James Martin Vanities",
    "toilet_brand": "Duravit",
    "vanity_storage": "wall mounted with drawers",
    "materials": {
        "id": "20",
        "tub": "",
        "tags": 20,
        "paint": "505c9c7c-4432-43c1-95fd-4650f8e4b775",
        "faucet": "",
        "mirror": "9ea7495d-4a33-4cc0-8832-d2b3545f0950",
        "toilet": "bed4b456-1058-4332-ac4b-ef40dcad5dfd",
        "vanity": "",
        "shelves": "33c3c4c9-4d57-4ae7-895b-00c80630da2c",
        "tubDoor": "",
        "lighting": "92fce210-b77d-49d6-8051-3b347917dc73",
        "robeHook": "97d986a6-d8f8-4f42-a04b-5f536f035304",
        "towelBar": "acd88e3b-3f58-4a33-8be0-86f6e8cb95a3",
        "tpHolder": "d8cfbb8b-c2a6-4bfd-a473-cf3a87ab4b1f",
        "wallTile": "135f1c09-8525-4e55-9997-cc263278ea72",
        "alcoveTub": "f976f7e6-da92-4236-9b53-ff3abc54d1eb",
        "floorTile": "ab07d14c-5b4e-44aa-96ba-1ccf3d901743",
        "towelRing": "be3e7f4d-ace2-443d-9bdc-a2601471d193",
        "tubFiller": "6608a61f-f005-410b-9efd-e6336e58a854",
        "faucetDict": {
            "24": "840eff26-6c5d-4467-b274-8c100b349cfd",
            "30": "045aef8d-0d33-4389-ac88-8a0635f611bd",
            "36": "045aef8d-0d33-4389-ac88-8a0635f611bd",
            "60": "045aef8d-0d33-4389-ac88-8a0635f611bd"
        },
        "vanityDict": {
            "24": "09d3ceb2-85fd-4e7b-86e2-3f9a97d9861f",
            "30": "9d8e170f-e0f1-4ba0-a609-e530b349195c",
            "36": "9de10ac3-166c-41fb-ae22-db8290e5ba6d",
            "60": "2a279059-d531-498d-8258-2171f1827160"
        },
        "showerGlass": "",
        "tubDoorFixed": "f0529d4f-5392-4ed1-91de-0b48ec31c14f",
        "showerWallTile": "135f1c09-8525-4e55-9997-cc263278ea72",
        "tubDoorSliding": "cd5af25c-5cb3-473e-ab7c-036fead0f041",
        "freestandingTub": "397d68a2-1c10-4bb4-96cc-145d0ef19e64",
        "showerFloorTile": "ab07d14c-5b4e-44aa-96ba-1ccf3d901743",
        "isTubDoorVisible": false,
        "showerGlassFixed": "ab32ea6f-a461-4f88-9910-4d13c722b951",
        "showerSystemFull": "8eee8bd7-21a9-4531-ae10-19af150a9980",
        "wallTilePlacement": "HalfWall",
        "showerGlassSliding": "197e7b0e-a554-4483-b956-e49dc80ef592",
        "showerSystemShower": "d16fcd40-cb89-4b9a-80af-ce294ac0f3e8",
        "wallpaperPlacement": "None",
        "isShowerGlassVisible": false
    },
    "render_priority": 1,
    "image_url": "https://cdn.arcstudio.ai/assets/Template20_ModernGeometry.webp",
    "highlighted_brand_urls": [
        "https://cdn.arcstudio.ai/assets/bedrosians-logo.pngImage preview",
        "https://cdn.arcstudio.ai/assets/behr-logo.pngImage preview",
        "https://cdn.arcstudio.ai/assets/kohler-logo-black.pngImage preview",
        "https://cdn.arcstudio.ai/assets/signature-hardware-logo.pngImage preview"
    ]
}`

func Test_FromJsonBlob(t *testing.T) {
	var designPkg adapters.DesignPackage
	err := json.Unmarshal([]byte(jsonBlob), &designPkg)
	require.NoError(t, err)
	assert.Equal(t, "20", designPkg.ID)
}

func TestDesignPackage_AlignId(t *testing.T) {
	tests := []struct {
		designPkgID, urlID string
		wantErr            string
	}{
		{"", uuid.New().String(), ""},                          // empty ID gets set
		{uuid.New().String(), "", ""},                          // matching IDs succeed
		{uuid.New().String(), uuid.New().String(), "mismatch"}, // different IDs fail
		{"", "invalid", "invalid design package UUID"},         // invalid UUID fails
	}

	for _, tt := range tests {
		designPkg := adapters.DesignPackage{ID: tt.designPkgID}
		urlID := tt.urlID
		if tt.urlID == "" && tt.designPkgID != "" {
			urlID = tt.designPkgID
		}

		err := designPkg.AlignId(urlID)

		if tt.wantErr != "" {
			require.Error(t, err)
			assert.Contains(t, err.Error(), tt.wantErr)
		} else {
			require.NoError(t, err)
			expected := tt.designPkgID
			if expected == "" {
				expected = urlID
			}
			assert.Equal(t, expected, designPkg.ID)
		}
	}
}

func TestToUsecaseDesignPackage(t *testing.T) {
	id := uuid.New()
	v1, v2, f1, f2 := uuid.New(), uuid.New(), uuid.New(), uuid.New()

	// Valid conversion
	designPkg := adapters.DesignPackage{
		ID: id.String(), Name: "Test", Atmosphere: "cozy, modern",
		Tagged: usecases.Tagged{ColorScheme: usecases.Neutral, Style: usecases.Modern},
		Materials: adapters.Materials{
			VanityDict: map[string]uuid.UUID{"24": v1, "36": v2},
			FaucetDict: map[string]uuid.UUID{"24": f1, "36": f2},
		},
	}
	result, err := adapters.ToUsecaseDesignPackage(designPkg)
	require.NoError(t, err)
	assert.Equal(t, id, result.ID)
	assert.Equal(t, []string{"cozy", "modern"}, result.Atmosphere)
	assert.Equal(t, v1, result.VanityScalingOptions[24].VanityProductID)

	// Error cases
	_, err = adapters.ToUsecaseDesignPackage(adapters.DesignPackage{ID: ""})
	assert.Equal(t, usecases.ErrInvalidPayload, err)

	designPkg.Materials.FaucetDict = map[string]uuid.UUID{"24": f1, "36": f2, "48": uuid.New()}
	_, err = adapters.ToUsecaseDesignPackage(designPkg)
	assert.Equal(t, usecases.ErrInvalidPayload, err)
}

func TestFromUsecaseDesignPackage(t *testing.T) {
	designPkgID := uuid.New()
	vanityId1 := uuid.New()
	vanityId2 := uuid.New()
	faucetId1 := uuid.New()
	faucetId2 := uuid.New()
	showerFloorTileId := uuid.New()

	t.Run("should convert usecase design package successfully", func(t *testing.T) {
		usecaseDesignPkg := usecases.DesignPackage{
			ID:   designPkgID,
			Name: "Test Design Package",
			Tagged: usecases.Tagged{
				ColorScheme: usecases.Neutral,
				Style:       usecases.Modern,
			},
			Description:     "A test design package",
			Inspiration:     "Test inspiration",
			Atmosphere:      []string{"cozy", "modern"},
			ColorPalette:    []string{"#FFFFFF", "#000000"},
			MaterialPalette: []string{"wood", "metal"},
			FixedProductSelections: usecases.FixedProductSelections{
				ShowerFloorTile: &showerFloorTileId,
			},
			WallTilePlacement:  usecases.HalfWall,
			WallpaperPlacement: usecases.NoWallpaper,
			VanityScalingOptions: map[int]usecases.VanityScalingOption{
				24: {VanityProductID: vanityId1, FaucetProductID: faucetId1},
				36: {VanityProductID: vanityId2, FaucetProductID: faucetId2},
			},
		}

		adapterDesignPkg := adapters.FromUsecaseDesignPackage(usecaseDesignPkg, false)
		assert.Equal(t, designPkgID.String(), adapterDesignPkg.Materials.ID)

		assert.Equal(t, designPkgID.String(), adapterDesignPkg.ID)
		assert.Equal(t, "Test Design Package", adapterDesignPkg.Name)
		assert.Equal(t, usecases.Neutral, adapterDesignPkg.ColorScheme)
		assert.Equal(t, usecases.Modern, adapterDesignPkg.Style)
		assert.Equal(t, "A test design package", adapterDesignPkg.Description)
		assert.Equal(t, "cozy, modern", adapterDesignPkg.Atmosphere)
		assert.Equal(t, showerFloorTileId, adapterDesignPkg.Materials.ShowerFloorTile)
		assert.Nil(t, adapterDesignPkg.Materials.WallTile)
		assert.Equal(t, usecases.HalfWall, adapterDesignPkg.Materials.WallTilePlacement)
		assert.Equal(t, usecases.NoWallpaper, adapterDesignPkg.Materials.WallpaperPlacement)

		// Check vanity/faucet dicts
		assert.Len(t, adapterDesignPkg.Materials.VanityDict, 2)
		assert.Len(t, adapterDesignPkg.Materials.FaucetDict, 2)
		assert.Equal(t, vanityId1, adapterDesignPkg.Materials.VanityDict["24"])
		assert.Equal(t, faucetId1, adapterDesignPkg.Materials.FaucetDict["24"])
		assert.Equal(t, vanityId2, adapterDesignPkg.Materials.VanityDict["36"])
		assert.Equal(t, faucetId2, adapterDesignPkg.Materials.FaucetDict["36"])
	})
}

func TestDesignPackage_ConversionRoundTrip(t *testing.T) {
	id := uuid.New()
	v1, f1 := uuid.New(), uuid.New()

	original := adapters.DesignPackage{
		ID: id.String(), Name: "Test", Atmosphere: "cozy, modern",
		Tagged: usecases.Tagged{ColorScheme: usecases.Neutral, Style: usecases.Modern},
		Materials: adapters.Materials{
			VanityDict: map[string]uuid.UUID{"24": v1},
			FaucetDict: map[string]uuid.UUID{"24": f1},
		},
	}

	usecase, err := adapters.ToUsecaseDesignPackage(original)
	require.NoError(t, err)
	converted := adapters.FromUsecaseDesignPackage(usecase, false)

	assert.Equal(t, original.ID, converted.ID)
	assert.Equal(t, original.Name, converted.Name)
	assert.Equal(t, original.Atmosphere, converted.Atmosphere)
	assert.Equal(t, original.Materials.VanityDict["24"], converted.Materials.VanityDict["24"])
}

// TestDesignPackageConversions_ComprehensiveFieldMapping tests that all fields are properly converted
func TestDesignPackageConversions_ComprehensiveFieldMapping(t *testing.T) {
	designPkgID := uuid.New()
	legacyId := "AB"

	// Generate UUIDs for all materials
	paintId := uuid.New()
	mirrorId := uuid.New()
	toiletId := uuid.New()
	shelvesId := uuid.New()
	lightingId := uuid.New()
	floorTileId := uuid.New()
	tubFillerId := uuid.New()
	showerWallTileId := uuid.New()
	showerFloorTileId := uuid.New()
	alcoveTubId := uuid.New()
	freestandingTubId := uuid.New()
	showerGlassFixedId := uuid.New()
	showerGlassSlidingId := uuid.New()
	showerSystemFullId := uuid.New()
	showerSystemShowerId := uuid.New()
	tubDoorFixedId := uuid.New()
	tubDoorSlidingId := uuid.New()
	wallTileId := uuid.New()
	wallpaperId := uuid.New()
	vanityId1 := uuid.New()
	vanityId2 := uuid.New()
	faucetId1 := uuid.New()
	faucetId2 := uuid.New()

	t.Run("ToUsecaseDesignPackage should convert all fields correctly", func(t *testing.T) {
		adapterDesignPkg := adapters.DesignPackage{
			ID:       designPkgID.String(),
			LegacyId: &legacyId,
			Tagged: usecases.Tagged{
				ColorScheme: usecases.Bold,
				Style:       usecases.Traditional,
			},
			Name:            "Comprehensive Test Design Package",
			ImageURL:        "https://example.com/image.jpg",
			Description:     "A comprehensive test design package",
			Inspiration:     "Traditional design inspiration",
			Atmosphere:      "elegant, warm, classic",
			ColorPalette:    "cream, gold, burgundy",
			MaterialPalette: "marble, brass, mahogany",
			HighlightedBrandUrls: []string{
				"https://example.com/brand1",
				"https://example.com/brand2",
			},
			VanityStorage: "floating with soft-close drawers",
			VanityBrand:   "Custom Vanities Inc",
			LightingBrand: "Elegant Lighting Co",
			ToiletBrand:   "Premium Toilets",
			PlumbingBrand: "Quality Plumbing",
			Materials: adapters.Materials{
				ID:                 designPkgID.String(),
				Tags:               18, // Traditional (2) + Bold (16)
				Paint:              paintId,
				Mirror:             mirrorId,
				Toilet:             toiletId,
				Shelves:            shelvesId,
				Lighting:           lightingId,
				FloorTile:          floorTileId,
				TubFiller:          tubFillerId,
				ShowerWallTile:     showerWallTileId,
				ShowerFloorTile:    showerFloorTileId,
				AlcoveTub:          alcoveTubId,
				FreestandingTub:    freestandingTubId,
				ShowerGlassFixed:   showerGlassFixedId,
				ShowerGlassSliding: showerGlassSlidingId,
				ShowerSystemFull:   showerSystemFullId,
				ShowerSystemShower: showerSystemShowerId,
				TubDoorFixed:       tubDoorFixedId,
				TubDoorSliding:     tubDoorSlidingId,
				WallpaperPlacement: usecases.VanityWall,
				Wallpaper:          &wallpaperId,
				WallTilePlacement:  usecases.HalfWall,
				WallTile:           &wallTileId,
				FaucetDict: map[string]uuid.UUID{
					"24": faucetId1,
					"36": faucetId2,
				},
				VanityDict: map[string]uuid.UUID{
					"24": vanityId1,
					"36": vanityId2,
				},
			},
		}

		usecaseDesignPkg, err := adapters.ToUsecaseDesignPackage(adapterDesignPkg)
		require.NoError(t, err)

		// Verify basic fields
		assert.Equal(t, designPkgID, usecaseDesignPkg.ID)
		assert.Equal(t, &legacyId, usecaseDesignPkg.LegacyId)
		assert.Equal(t, usecases.Bold, usecaseDesignPkg.ColorScheme)
		assert.Equal(t, usecases.Traditional, usecaseDesignPkg.Style)
		assert.Equal(t, "Comprehensive Test Design Package", usecaseDesignPkg.Name)
		assert.Equal(t, "https://example.com/image.jpg", usecaseDesignPkg.ImageURL.String())
		assert.Equal(t, "A comprehensive test design package", usecaseDesignPkg.Description)
		assert.Equal(t, "Traditional design inspiration", usecaseDesignPkg.Inspiration)

		// Verify array conversions
		assert.Equal(t, []string{"elegant", "warm", "classic"}, usecaseDesignPkg.Atmosphere)
		assert.Equal(t, []string{"cream", "gold", "burgundy"}, usecaseDesignPkg.ColorPalette)
		assert.Equal(t, []string{"marble", "brass", "mahogany"}, usecaseDesignPkg.MaterialPalette)

		// Verify highlighted brand URLs
		require.Len(t, usecaseDesignPkg.HighlightedBrandUrls, 2)
		assert.Equal(t, "https://example.com/brand1", usecaseDesignPkg.HighlightedBrandUrls[0].String())
		assert.Equal(t, "https://example.com/brand2", usecaseDesignPkg.HighlightedBrandUrls[1].String())

		// Verify fixed product selections
		assert.Equal(t, &paintId, usecaseDesignPkg.Paint)
		assert.Equal(t, &mirrorId, usecaseDesignPkg.Mirror)
		assert.Equal(t, &toiletId, usecaseDesignPkg.Toilet)
		assert.Equal(t, &shelvesId, usecaseDesignPkg.Shelving)
		assert.Equal(t, &lightingId, usecaseDesignPkg.Lighting)
		assert.Equal(t, &floorTileId, usecaseDesignPkg.FloorTile)

		// Verify product selection options
		assert.Equal(t, alcoveTubId, usecaseDesignPkg.AlcoveTub)
		assert.Equal(t, freestandingTubId, usecaseDesignPkg.FreestandingTub)
		assert.Equal(t, showerGlassFixedId, usecaseDesignPkg.ShowerGlassFixed)
		assert.Equal(t, showerGlassSlidingId, usecaseDesignPkg.ShowerGlassSliding)
		assert.Equal(t, showerSystemFullId, usecaseDesignPkg.ShowerSystemCombo)
		assert.Equal(t, showerSystemShowerId, usecaseDesignPkg.ShowerSystemSolo)
		assert.Equal(t, tubDoorFixedId, usecaseDesignPkg.TubDoorFixed)
		assert.Equal(t, tubDoorSlidingId, usecaseDesignPkg.TubDoorSliding)

		// Verify design package provenance
		assert.Equal(t, "Quality Plumbing", *usecaseDesignPkg.PlumbingBrand)
		assert.Equal(t, "Elegant Lighting Co", *usecaseDesignPkg.LightingBrand)
		assert.Equal(t, "Custom Vanities Inc", *usecaseDesignPkg.VanityBrand)
		assert.Equal(t, "Premium Toilets", *usecaseDesignPkg.ToiletBrand)
		assert.Equal(t, "floating with soft-close drawers", *usecaseDesignPkg.VanityStorage)

		// Verify additional fields
		assert.Equal(t, &showerFloorTileId, usecaseDesignPkg.ShowerFloorTile)
		assert.Equal(t, &showerWallTileId, usecaseDesignPkg.ShowerWallTile)
		assert.Equal(t, &tubFillerId, usecaseDesignPkg.TubFiller)
		assert.Equal(t, usecases.HalfWall, usecaseDesignPkg.WallTilePlacement)
		assert.Equal(t, &wallTileId, usecaseDesignPkg.WallTile)
		assert.Equal(t, usecases.VanityWall, usecaseDesignPkg.WallpaperPlacement)
		assert.Equal(t, &wallpaperId, usecaseDesignPkg.Wallpaper)

		// Verify vanity scaling options
		require.Len(t, usecaseDesignPkg.VanityScalingOptions, 2)
		assert.Equal(t, vanityId1, usecaseDesignPkg.VanityScalingOptions[24].VanityProductID)
		assert.Equal(t, faucetId1, usecaseDesignPkg.VanityScalingOptions[24].FaucetProductID)
		assert.Equal(t, vanityId2, usecaseDesignPkg.VanityScalingOptions[36].VanityProductID)
		assert.Equal(t, faucetId2, usecaseDesignPkg.VanityScalingOptions[36].FaucetProductID)
	})

	t.Run("FromUsecaseDesignPackage should convert all fields correctly", func(t *testing.T) {
		// Create a comprehensive usecase design package
		usecaseDesignPkg := usecases.DesignPackage{
			ID:       designPkgID,
			LegacyId: &legacyId,
			Tagged: usecases.Tagged{
				ColorScheme: usecases.Neutral,
				Style:       usecases.MidCentury,
			},
			Name:            "Comprehensive Usecase Design Package",
			ImageURL:        mustParseURL("https://example.com/usecase-image.jpg"),
			Description:     "A comprehensive usecase design package",
			Inspiration:     "Mid-century modern inspiration",
			Atmosphere:      []string{"retro", "sleek", "minimalist"},
			ColorPalette:    []string{"orange", "teal", "walnut"},
			MaterialPalette: []string{"teak", "chrome", "ceramic"},
			HighlightedBrandUrls: []url.URL{
				mustParseURL("https://example.com/usecase-brand1"),
				mustParseURL("https://example.com/usecase-brand2"),
			},
			FixedProductSelections: usecases.FixedProductSelections{
				Paint:           &paintId,
				Mirror:          &mirrorId,
				Toilet:          &toiletId,
				Shelving:        &shelvesId,
				Lighting:        &lightingId,
				FloorTile:       &floorTileId,
				ShowerFloorTile: &showerFloorTileId,
				ShowerWallTile:  &showerWallTileId,
				TubFiller:       &tubFillerId,
				WallTile:        &wallTileId,
				Wallpaper:       &wallpaperId,
			},
			ProductSelectionOptions: usecases.ProductSelectionOptions{
				AlcoveTub:          alcoveTubId,
				FreestandingTub:    freestandingTubId,
				ShowerGlassFixed:   showerGlassFixedId,
				ShowerGlassSliding: showerGlassSlidingId,
				ShowerSystemCombo:  showerSystemFullId,
				ShowerSystemSolo:   showerSystemShowerId,
				TubDoorFixed:       tubDoorFixedId,
				TubDoorSliding:     tubDoorSlidingId,
			},
			DesignPackageProvenance: usecases.DesignPackageProvenance{
				PlumbingBrand: stringPtr("Modern Plumbing"),
				LightingBrand: stringPtr("Retro Lighting"),
				VanityBrand:   stringPtr("Mid-Century Vanities"),
				ToiletBrand:   stringPtr("Designer Toilets"),
				VanityStorage: stringPtr("wall-mounted with hidden storage"),
			},
			WallTilePlacement:  usecases.FullWall,
			WallpaperPlacement: usecases.AllWalls,
			VanityScalingOptions: map[int]usecases.VanityScalingOption{
				24: {VanityProductID: vanityId1, FaucetProductID: faucetId1},
				36: {VanityProductID: vanityId2, FaucetProductID: faucetId2},
			},
		}

		adapterDesignPkg := adapters.FromUsecaseDesignPackage(usecaseDesignPkg, true)

		// Verify basic fields
		assert.Equal(t, legacyId, adapterDesignPkg.ID) // Should use legacy ID as primary ID
		// Note: FromUsecaseDesignPackage doesn't set LegacyId field in adapter design package
		assert.Equal(t, usecases.Neutral, adapterDesignPkg.ColorScheme)
		assert.Equal(t, usecases.MidCentury, adapterDesignPkg.Style)
		assert.Equal(t, "Comprehensive Usecase Design Package", adapterDesignPkg.Name)
		assert.Equal(t, "https://example.com/usecase-image.jpg", adapterDesignPkg.ImageURL)
		assert.Equal(t, "A comprehensive usecase design package", adapterDesignPkg.Description)
		assert.Equal(t, "Mid-century modern inspiration", adapterDesignPkg.Inspiration)

		// Verify string conversions
		assert.Equal(t, "retro, sleek, minimalist", adapterDesignPkg.Atmosphere)
		assert.Equal(t, "orange, teal, walnut", adapterDesignPkg.ColorPalette)
		assert.Equal(t, "teak, chrome, ceramic", adapterDesignPkg.MaterialPalette)

		// Verify highlighted brand URLs
		require.Len(t, adapterDesignPkg.HighlightedBrandUrls, 2)
		assert.Equal(t, "https://example.com/usecase-brand1", adapterDesignPkg.HighlightedBrandUrls[0])
		assert.Equal(t, "https://example.com/usecase-brand2", adapterDesignPkg.HighlightedBrandUrls[1])

		// Verify brand fields
		assert.Equal(t, "Modern Plumbing", adapterDesignPkg.PlumbingBrand)
		assert.Equal(t, "Retro Lighting", adapterDesignPkg.LightingBrand)
		assert.Equal(t, "Mid-Century Vanities", adapterDesignPkg.VanityBrand)
		assert.Equal(t, "Designer Toilets", adapterDesignPkg.ToiletBrand)
		assert.Equal(t, "wall-mounted with hidden storage", adapterDesignPkg.VanityStorage)

		// Verify materials structure
		assert.Equal(t, legacyId, adapterDesignPkg.Materials.ID)
		assert.Equal(t, int64(33), adapterDesignPkg.Materials.Tags) // MidCentury (1) + Neutral (32)
		assert.Equal(t, paintId, adapterDesignPkg.Materials.Paint)
		assert.Equal(t, mirrorId, adapterDesignPkg.Materials.Mirror)
		assert.Equal(t, toiletId, adapterDesignPkg.Materials.Toilet)
		assert.Equal(t, shelvesId, adapterDesignPkg.Materials.Shelves)
		assert.Equal(t, lightingId, adapterDesignPkg.Materials.Lighting)
		assert.Equal(t, floorTileId, adapterDesignPkg.Materials.FloorTile)
		assert.Equal(t, tubFillerId, adapterDesignPkg.Materials.TubFiller)
		assert.Equal(t, showerWallTileId, adapterDesignPkg.Materials.ShowerWallTile)
		assert.Equal(t, showerFloorTileId, adapterDesignPkg.Materials.ShowerFloorTile)
		assert.Equal(t, alcoveTubId, adapterDesignPkg.Materials.AlcoveTub)
		assert.Equal(t, freestandingTubId, adapterDesignPkg.Materials.FreestandingTub)
		assert.Equal(t, showerGlassFixedId, adapterDesignPkg.Materials.ShowerGlassFixed)
		assert.Equal(t, showerGlassSlidingId, adapterDesignPkg.Materials.ShowerGlassSliding)
		assert.Equal(t, showerSystemFullId, adapterDesignPkg.Materials.ShowerSystemFull)
		assert.Equal(t, showerSystemShowerId, adapterDesignPkg.Materials.ShowerSystemShower)
		assert.Equal(t, tubDoorFixedId, adapterDesignPkg.Materials.TubDoorFixed)
		assert.Equal(t, tubDoorSlidingId, adapterDesignPkg.Materials.TubDoorSliding)
		assert.Equal(t, usecases.FullWall, adapterDesignPkg.Materials.WallTilePlacement)
		assert.Equal(t, wallTileId, *adapterDesignPkg.Materials.WallTile)
		assert.Equal(t, usecases.AllWalls, adapterDesignPkg.Materials.WallpaperPlacement)
		assert.Equal(t, &wallpaperId, adapterDesignPkg.Materials.Wallpaper)

		// Verify vanity/faucet dicts
		require.Len(t, adapterDesignPkg.Materials.VanityDict, 2)
		require.Len(t, adapterDesignPkg.Materials.FaucetDict, 2)
		assert.Equal(t, vanityId1, adapterDesignPkg.Materials.VanityDict["24"])
		assert.Equal(t, faucetId1, adapterDesignPkg.Materials.FaucetDict["24"])
		assert.Equal(t, vanityId2, adapterDesignPkg.Materials.VanityDict["36"])
		assert.Equal(t, faucetId2, adapterDesignPkg.Materials.FaucetDict["36"])
	})
}

func TestDesignPackageConversions_EdgeCases(t *testing.T) {
	id := uuid.New()
	v1, f1 := uuid.New(), uuid.New()

	// Empty strings
	designPkg := adapters.DesignPackage{ID: id.String(), Atmosphere: "", Materials: adapters.Materials{VanityDict: map[string]uuid.UUID{}, FaucetDict: map[string]uuid.UUID{}}}
	result, err := adapters.ToUsecaseDesignPackage(designPkg)
	require.NoError(t, err)
	assert.Empty(t, result.Atmosphere)

	// Single item
	designPkg.Atmosphere = "cozy"
	result, err = adapters.ToUsecaseDesignPackage(designPkg)
	require.NoError(t, err)
	assert.Equal(t, []string{"cozy"}, result.Atmosphere)

	// Invalid vanity keys
	designPkg.Materials.VanityDict = map[string]uuid.UUID{"24": v1, "invalid": uuid.New()}
	designPkg.Materials.FaucetDict = map[string]uuid.UUID{"24": f1, "invalid": uuid.New()}
	result, err = adapters.ToUsecaseDesignPackage(designPkg)
	require.NoError(t, err)
	assert.Len(t, result.VanityScalingOptions, 1)
	assert.Equal(t, v1, result.VanityScalingOptions[24].VanityProductID)

	// Invalid URL
	designPkg.ImageURL = "://invalid"
	_, err = adapters.ToUsecaseDesignPackage(designPkg)
	assert.Equal(t, usecases.ErrInvalidPayload, err)

	// Invalid brand URLs (filters out bad ones)
	designPkg.ImageURL = "https://valid.com"
	designPkg.HighlightedBrandUrls = []string{"https://good.com", "://bad", "https://also-good.com"}
	result, err = adapters.ToUsecaseDesignPackage(designPkg)
	require.NoError(t, err)
	assert.Len(t, result.HighlightedBrandUrls, 2)
}

func TestDesignPackageConversions_EnumValues(t *testing.T) {
	id := uuid.New()
	colorSchemes := []usecases.ColorScheme{usecases.Neutral, usecases.Bold}
	styles := []usecases.Style{usecases.Traditional, usecases.Modern}

	for _, cs := range colorSchemes {
		for _, style := range styles {
			designPkg := adapters.DesignPackage{
				ID: id.String(), ImageURL: "https://test.com",
				Tagged:    usecases.Tagged{ColorScheme: cs, Style: style},
				Materials: adapters.Materials{VanityDict: map[string]uuid.UUID{}, FaucetDict: map[string]uuid.UUID{}},
			}

			result, err := adapters.ToUsecaseDesignPackage(designPkg)
			require.NoError(t, err)
			assert.Equal(t, cs, result.ColorScheme)
			assert.Equal(t, style, result.Style)

			// Round-trip
			back := adapters.FromUsecaseDesignPackage(result, false)
			assert.Equal(t, cs, back.ColorScheme)
			assert.Equal(t, style, back.Style)
		}
	}
}

func mustParseURL(urlStr string) url.URL {
	u, err := url.Parse(urlStr)
	if err != nil {
		panic(err)
	}
	return *u
}

func stringPtr(s string) *string {
	return &s
}

func TestDesignPackageConversions_TagsAndLegacyID(t *testing.T) {
	id := uuid.New()

	// Legacy ID preference
	legacyId := "XY"
	usecase := usecases.DesignPackage{
		ID: id, LegacyId: &legacyId, ImageURL: mustParseURL("https://test.com"),
		Tagged:               usecases.Tagged{ColorScheme: usecases.Bold, Style: usecases.Modern},
		VanityScalingOptions: map[int]usecases.VanityScalingOption{},
	}
	adapter := adapters.FromUsecaseDesignPackage(usecase, true)
	assert.Equal(t, legacyId, adapter.ID)
	assert.Equal(t, legacyId, adapter.Materials.ID)

	// UUID fallback
	usecase.LegacyId = nil
	adapter = adapters.FromUsecaseDesignPackage(usecase, false)
	assert.Equal(t, id.String(), adapter.ID)

	// Tag calculations
	tests := []struct {
		cs    usecases.ColorScheme
		style usecases.Style
		tag   int64
	}{
		{usecases.Neutral, usecases.MidCentury, 33}, {usecases.Bold, usecases.Modern, 20},
	}
	for _, tt := range tests {
		usecase.Tagged = usecases.Tagged{ColorScheme: tt.cs, Style: tt.style}
		adapter = adapters.FromUsecaseDesignPackage(usecase, false)
		assert.Equal(t, tt.tag, adapter.Materials.Tags)
	}
}

// TestDesignPackageConversions_NilPointerHandling tests nil pointer handling
func TestDesignPackageConversions_NilPointerHandling(t *testing.T) {
	designPkgID := uuid.New()

	t.Run("should handle nil pointers in FromUsecaseDesignPackage", func(t *testing.T) {
		usecaseDesignPkg := usecases.DesignPackage{
			ID:       designPkgID,
			Name:     "Nil Pointers Design Package",
			ImageURL: mustParseURL("https://example.com/test.jpg"),
			Tagged: usecases.Tagged{
				ColorScheme: usecases.Neutral,
				Style:       usecases.Modern,
			},
			FixedProductSelections: usecases.FixedProductSelections{
				Paint:           nil,
				Mirror:          nil,
				Toilet:          nil,
				Shelving:        nil,
				Lighting:        nil,
				FloorTile:       nil,
				ShowerFloorTile: nil,
				ShowerWallTile:  nil,
				TubFiller:       nil,
				WallTile:        nil,
				Wallpaper:       nil,
			},
			DesignPackageProvenance: usecases.DesignPackageProvenance{
				PlumbingBrand: nil,
				LightingBrand: nil,
				VanityBrand:   nil,
				ToiletBrand:   nil,
				VanityStorage: nil,
			},
			VanityScalingOptions: map[int]usecases.VanityScalingOption{},
		}

		adapterDesignPkg := adapters.FromUsecaseDesignPackage(usecaseDesignPkg, false)

		// Should convert nil pointers to zero values
		assert.Equal(t, uuid.Nil, adapterDesignPkg.Materials.Paint)
		assert.Equal(t, uuid.Nil, adapterDesignPkg.Materials.Mirror)
		assert.Equal(t, uuid.Nil, adapterDesignPkg.Materials.Toilet)
		assert.Equal(t, uuid.Nil, adapterDesignPkg.Materials.Shelves)
		assert.Equal(t, uuid.Nil, adapterDesignPkg.Materials.Lighting)
		assert.Equal(t, uuid.Nil, adapterDesignPkg.Materials.FloorTile)
		assert.Equal(t, uuid.Nil, adapterDesignPkg.Materials.ShowerFloorTile)
		assert.Equal(t, uuid.Nil, adapterDesignPkg.Materials.ShowerWallTile)
		assert.Equal(t, uuid.Nil, adapterDesignPkg.Materials.TubFiller)
		assert.Nil(t, adapterDesignPkg.Materials.WallTile)
		assert.Nil(t, adapterDesignPkg.Materials.Wallpaper)

		// Should convert nil string pointers to empty strings
		assert.Equal(t, "", adapterDesignPkg.PlumbingBrand)
		assert.Equal(t, "", adapterDesignPkg.LightingBrand)
		assert.Equal(t, "", adapterDesignPkg.VanityBrand)
		assert.Equal(t, "", adapterDesignPkg.ToiletBrand)
		assert.Equal(t, "", adapterDesignPkg.VanityStorage)
	})
}
