package adapters

import (
	"encoding/json"
	"strings"
	"testing"

	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"gitlab.com/arc-studio-ai/services/room-design/entities"
)

func TestRoomLayoutJSONMarshaling(t *testing.T) {
	// Test with a compact but representative JSON structure
	jsonData := `{
		"areas": {
			"showers": [{
				"identifier": "7d959d9c-415b-489e-8011-2e364bd5309f",
				"parentIdentifier": "397EF72C-53A3-4F0A-BC8A-E641E3FF691D",
				"curbHeight": 0.1,
				"shape": [{"x": 0, "y": 0}, {"x": 1.5, "y": 1.2}]
			}]
		},
		"doors": [{
			"identifier": "C5050BF6-498E-4A71-A202-A893F72DC735",
			"swing": "In",
			"type": "SingleSwingDoor",
			"shortName": "D1"
		}],
		"tubs": [{
			"identifier": "397EF72C-53A3-4F0A-BC8A-E641E3FF691D",
			"type": "Alcove"
		}],
		"walls": [{
			"identifier": "8087FDE7-4B16-459E-9CA8-3E480D8DAFA3",
			"shortName": "A",
			"thickness": 0.15
		}],
		"measurements": {
			"ceilingArea": 25.0,
			"vanityLength": 1.8
		}
	}`

	var roomLayout RoomLayout
	err := json.Unmarshal([]byte(jsonData), &roomLayout)
	require.NoError(t, err)

	// Verify structure
	assert.Len(t, roomLayout.Areas.Showers, 1)
	assert.Len(t, roomLayout.Doors, 1)
	assert.Len(t, roomLayout.Tubs, 1)
	assert.Len(t, roomLayout.Walls, 1)

	// Verify key fields
	shower := roomLayout.Areas.Showers[0]
	assert.Equal(t, "7d959d9c-415b-489e-8011-2e364bd5309f", shower.Identifier)
	assert.Equal(t, 0.1, shower.CurbHeight)
	assert.Len(t, shower.Shape, 2)

	// Test round-trip marshaling
	marshaledData, err := json.Marshal(roomLayout)
	require.NoError(t, err)

	var roomLayout2 RoomLayout
	err = json.Unmarshal(marshaledData, &roomLayout2)
	require.NoError(t, err)
	assert.Equal(t, roomLayout.Areas.Showers[0].Identifier, roomLayout2.Areas.Showers[0].Identifier)
}

func TestRoomLayoutToEntity(t *testing.T) {
	roomLayout := RoomLayout{
		Floors: []Floor{{
			ShapedElement: ShapedElement{
				BaseElement: BaseElement{Identifier: "EEFDD188-4481-41B3-8B82-213A0D6BEC4B"},
			},
		}},
		Toilets: []Toilet{{
			ScalableElement: ScalableElement{
				BaseElement: BaseElement{Identifier: "0EE45E00-0FBC-4A19-A50A-579E32FF91BF"},
			},
			Type: "Freestanding",
		}},
		Measurements: &Measurements{CeilingArea: floatPtr(25.0)},
	}

	entity := roomLayout.ToEntity()

	assert.NotZero(t, entity.Hash)
	assert.NotEmpty(t, entity.RawData)
	assert.Equal(t, 25.0, entity.AreaSqFt)
	assert.Len(t, entity.FloorIds, 1)
	assert.Len(t, entity.ToiletIds, 1)
	assert.Equal(t, "eefdd188-4481-41b3-8b82-213a0d6bec4b", entity.FloorIds[0].String())
	assert.Equal(t, "0ee45e00-0fbc-4a19-a50a-579e32ff91bf", entity.ToiletIds[0].String())
}

func TestWallNicheRelationships(t *testing.T) {
	wallId := "8087FDE7-4B16-459E-9CA8-3E480D8DAFA3"
	roomLayout := RoomLayout{
		Walls: []Wall{{
			ShapedElement: ShapedElement{
				BaseElement: BaseElement{Identifier: wallId},
			},
			Thickness: 0.15,
		}},
		Niches: []Niche{
			{ScalableElement: ScalableElement{
				BaseElement: BaseElement{
					Identifier:       "11111111-1111-1111-1111-111111111111",
					ParentIdentifier: &wallId,
				},
			}},
			{ScalableElement: ScalableElement{
				BaseElement: BaseElement{
					Identifier:       "*************-2222-2222-************",
					ParentIdentifier: &wallId,
				},
			}},
		},
	}

	entity := roomLayout.ToEntity()

	assert.Len(t, entity.Walls, 1)
	wall := entity.Walls[0]
	assert.Equal(t, strings.ToLower(wallId), wall.LayoutId.String())
	assert.Len(t, wall.NicheIds, 2)
}

func TestWetAreaCreation(t *testing.T) {
	showerId := "7d959d9c-415b-489e-8011-2e364bd5309f"
	tubId := "397EF72C-53A3-4F0A-BC8A-E641E3FF691D"

	roomLayout := RoomLayout{
		Areas: Areas{
			Showers: []Shower{{
				BaseElement: BaseElement{
					Identifier:       showerId,
					ParentIdentifier: &tubId, // Shower is part of tub
				},
				CurbHeight: 0.1,
			}},
		},
		Tubs: []Tub{
			{
				ScalableElement: ScalableElement{
					BaseElement: BaseElement{Identifier: tubId},
				},
				Type: "Alcove",
			},
			{
				ScalableElement: ScalableElement{
					BaseElement: BaseElement{Identifier: "*************-3333-3333-************"},
				},
				Type: "Freestanding",
			},
		},
		ShowerSystems: []ShowerSystem{{
			ScalableElement: ScalableElement{
				BaseElement: BaseElement{Identifier: "*************-4444-4444-************"},
			},
			ShowerAreaIdentifier: showerId,
			Type:                 "TubAndShowerFaucet",
		}},
		Measurements: &Measurements{TubLength: floatPtr(1.5)},
	}

	entity := roomLayout.ToEntity()

	// Should have 2 wet areas: shower+tub combo, freestanding tub
	assert.Len(t, entity.WetAreas, 2)

	// Find wet areas by layout ID
	var showerWetArea, freestandingTubWetArea *entities.WetArea
	for i := range entity.WetAreas {
		if entity.WetAreas[i].LayoutId.String() == strings.ToLower(showerId) {
			showerWetArea = &entity.WetAreas[i]
		} else if entity.WetAreas[i].LayoutId.String() == "*************-3333-3333-************" {
			freestandingTubWetArea = &entity.WetAreas[i]
		}
	}

	// Verify shower+tub combo
	require.NotNil(t, showerWetArea)
	assert.Len(t, showerWetArea.AlcoveTubs, 1)
	assert.Equal(t, strings.ToLower(tubId), showerWetArea.AlcoveTubs[0].LayoutId.String())
	assert.Equal(t, 1.5, *showerWetArea.MaxTubLength)

	// Verify freestanding tub
	require.NotNil(t, freestandingTubWetArea)
	assert.Len(t, freestandingTubWetArea.FreestandingTubIds, 1)
}

func TestVanityConversion(t *testing.T) {
	roomLayout := RoomLayout{
		Vanities: []Vanity{{
			ScalableElement: ScalableElement{
				BaseElement: BaseElement{Identifier: "11111111-1111-1111-1111-111111111111"},
			},
			Type: "Floating",
		}},
		Measurements: &Measurements{VanityLength: floatPtr(2.0)},
	}

	entity := roomLayout.ToEntity()

	assert.Len(t, entity.Vanities, 1)
	assert.Equal(t, 2.0, *entity.Vanities[0].MaxLength)
}

func TestInvalidUUIDs(t *testing.T) {
	roomLayout := RoomLayout{
		Floors: []Floor{
			{ShapedElement: ShapedElement{BaseElement: BaseElement{Identifier: "invalid-uuid"}}},
			{ShapedElement: ShapedElement{BaseElement: BaseElement{Identifier: "EEFDD188-4481-41B3-8B82-213A0D6BEC4B"}}},
		},
		Toilets: []Toilet{{
			ScalableElement: ScalableElement{BaseElement: BaseElement{Identifier: "not-a-uuid"}},
		}},
	}

	entity := roomLayout.ToEntity()

	// Only valid UUIDs should be included
	assert.Len(t, entity.FloorIds, 1)
	assert.Equal(t, "eefdd188-4481-41b3-8b82-213a0d6bec4b", entity.FloorIds[0].String())
	assert.Empty(t, entity.ToiletIds)
}

func TestFromRoomLayoutEntity(t *testing.T) {
	originalLayout := RoomLayout{
		Areas: Areas{
			Showers: []Shower{{
				BaseElement: BaseElement{Identifier: "7d959d9c-415b-489e-8011-2e364bd5309f"},
				CurbHeight:  0.5,
			}},
		},
	}

	entity := originalLayout.ToEntity()
	convertedLayout := FromRoomLayoutEntity(entity)

	assert.Len(t, convertedLayout.Areas.Showers, 1)
	assert.Equal(t, originalLayout.Areas.Showers[0].Identifier, convertedLayout.Areas.Showers[0].Identifier)
	assert.Equal(t, originalLayout.Areas.Showers[0].CurbHeight, convertedLayout.Areas.Showers[0].CurbHeight)
}

func TestRoundTripConversion(t *testing.T) {
	originalLayout := RoomLayout{
		Areas: Areas{
			Showers: []Shower{{
				BaseElement: BaseElement{
					Identifier: "shower-1",
					Position:   Position{X: 1.0, Y: 2.0, Z: 3.0},
					ShortName:  stringPtr("Main Shower"),
				},
				CurbHeight: 0.1,
				Shape:      []Point2D{{X: 0, Y: 0}, {X: 1.5, Y: 1.2}},
			}},
		},
		Doors: []Door{{
			ScalableElement: ScalableElement{
				BaseElement: BaseElement{Identifier: "door-1"},
			},
			Swing: "In",
			Type:  "SingleSwingDoor",
		}},
		Measurements: &Measurements{CeilingArea: floatPtr(30.0)},
	}

	entity := originalLayout.ToEntity()
	require.NotEmpty(t, entity.RawData)

	convertedLayout := FromRoomLayoutEntity(entity)

	// Verify preservation of key data
	assert.Len(t, convertedLayout.Areas.Showers, 1)
	shower := convertedLayout.Areas.Showers[0]
	assert.Equal(t, "shower-1", shower.Identifier)
	assert.Equal(t, 1.0, shower.Position.X)
	assert.Equal(t, 0.1, shower.CurbHeight)

	assert.Len(t, convertedLayout.Doors, 1)
	assert.Equal(t, "door-1", convertedLayout.Doors[0].Identifier)
	assert.Equal(t, 30.0, *convertedLayout.Measurements.CeilingArea)
}

func TestFallbackConversion(t *testing.T) {
	entity := entities.RoomLayout{
		RawData:   []byte(`{"invalid": "json"`), // Invalid JSON
		AreaSqFt:  25.0,
		FloorIds:  []uuid.UUID{uuid.MustParse("EEFDD188-4481-41B3-8B82-213A0D6BEC4B")},
		ToiletIds: []uuid.UUID{uuid.MustParse("0EE45E00-0FBC-4A19-A50A-579E32FF91BF")},
		Walls: []entities.Wall{{
			LayoutId: uuid.MustParse("8087FDE7-4B16-459E-9CA8-3E480D8DAFA3"),
			NicheIds: []uuid.UUID{uuid.MustParse("11111111-1111-1111-1111-111111111111")},
		}},
		Vanities: []entities.Vanity{{
			LayoutId:  uuid.MustParse("*************-2222-2222-************"),
			MaxLength: floatPtr(1.5),
		}},
		WetAreas: []entities.WetArea{
			{
				LayoutId:             uuid.MustParse("*************-3333-3333-************"),
				GlassType:            entities.FixedShowerEnclosure,
				ShowerIds:            []uuid.UUID{uuid.MustParse("*************-4444-4444-************")},
				MaxShowerGlassLength: floatPtr(1.8),
			},
			{
				LayoutId: uuid.MustParse("*************-5555-5555-************"),
				AlcoveTubs: []entities.AlcoveTub{{
					LayoutId: uuid.MustParse("*************-6666-6666-************"),
					ShowerId: &[]uuid.UUID{uuid.MustParse("*************-7777-7777-************")}[0],
				}},
				MaxTubLength: floatPtr(1.6),
			},
			{
				LayoutId:           uuid.MustParse("*************-8888-8888-************"),
				FreestandingTubIds: []uuid.UUID{uuid.MustParse("*************-9999-9999-************")},
			},
		},
	}

	convertedLayout := FromRoomLayoutEntity(entity)

	// Verify fallback structure is created
	assert.Len(t, convertedLayout.Floors, 1)
	assert.Equal(t, "eefdd188-4481-41b3-8b82-213a0d6bec4b", convertedLayout.Floors[0].Identifier)
	assert.Len(t, convertedLayout.Toilets, 1)
	assert.Len(t, convertedLayout.Walls, 1)
	assert.Len(t, convertedLayout.Niches, 1)
	assert.Len(t, convertedLayout.Vanities, 1)
	assert.Equal(t, 25.0, *convertedLayout.Measurements.CeilingArea)

	// Verify wet area fallback conversion
	assert.Len(t, convertedLayout.Areas.Showers, 2) // One standalone shower + one from alcove tub
	assert.Len(t, convertedLayout.Tubs, 2)          // One alcove + one freestanding
	assert.Len(t, convertedLayout.ShowerSystems, 2) // One for standalone + one for alcove

	// Check shower areas
	showerIds := []string{convertedLayout.Areas.Showers[0].Identifier, convertedLayout.Areas.Showers[1].Identifier}
	assert.Contains(t, showerIds, "*************-3333-3333-************") // Standalone shower
	assert.Contains(t, showerIds, "*************-5555-5555-************") // Alcove shower area

	// Check tubs
	tubIds := []string{convertedLayout.Tubs[0].Identifier, convertedLayout.Tubs[1].Identifier}
	assert.Contains(t, tubIds, "*************-6666-6666-************") // Alcove tub
	assert.Contains(t, tubIds, "*************-9999-9999-************") // Freestanding tub

	// Check shower systems
	systemIds := []string{convertedLayout.ShowerSystems[0].Identifier, convertedLayout.ShowerSystems[1].Identifier}
	assert.Contains(t, systemIds, "*************-4444-4444-************") // Standalone shower system
	assert.Contains(t, systemIds, "*************-7777-7777-************") // Alcove shower system
}

func TestWetAreaFallbackConversion(t *testing.T) {
	t.Run("standalone shower wet area", func(t *testing.T) {
		entity := entities.RoomLayout{
			RawData: []byte(`{"invalid": "json"`), // Force fallback
			WetAreas: []entities.WetArea{{
				LayoutId:             uuid.MustParse("11111111-1111-1111-1111-111111111111"),
				GlassType:            entities.SlidingShowerEnclosure,
				ShowerIds:            []uuid.UUID{uuid.MustParse("*************-2222-2222-************")},
				MaxShowerGlassLength: floatPtr(2.0),
			}},
		}

		layout := FromRoomLayoutEntity(entity)

		assert.Len(t, layout.Areas.Showers, 1)
		assert.Len(t, layout.ShowerSystems, 1)
		assert.Empty(t, layout.Tubs)

		shower := layout.Areas.Showers[0]
		assert.Equal(t, "11111111-1111-1111-1111-111111111111", shower.Identifier)

		system := layout.ShowerSystems[0]
		assert.Equal(t, "*************-2222-2222-************", system.Identifier)
		assert.Equal(t, "11111111-1111-1111-1111-111111111111", system.ShowerAreaIdentifier)
	})

	t.Run("alcove tub with shower", func(t *testing.T) {
		entity := entities.RoomLayout{
			RawData: []byte(`{"invalid": "json"`), // Force fallback
			WetAreas: []entities.WetArea{{
				LayoutId: uuid.MustParse("*************-3333-3333-************"),
				AlcoveTubs: []entities.AlcoveTub{{
					LayoutId: uuid.MustParse("*************-4444-4444-************"),
					DoorType: entities.FixedShowerEnclosure,
					ShowerId: &[]uuid.UUID{uuid.MustParse("*************-5555-5555-************")}[0],
				}},
				MaxTubLength: floatPtr(1.7),
			}},
		}

		layout := FromRoomLayoutEntity(entity)

		assert.Len(t, layout.Areas.Showers, 1)
		assert.Len(t, layout.Tubs, 1)
		assert.Len(t, layout.ShowerSystems, 1)

		shower := layout.Areas.Showers[0]
		assert.Equal(t, "*************-3333-3333-************", shower.Identifier)
		assert.Equal(t, "*************-4444-4444-************", *shower.ParentIdentifier)

		tub := layout.Tubs[0]
		assert.Equal(t, "*************-4444-4444-************", tub.Identifier)
		assert.Equal(t, "Alcove", tub.Type)

		system := layout.ShowerSystems[0]
		assert.Equal(t, "*************-5555-5555-************", system.Identifier)
		assert.Equal(t, "*************-3333-3333-************", system.ShowerAreaIdentifier)
	})

	t.Run("freestanding tub only", func(t *testing.T) {
		entity := entities.RoomLayout{
			RawData: []byte(`{"invalid": "json"`), // Force fallback
			WetAreas: []entities.WetArea{{
				LayoutId:           uuid.MustParse("*************-6666-6666-************"),
				FreestandingTubIds: []uuid.UUID{uuid.MustParse("*************-7777-7777-************")},
			}},
		}

		layout := FromRoomLayoutEntity(entity)

		assert.Empty(t, layout.Areas.Showers)
		assert.Len(t, layout.Tubs, 1)
		assert.Empty(t, layout.ShowerSystems)

		tub := layout.Tubs[0]
		assert.Equal(t, "*************-7777-7777-************", tub.Identifier)
		assert.Equal(t, "Freestanding", tub.Type)
	})
}

func TestEdgeCases(t *testing.T) {
	t.Run("empty JSON", func(t *testing.T) {
		var layout RoomLayout
		err := json.Unmarshal([]byte(`{}`), &layout)
		require.NoError(t, err)
		assert.Empty(t, layout.Areas.Showers)
	})

	t.Run("nil vs empty arrays", func(t *testing.T) {
		layout := RoomLayout{Floors: []Floor{}, Doors: nil}
		data, err := json.Marshal(layout)
		require.NoError(t, err)

		var unmarshaled RoomLayout
		err = json.Unmarshal(data, &unmarshaled)
		require.NoError(t, err)
		assert.Empty(t, unmarshaled.Floors)
		assert.Empty(t, unmarshaled.Doors)
	})

	t.Run("invalid JSON", func(t *testing.T) {
		var layout RoomLayout
		err := json.Unmarshal([]byte(`{"invalid": json}`), &layout)
		assert.Error(t, err)
	})

	t.Run("empty measurements", func(t *testing.T) {
		layout := RoomLayout{Measurements: &Measurements{}}
		entity := layout.ToEntity()
		assert.Equal(t, float64(0), entity.AreaSqFt)
	})
}

// Helper functions
func stringPtr(s string) *string {
	return &s
}

func floatPtr(f float64) *float64 {
	return &f
}
