package adapters

type Position struct {
	X float64 `json:"x"`
	Y float64 `json:"y"`
	Z float64 `json:"z"`
}

type Rotation struct {
	X float64 `json:"x"`
	Y float64 `json:"y"`
	Z float64 `json:"z"`
}

type Scale struct {
	X float64 `json:"x"`
	Y float64 `json:"y"`
	Z float64 `json:"z"`
}

type Point2D struct {
	X float64 `json:"x"`
	Y float64 `json:"y"`
}

// BaseElement contains common fields for all room elements
type BaseElement struct {
	Identifier       string   `json:"identifier"`
	ParentIdentifier *string  `json:"parentIdentifier"`
	Position         Position `json:"position"`
	Rotation         Rotation `json:"rotation"`
	ShortName        *string  `json:"shortName"`
}

// ScalableElement extends BaseElement with scale information
type ScalableElement struct {
	BaseElement
	Scale Scale `json:"scale"`
}

// ShapedElement extends BaseElement with shape information
type ShapedElement struct {
	BaseElement
	Shape []Point2D `json:"shape"`
}

// Shower represents a shower area
type Shower struct {
	BaseElement
	CurbHeight    float64   `json:"curbHeight"`
	CurbThickness float64   `json:"curbThickness"`
	Shape         []Point2D `json:"shape"`
}

type Areas struct {
	Showers []Shower `json:"showers"`
}

type Ceiling struct {
	ShapedElement
}

type Door struct {
	ScalableElement
	Swing string `json:"swing"`
	Type  string `json:"type"`
}

type Floor struct {
	ShapedElement
}

// Light represents a light fixture
type Light struct {
	ScalableElement
}

type Niche struct {
	ScalableElement
}

type ShowerSystem struct {
	ScalableElement
	ShowerAreaIdentifier string `json:"showerAreaIdentifier"`
	Type                 string `json:"type"`
}

type Toilet struct {
	ScalableElement
	Type string `json:"type"`
}

type Tub struct {
	ScalableElement
	Type string `json:"type"`
}

type Vanity struct {
	ScalableElement
	Type string `json:"type"`
}

type Wall struct {
	ShapedElement
	Thickness float64 `json:"thickness"`
}

type Measurements struct {
	CeilingArea                 *float64 `json:"ceilingArea"`
	CurbArea                    *float64 `json:"curbArea"`
	FloorArea                   *float64 `json:"floorArea"`
	HalfWallTileLength          *float64 `json:"halfWallTileLength"`
	LinearLengthOfWall          *float64 `json:"linearLengthOfWall"`
	NichesArea                  *float64 `json:"nichesArea"`
	ShowerAreaHeight            *float64 `json:"showerAreaHeight"`
	ShowerFloorArea             *float64 `json:"showerFloorArea"`
	ShowerLongestOpenSideLength *float64 `json:"showerLongestOpenSideLength"`
	ShowerOpenSides             *int     `json:"showerOpenSides"`
	ShowerWallArea              *float64 `json:"showerWallArea"`
	TotalShowerCurbLength       *float64 `json:"totalShowerCurbLength"`
	TubLength                   *float64 `json:"tubLength"`
	VanityHalfWallArea          *float64 `json:"vanityHalfWallArea"`
	VanityLength                *float64 `json:"vanityLength"`
	VanityWallArea              *float64 `json:"vanityWallArea"`
	VanityWallLength            *float64 `json:"vanityWallLength"`
	WallHalfArea                *float64 `json:"wallHalfArea"`
	WallPaintArea               *float64 `json:"wallPaintArea"`
}

type RoomLayout struct {
	Areas         Areas          `json:"areas"`
	Ceilings      []Ceiling      `json:"ceilings"`
	Doors         []Door         `json:"doors"`
	Floors        []Floor        `json:"floors"`
	Lights        []Light        `json:"lights"`
	LinenCabinets []BaseElement  `json:"linenCabinets"`
	Mirrors       []BaseElement  `json:"mirrors"`
	Niches        []Niche        `json:"niches"`
	Openings      []BaseElement  `json:"openings"`
	RobeHooks     []BaseElement  `json:"robeHooks"`
	Shelves       []BaseElement  `json:"shelves"`
	ShowerSystems []ShowerSystem `json:"showerSystems"`
	Sinks         []BaseElement  `json:"sinks"`
	Toilets       []Toilet       `json:"toilets"`
	TowelBars     []BaseElement  `json:"towelBars"`
	TowelRings    []BaseElement  `json:"towelRings"`
	TpHolders     []BaseElement  `json:"tpHolders"`
	TubFillers    []BaseElement  `json:"tubFillers"`
	Tubs          []Tub          `json:"tubs"`
	Vanities      []Vanity       `json:"vanities"`
	Walls         []Wall         `json:"walls"`
	Windows       []BaseElement  `json:"windows"`
	Measurements  *Measurements  `json:"measurements,omitempty"`
}
